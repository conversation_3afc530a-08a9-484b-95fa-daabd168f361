/**
 * Styles pour l'interface utilisateur améliorée SmartSEO AI
 */

/* Variables CSS */
:root {
    --smartseo-primary: #0073aa;
    --smartseo-success: #46b450;
    --smartseo-warning: #ffb900;
    --smartseo-error: #dc3232;
    --smartseo-gray-light: #f1f1f1;
    --smartseo-gray-medium: #ddd;
    --smartseo-gray-dark: #666;
    --smartseo-border-radius: 8px;
    --smartseo-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --smartseo-transition: all 0.3s ease;
}

/* Conteneur principal du tableau de bord */
.smartseo-ai-enhanced-dashboard {
    opacity: 0;
    transform: translateY(20px);
    transition: var(--smartseo-transition);
}

.smartseo-ai-enhanced-dashboard.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Indicateurs de santé */
.smartseo-health-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.health-card {
    background: #fff;
    border: 1px solid var(--smartseo-gray-medium);
    border-radius: var(--smartseo-border-radius);
    padding: 20px;
    box-shadow: var(--smartseo-shadow);
    transition: var(--smartseo-transition);
}

.health-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.health-card h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--smartseo-gray-dark);
}

.health-status {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

/* Indicateurs de statut */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.healthy {
    background-color: var(--smartseo-success);
    box-shadow: 0 0 0 3px rgba(70, 180, 80, 0.2);
}

.status-indicator.warning {
    background-color: var(--smartseo-warning);
    box-shadow: 0 0 0 3px rgba(255, 185, 0, 0.2);
}

.status-indicator.error {
    background-color: var(--smartseo-error);
    box-shadow: 0 0 0 3px rgba(220, 50, 50, 0.2);
}

.status-indicator.processing {
    background-color: var(--smartseo-primary);
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Cartes de santé avec couleurs */
.health-card.healthy {
    border-left: 4px solid var(--smartseo-success);
}

.health-card.warning {
    border-left: 4px solid var(--smartseo-warning);
}

.health-card.error {
    border-left: 4px solid var(--smartseo-error);
}

.health-card.processing {
    border-left: 4px solid var(--smartseo-primary);
}

/* Conteneur des graphiques */
.smartseo-charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    background: #fff;
    border: 1px solid var(--smartseo-gray-medium);
    border-radius: var(--smartseo-border-radius);
    padding: 20px;
    box-shadow: var(--smartseo-shadow);
}

.chart-card h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--smartseo-gray-dark);
    text-align: center;
}

.chart-card canvas {
    max-height: 300px;
}

/* Actions rapides */
.smartseo-quick-actions {
    background: #fff;
    border: 1px solid var(--smartseo-gray-medium);
    border-radius: var(--smartseo-border-radius);
    padding: 20px;
    box-shadow: var(--smartseo-shadow);
}

.smartseo-quick-actions h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--smartseo-gray-dark);
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-buttons .button {
    transition: var(--smartseo-transition);
}

.action-buttons .button:hover {
    transform: translateY(-1px);
}

/* Notifications toast */
#smartseo-toast-container {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 999999;
    max-width: 400px;
}

.smartseo-toast {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: var(--smartseo-border-radius);
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid var(--smartseo-gray-medium);
    opacity: 0;
    transform: translateX(100%);
    transition: var(--smartseo-transition);
}

.smartseo-toast.show {
    opacity: 1;
    transform: translateX(0);
}

.smartseo-toast.toast-success {
    border-left-color: var(--smartseo-success);
}

.smartseo-toast.toast-warning {
    border-left-color: var(--smartseo-warning);
}

.smartseo-toast.toast-error {
    border-left-color: var(--smartseo-error);
}

.toast-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    border-radius: 50%;
}

.toast-success .toast-icon {
    background-color: var(--smartseo-success);
}

.toast-warning .toast-icon {
    background-color: var(--smartseo-warning);
}

.toast-error .toast-icon {
    background-color: var(--smartseo-error);
}

.toast-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--smartseo-gray-dark);
    padding: 0;
    margin-left: 10px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-close:hover {
    color: var(--smartseo-error);
}

/* Spinner personnalisé */
.smartseo-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--smartseo-gray-light);
    border-top: 2px solid var(--smartseo-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Optimisation en masse */
.bulk-optimization-controls {
    background: var(--smartseo-gray-light);
    border-radius: var(--smartseo-border-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.bulk-optimization-controls h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
}

.bulk-progress {
    background: #fff;
    border-radius: var(--smartseo-border-radius);
    padding: 10px;
    margin-top: 15px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: var(--smartseo-gray-light);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--smartseo-primary), var(--smartseo-success));
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    color: var(--smartseo-gray-dark);
}

/* Responsive */
@media (max-width: 768px) {
    .smartseo-health-indicators {
        grid-template-columns: 1fr;
    }
    
    .smartseo-charts-container {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .button {
        width: 100%;
        text-align: center;
    }
    
    #smartseo-toast-container {
        left: 20px;
        right: 20px;
        max-width: none;
    }
}

/* Animations d'entrée */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.health-card,
.chart-card,
.smartseo-quick-actions {
    animation: fadeInUp 0.6s ease forwards;
}

.health-card:nth-child(1) { animation-delay: 0.1s; }
.health-card:nth-child(2) { animation-delay: 0.2s; }
.health-card:nth-child(3) { animation-delay: 0.3s; }

/* États de chargement */
.loading-state {
    opacity: 0.6;
    pointer-events: none;
}

.loading-state::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--smartseo-gray-light);
    border-top: 2px solid var(--smartseo-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
