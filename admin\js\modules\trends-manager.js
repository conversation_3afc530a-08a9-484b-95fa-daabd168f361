/**
 * Module de gestion des tendances
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire des tendances
     */
    const TrendsManager = {
        /**
         * Initialise le gestionnaire des tendances
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Récupère les tendances pour un mot-clé
         * @param {string} keyword Mot-clé à analyser
         */
        getTrends: function(keyword) {
            // Vérifier si le mot-clé est fourni
            if (!keyword) {
                window.SmartSEOAI.UIManager.showError(smartseoAiWritingAssistant.i18n.no_keyword);
                return;
            }
            
            // Afficher le chargement
            window.SmartSEOAI.UIManager.showLoading(smartseoAiWritingAssistant.i18n.getting_trends, 'trends');
            
            // Préparer les données
            const data = {
                action: 'smartseo_ai_get_trends',
                nonce: smartseoAiWritingAssistant.nonce,
                keyword: keyword
            };
            
            // Envoyer la requête AJAX
            $.ajax({
                url: smartseoAiWritingAssistant.ajaxUrl,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading('trends');
                    
                    if (response.success) {
                        // Afficher les résultats
                        window.SmartSEOAI.UIManager.showTrendsResults(response.data);
                        
                        // Afficher un message de succès
                        window.SmartSEOAI.UIManager.showSuccess(smartseoAiWritingAssistant.i18n.success);
                    } else {
                        // Afficher un message d'erreur
                        window.SmartSEOAI.UIManager.showError(response.data.message || smartseoAiWritingAssistant.i18n.error);
                    }
                },
                error: function(xhr, status, error) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading('trends');
                    
                    // Afficher un message d'erreur
                    window.SmartSEOAI.UIManager.showError(smartseoAiWritingAssistant.i18n.error + ': ' + error);
                }
            });
        },

        /**
         * Génère du contenu pour un sujet tendance
         * @param {string} trend Sujet tendance
         */
        generateContentForTrend: function(trend) {
            // Mettre à jour le champ de mot-clé
            $('#smartseo-ai-focus-keyword').val(trend);
            
            // Afficher un message
            window.SmartSEOAI.UIManager.showInfo(smartseoAiWritingAssistant.i18n.select_content_type);
            
            // Basculer vers l'onglet de contenu
            if (window.SmartSEOAI.UIManager.isGutenbergEditor()) {
                $('.smartseo-ai-tab-content').click();
            } else {
                $('.smartseo-ai-tab-nav[data-tab="content"]').click();
            }
        },

        /**
         * Recherche des sujets connexes pour un mot-clé
         * @param {string} keyword Mot-clé à analyser
         */
        searchRelatedTopics: function(keyword) {
            // Vérifier si le mot-clé est fourni
            if (!keyword) {
                window.SmartSEOAI.UIManager.showError(smartseoAiWritingAssistant.i18n.no_keyword);
                return;
            }
            
            // Afficher le chargement
            window.SmartSEOAI.UIManager.showLoading(smartseoAiWritingAssistant.i18n.searching_topics);
            
            // Préparer les données
            const data = {
                action: 'smartseo_ai_search_related_topics',
                nonce: smartseoAiWritingAssistant.nonce,
                keyword: keyword
            };
            
            // Envoyer la requête AJAX
            $.ajax({
                url: smartseoAiWritingAssistant.ajaxUrl,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading();
                    
                    if (response.success) {
                        // Créer le contenu de la modale
                        const topics = response.data.topics;
                        let content = '<div class="smartseo-ai-related-topics">';
                        
                        if (topics && topics.length > 0) {
                            content += '<ul>';
                            
                            topics.forEach(topic => {
                                content += '<li>';
                                content += '<div class="smartseo-ai-topic-name">' + topic.name + '</div>';
                                
                                if (topic.volume) {
                                    content += '<div class="smartseo-ai-topic-volume">Volume de recherche : ' + topic.volume + '</div>';
                                }
                                
                                content += '<div class="smartseo-ai-topic-actions">';
                                content += '<button class="button smartseo-ai-use-topic" data-topic="' + topic.name + '">Utiliser comme mot-clé</button>';
                                content += '<button class="button smartseo-ai-generate-for-topic" data-topic="' + topic.name + '">Générer du contenu</button>';
                                content += '</div>';
                                content += '</li>';
                            });
                            
                            content += '</ul>';
                        } else {
                            content += '<p>Aucun sujet connexe trouvé.</p>';
                        }
                        
                        content += '</div>';
                        
                        // Afficher la modale
                        window.SmartSEOAI.ModalUI.showModal(
                            'Sujets connexes pour "' + keyword + '"',
                            content,
                            {
                                width: '600px',
                                buttons: [
                                    {
                                        text: 'Fermer',
                                        primary: true,
                                        click: function() {
                                            window.SmartSEOAI.ModalUI.hideModal();
                                        }
                                    }
                                ],
                                onOpen: function() {
                                    // Ajouter les écouteurs d'événements pour les boutons
                                    $('.smartseo-ai-use-topic').on('click', function() {
                                        const topic = $(this).data('topic');
                                        $('#smartseo-ai-focus-keyword, #smartseo-ai-focus-keyword-analysis, #smartseo-ai-trends-keyword').val(topic);
                                        window.SmartSEOAI.UIManager.showSuccess(smartseoAiWritingAssistant.i18n.keyword_updated);
                                        window.SmartSEOAI.ModalUI.hideModal();
                                    });
                                    
                                    $('.smartseo-ai-generate-for-topic').on('click', function() {
                                        const topic = $(this).data('topic');
                                        TrendsManager.generateContentForTrend(topic);
                                        window.SmartSEOAI.ModalUI.hideModal();
                                    });
                                }
                            }
                        );
                    } else {
                        // Afficher un message d'erreur
                        window.SmartSEOAI.UIManager.showError(response.data.message || smartseoAiWritingAssistant.i18n.error);
                    }
                },
                error: function(xhr, status, error) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading();
                    
                    // Afficher un message d'erreur
                    window.SmartSEOAI.UIManager.showError(smartseoAiWritingAssistant.i18n.error + ': ' + error);
                }
            });
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.TrendsManager = TrendsManager;

})(jQuery);
