<?php
/**
 * Classe pour l'analyse des images
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'analyse des images
 */
class SmartSEO_AI_Image_Analyzer {

    /**
     * Analyse les images du contenu
     *
     * @param string $content Contenu à analyser.
     * @param string $keyword Mot-clé principal.
     * @return array Résultats de l'analyse.
     */
    public function analyze( $content, $keyword = '' ) {
        // Extraire toutes les images
        preg_match_all( '/<img[^>]+>/i', $content, $img_matches );
        
        $images = array();
        $missing_alt = 0;
        $empty_alt = 0;
        $keyword_in_alt = 0;
        $large_images = 0;
        
        foreach ( $img_matches[0] as $img ) {
            // Extraire l'attribut alt
            preg_match( '/alt=([\'"])(.*?)\1/i', $img, $alt_match );
            $alt = isset( $alt_match[2] ) ? $alt_match[2] : '';
            
            // Extraire l'attribut src
            preg_match( '/src=([\'"])(.*?)\1/i', $img, $src_match );
            $src = isset( $src_match[2] ) ? $src_match[2] : '';
            
            // Extraire les attributs width et height
            preg_match( '/width=([\'"])(.*?)\1/i', $img, $width_match );
            $width = isset( $width_match[2] ) ? intval( $width_match[2] ) : 0;
            
            preg_match( '/height=([\'"])(.*?)\1/i', $img, $height_match );
            $height = isset( $height_match[2] ) ? intval( $height_match[2] ) : 0;
            
            // Vérifier si l'attribut alt est manquant
            $has_alt = isset( $alt_match[2] );
            
            // Vérifier si l'attribut alt est vide
            $is_empty_alt = $has_alt && empty( trim( $alt ) );
            
            // Vérifier si le mot-clé est présent dans l'attribut alt
            $has_keyword_in_alt = ! empty( $keyword ) && ! empty( $alt ) && stripos( $alt, $keyword ) !== false;
            
            // Vérifier si l'image est grande (plus de 100 Ko)
            $is_large = false;
            if ( ! empty( $src ) && strpos( $src, 'http' ) === 0 ) {
                // Pour les images externes, on ne peut pas vérifier la taille
                $is_large = false;
            } elseif ( ! empty( $src ) ) {
                // Pour les images locales, on peut essayer de récupérer la taille
                $file_path = $this->get_file_path_from_url( $src );
                if ( $file_path && file_exists( $file_path ) ) {
                    $file_size = filesize( $file_path );
                    $is_large = $file_size > 100 * 1024; // 100 Ko
                }
            }
            
            $images[] = array(
                'src' => $src,
                'alt' => $alt,
                'width' => $width,
                'height' => $height,
                'has_alt' => $has_alt,
                'is_empty_alt' => $is_empty_alt,
                'has_keyword_in_alt' => $has_keyword_in_alt,
                'is_large' => $is_large,
            );
            
            if ( ! $has_alt ) {
                $missing_alt++;
            }
            
            if ( $is_empty_alt ) {
                $empty_alt++;
            }
            
            if ( $has_keyword_in_alt ) {
                $keyword_in_alt++;
            }
            
            if ( $is_large ) {
                $large_images++;
            }
        }
        
        // Analyser les attributs alt
        $alt_analysis = $this->analyze_alt_attributes( $images, $keyword );
        
        // Analyser l'optimisation des images
        $optimization_analysis = $this->analyze_image_optimization( $images );
        
        // Créer la liste des vérifications
        $checks = array(
            array(
                'label' => __( 'Attributs alt', 'smartseo-ai' ),
                'status' => $alt_analysis['status'],
                'recommendation' => $alt_analysis['recommendation'],
            ),
            array(
                'label' => __( 'Optimisation des images', 'smartseo-ai' ),
                'status' => $optimization_analysis['status'],
                'recommendation' => $optimization_analysis['recommendation'],
            ),
        );
        
        // Calculer le score
        $score = $this->calculate_score( $alt_analysis, $optimization_analysis );
        
        // Retourner les résultats
        return array(
            'score' => $score,
            'total_images' => count( $images ),
            'missing_alt' => $missing_alt,
            'empty_alt' => $empty_alt,
            'keyword_in_alt' => $keyword_in_alt,
            'large_images' => $large_images,
            'alt_analysis' => $alt_analysis,
            'optimization_analysis' => $optimization_analysis,
            'checks' => $checks,
        );
    }

    /**
     * Analyse les attributs alt des images
     *
     * @param array  $images  Images à analyser.
     * @param string $keyword Mot-clé principal.
     * @return array Résultats de l'analyse.
     */
    private function analyze_alt_attributes( $images, $keyword ) {
        $total_images = count( $images );
        $missing_alt = 0;
        $empty_alt = 0;
        $keyword_in_alt = 0;
        
        foreach ( $images as $image ) {
            if ( ! $image['has_alt'] ) {
                $missing_alt++;
            }
            
            if ( $image['is_empty_alt'] ) {
                $empty_alt++;
            }
            
            if ( $image['has_keyword_in_alt'] ) {
                $keyword_in_alt++;
            }
        }
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( $total_images === 0 ) {
            $recommendation = __( 'Aucune image détectée. Envisagez d\'ajouter des images pour améliorer l\'engagement et le référencement.', 'smartseo-ai' );
        } elseif ( $missing_alt === 0 && $empty_alt === 0 ) {
            $status = 'good';
            $recommendation = __( 'Toutes les images ont des attributs alt descriptifs. C\'est excellent pour l\'accessibilité et le SEO.', 'smartseo-ai' );
            
            if ( ! empty( $keyword ) && $keyword_in_alt === 0 ) {
                $status = 'average';
                $recommendation .= ' ' . sprintf(
                    __( 'Cependant, aucune image n\'inclut le mot-clé principal "%s" dans son attribut alt. Envisagez d\'ajouter le mot-clé à au moins une image pertinente.', 'smartseo-ai' ),
                    $keyword
                );
            }
        } elseif ( $missing_alt + $empty_alt <= $total_images / 2 ) {
            $status = 'average';
            $recommendation = __( 'Certaines images n\'ont pas d\'attributs alt ou ont des attributs alt vides. Ajoutez des descriptions pertinentes à toutes les images.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'La plupart des images n\'ont pas d\'attributs alt ou ont des attributs alt vides. Ajoutez des descriptions pertinentes à toutes les images pour améliorer l\'accessibilité et le SEO.', 'smartseo-ai' );
        }
        
        return array(
            'missing_alt' => $missing_alt,
            'empty_alt' => $empty_alt,
            'keyword_in_alt' => $keyword_in_alt,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Analyse l'optimisation des images
     *
     * @param array $images Images à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_image_optimization( $images ) {
        $total_images = count( $images );
        $large_images = 0;
        $missing_dimensions = 0;
        
        foreach ( $images as $image ) {
            if ( $image['is_large'] ) {
                $large_images++;
            }
            
            if ( $image['width'] === 0 || $image['height'] === 0 ) {
                $missing_dimensions++;
            }
        }
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( $total_images === 0 ) {
            $recommendation = __( 'Aucune image détectée. Envisagez d\'ajouter des images pour améliorer l\'engagement et le référencement.', 'smartseo-ai' );
        } elseif ( $large_images === 0 && $missing_dimensions === 0 ) {
            $status = 'good';
            $recommendation = __( 'Toutes les images sont bien optimisées. C\'est excellent pour les performances et l\'expérience utilisateur.', 'smartseo-ai' );
        } elseif ( $large_images + $missing_dimensions <= $total_images / 2 ) {
            $status = 'average';
            $recommendation = __( 'Certaines images pourraient être mieux optimisées. Compressez les images volumineuses et spécifiez les dimensions pour toutes les images.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'La plupart des images ne sont pas optimisées. Compressez les images volumineuses et spécifiez les dimensions pour toutes les images afin d\'améliorer les performances.', 'smartseo-ai' );
        }
        
        return array(
            'large_images' => $large_images,
            'missing_dimensions' => $missing_dimensions,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Récupère le chemin du fichier à partir de l'URL
     *
     * @param string $url URL de l'image.
     * @return string|false Chemin du fichier ou false si non trouvé.
     */
    private function get_file_path_from_url( $url ) {
        // Récupérer le chemin de base des uploads
        $upload_dir = wp_upload_dir();
        $upload_url = $upload_dir['baseurl'];
        $upload_path = $upload_dir['basedir'];
        
        // Vérifier si l'URL est dans le répertoire des uploads
        if ( strpos( $url, $upload_url ) === 0 ) {
            $file_path = str_replace( $upload_url, $upload_path, $url );
            return $file_path;
        }
        
        // Vérifier si l'URL est relative
        if ( strpos( $url, '/' ) === 0 ) {
            $site_url = get_site_url();
            $site_path = ABSPATH;
            
            $file_path = str_replace( $site_url, $site_path, $url );
            return $file_path;
        }
        
        return false;
    }

    /**
     * Calcule le score global de l'analyse d'images
     *
     * @param array $alt_analysis          Résultats de l'analyse des attributs alt.
     * @param array $optimization_analysis Résultats de l'analyse de l'optimisation.
     * @return int Score (0-100).
     */
    private function calculate_score( $alt_analysis, $optimization_analysis ) {
        // Pondération des différents facteurs
        $weights = array(
            'alt' => 0.7,
            'optimization' => 0.3,
        );
        
        // Convertir les statuts en scores numériques
        $scores = array(
            'alt' => $this->status_to_score( $alt_analysis['status'] ),
            'optimization' => $this->status_to_score( $optimization_analysis['status'] ),
        );
        
        // Calculer le score pondéré
        $weighted_score = 
            $scores['alt'] * $weights['alt'] +
            $scores['optimization'] * $weights['optimization'];
        
        // Arrondir le score
        return round( $weighted_score );
    }

    /**
     * Convertit un statut en score numérique
     *
     * @param string $status Statut (good, average, poor).
     * @return int Score (0-100).
     */
    private function status_to_score( $status ) {
        switch ( $status ) {
            case 'good':
                return 100;
            case 'average':
                return 50;
            case 'poor':
                return 0;
            default:
                return 0;
        }
    }
}
