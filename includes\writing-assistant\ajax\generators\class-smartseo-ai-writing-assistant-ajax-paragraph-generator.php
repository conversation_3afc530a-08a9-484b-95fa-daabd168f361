<?php
/**
 * Classe de génération de paragraphes pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la génération de paragraphes
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Paragraph_Generator {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Génère un paragraphe
     *
     * @param int    $post_id ID de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $topic   Sujet de l'article.
     * @return array|WP_Error Résultats de la génération ou erreur.
     */
    public function generate( $post_id, $keyword, $topic ) {
        // Vérifier si le mot-clé ou le sujet est fourni
        if ( empty( $keyword ) && empty( $topic ) ) {
            return new WP_Error( 'missing_data', __( 'Veuillez fournir un mot-clé ou un sujet.', 'smartseo-ai' ) );
        }

        // Utiliser le mot-clé comme sujet s'il n'est pas fourni
        if ( empty( $topic ) ) {
            $topic = $keyword;
        }

        // Récupérer les données de l'article
        $post = get_post( $post_id );
        $post_type = $post ? $post->post_type : 'post';
        $post_type_object = get_post_type_object( $post_type );
        $post_type_label = $post_type_object ? $post_type_object->labels->singular_name : __( 'Article', 'smartseo-ai' );

        // Construire le prompt
        $prompt = $this->build_prompt( $keyword, $topic, $post_type_label );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        $paragraph = $this->process_response( $response );

        // Retourner les résultats
        return array(
            'paragraph' => $paragraph,
        );
    }

    /**
     * Construit le prompt pour l'IA
     *
     * @param string $keyword       Mot-clé principal.
     * @param string $topic         Sujet de l'article.
     * @param string $post_type_label Label du type de publication.
     * @return string Prompt pour l'IA.
     */
    private function build_prompt( $keyword, $topic, $post_type_label ) {
        $prompt = "Génère un paragraphe SEO informatif et engageant pour un {$post_type_label} sur le sujet : \"{$topic}\".";
        
        if ( ! empty( $keyword ) && $keyword !== $topic ) {
            $prompt .= " Le mot-clé principal à inclure est : \"{$keyword}\".";
        }
        
        $prompt .= " Le paragraphe doit :
1. Être optimisé pour le SEO
2. Être informatif et apporter de la valeur au lecteur
3. Inclure le mot-clé principal de manière naturelle
4. Être entre 100 et 150 mots
5. Utiliser un ton professionnel mais accessible
6. Inclure des faits ou statistiques pertinents si possible

Réponds uniquement avec le paragraphe, sans titre ni autres explications.";

        return $prompt;
    }

    /**
     * Traite la réponse de l'IA
     *
     * @param string $response Réponse de l'IA.
     * @return string Paragraphe généré.
     */
    private function process_response( $response ) {
        // Nettoyer la réponse
        $paragraph = trim( $response );
        
        // Supprimer les titres éventuels
        $paragraph = preg_replace( '/^#+ .*$/m', '', $paragraph );
        
        // Supprimer les lignes vides au début et à la fin
        $paragraph = trim( $paragraph );
        
        return $paragraph;
    }
}
