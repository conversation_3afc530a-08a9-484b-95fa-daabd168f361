/**
 * Script d'initialisation de l'interface utilisateur pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Initialisation de l'interface utilisateur
     */
    const SmartSEOAIUIInit = {
        /**
         * Initialise l'interface utilisateur
         */
        init: function() {
            console.log('SmartSEO AI UI Init: Initialisation de l\'interface utilisateur');
            
            // Vérifier si les modules sont chargés
            if (!window.SmartSEOAI || !window.SmartSEOAI.UIManager) {
                console.error('SmartSEO AI UI Init: Les modules nécessaires ne sont pas chargés');
                return;
            }
            
            // Initialiser l'interface utilisateur
            window.SmartSEOAI.UIManager.init();
            
            // Vérifier si nous sommes dans l'éditeur Gutenberg
            if (this.isGutenbergEditor()) {
                console.log('SmartSEO AI UI Init: Éditeur Gutenberg détecté');
                
                // Initialiser la barre latérale pour Gutenberg
                if (window.SmartSEOAI.UIManager.initGutenbergSidebar) {
                    window.SmartSEOAI.UIManager.initGutenbergSidebar();
                }
            } else {
                console.log('SmartSEO AI UI Init: Éditeur classique détecté');
                
                // Initialiser la barre latérale pour l'éditeur classique
                if (window.SmartSEOAI.UIManager.initClassicEditorSidebar) {
                    window.SmartSEOAI.UIManager.initClassicEditorSidebar();
                }
            }
        },
        
        /**
         * Vérifie si nous sommes dans l'éditeur Gutenberg
         * @return {boolean} Vrai si nous sommes dans l'éditeur Gutenberg
         */
        isGutenbergEditor: function() {
            return window.wp && window.wp.data && window.wp.data.select('core/editor');
        }
    };
    
    // Initialiser l'interface utilisateur au chargement du document
    $(document).ready(function() {
        // Attendre que tous les scripts soient chargés
        setTimeout(function() {
            SmartSEOAIUIInit.init();
        }, 1000);
    });
    
    // Exposer l'initialisation pour pouvoir la forcer
    window.SmartSEOAIUIInit = SmartSEOAIUIInit;

})(jQuery);
