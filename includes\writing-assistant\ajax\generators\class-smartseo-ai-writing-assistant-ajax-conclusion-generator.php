<?php
/**
 * Classe de génération de conclusions pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la génération de conclusions
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Conclusion_Generator {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Génère une conclusion
     *
     * @param int    $post_id ID de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $topic   Sujet de l'article.
     * @param string $content Contenu de l'article.
     * @return array|WP_Error Résultats de la génération ou erreur.
     */
    public function generate( $post_id, $keyword, $topic, $content ) {
        // Vérifier si le mot-clé ou le sujet est fourni
        if ( empty( $keyword ) && empty( $topic ) ) {
            return new WP_Error( 'missing_data', __( 'Veuillez fournir un mot-clé ou un sujet.', 'smartseo-ai' ) );
        }

        // Utiliser le mot-clé comme sujet s'il n'est pas fourni
        if ( empty( $topic ) ) {
            $topic = $keyword;
        }

        // Récupérer les données de l'article
        $post = get_post( $post_id );
        $post_type = $post ? $post->post_type : 'post';
        $post_type_object = get_post_type_object( $post_type );
        $post_type_label = $post_type_object ? $post_type_object->labels->singular_name : __( 'Article', 'smartseo-ai' );

        // Extraire un résumé du contenu
        $summary = $this->extract_summary( $content );

        // Construire le prompt
        $prompt = $this->build_prompt( $keyword, $topic, $post_type_label, $summary );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        $conclusion = $this->process_response( $response );

        // Retourner les résultats
        return array(
            'conclusion' => $conclusion,
        );
    }

    /**
     * Extrait un résumé du contenu
     *
     * @param string $content Contenu de l'article.
     * @return string Résumé du contenu.
     */
    private function extract_summary( $content ) {
        // Nettoyer le contenu
        $content = wp_strip_all_tags( $content );
        
        // Limiter la longueur
        if ( strlen( $content ) > 1000 ) {
            $content = substr( $content, 0, 1000 ) . '...';
        }
        
        return $content;
    }

    /**
     * Construit le prompt pour l'IA
     *
     * @param string $keyword       Mot-clé principal.
     * @param string $topic         Sujet de l'article.
     * @param string $post_type_label Label du type de publication.
     * @param string $summary       Résumé du contenu.
     * @return string Prompt pour l'IA.
     */
    private function build_prompt( $keyword, $topic, $post_type_label, $summary ) {
        $prompt = "Génère une conclusion SEO efficace pour un {$post_type_label} sur le sujet : \"{$topic}\".";
        
        if ( ! empty( $keyword ) && $keyword !== $topic ) {
            $prompt .= " Le mot-clé principal à inclure est : \"{$keyword}\".";
        }
        
        if ( ! empty( $summary ) ) {
            $prompt .= " Voici un résumé du contenu : \"{$summary}\".";
        }
        
        $prompt .= " La conclusion doit :
1. Résumer les points clés de l'article
2. Réaffirmer l'importance du sujet
3. Inclure le mot-clé principal de manière naturelle
4. Proposer un appel à l'action clair
5. Être entre 100 et 150 mots
6. Laisser une impression forte au lecteur

Réponds uniquement avec la conclusion, sans titre ni autres explications.";

        return $prompt;
    }

    /**
     * Traite la réponse de l'IA
     *
     * @param string $response Réponse de l'IA.
     * @return string Conclusion générée.
     */
    private function process_response( $response ) {
        // Nettoyer la réponse
        $conclusion = trim( $response );
        
        // Supprimer les titres éventuels
        $conclusion = preg_replace( '/^#+ .*$/m', '', $conclusion );
        
        // Supprimer les lignes vides au début et à la fin
        $conclusion = trim( $conclusion );
        
        return $conclusion;
    }
}
