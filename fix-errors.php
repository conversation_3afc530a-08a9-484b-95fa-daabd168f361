<?php
/**
 * Script de réparation rapide pour SmartSEO AI
 * 
 * Ce script corrige immédiatement les erreurs courantes
 */

// Vérifier que nous sommes dans WordPress
if ( ! defined( 'ABSPATH' ) ) {
    die( 'Accès direct interdit' );
}

/**
 * Classe de réparation rapide
 */
class SmartSEO_AI_Quick_Fix {
    
    /**
     * Applique tous les correctifs
     */
    public static function apply_all_fixes() {
        $fixes_applied = array();
        
        // Correctif 1 : Initialiser les options du sitemap
        if ( self::fix_sitemap_options() ) {
            $fixes_applied[] = 'Options sitemap initialisées';
        }
        
        // Correctif 2 : Initialiser les options de la meta box
        if ( self::fix_metabox_options() ) {
            $fixes_applied[] = 'Options meta box initialisées';
        }
        
        // Correctif 3 : Initialiser les options d'optimisation en masse
        if ( self::fix_bulk_optimizer_options() ) {
            $fixes_applied[] = 'Options optimisation en masse initialisées';
        }
        
        // Correctif 4 : Vérifier les constantes
        if ( self::check_constants() ) {
            $fixes_applied[] = 'Constantes vérifiées';
        }
        
        return $fixes_applied;
    }
    
    /**
     * Corrige les options du sitemap
     */
    private static function fix_sitemap_options() {
        $options = get_option( 'smartseo_ai_sitemap_options' );
        
        if ( false === $options || ! is_array( $options ) ) {
            $default_options = array(
                'enabled' => 'no',
                'use_index' => 'yes',
                'include_posts' => 'yes',
                'include_pages' => 'yes',
                'include_products' => 'no',
                'include_custom_post_types' => 'no',
                'max_entries' => 1000,
                'update_frequency' => 'daily',
                'priority' => '0.8'
            );
            
            update_option( 'smartseo_ai_sitemap_options', $default_options );
            return true;
        }
        
        // Vérifier que toutes les clés nécessaires existent
        $required_keys = array( 'enabled', 'use_index', 'include_posts', 'include_pages' );
        $updated = false;
        
        foreach ( $required_keys as $key ) {
            if ( ! isset( $options[ $key ] ) ) {
                switch ( $key ) {
                    case 'enabled':
                        $options[ $key ] = 'no';
                        break;
                    case 'use_index':
                        $options[ $key ] = 'yes';
                        break;
                    case 'include_posts':
                    case 'include_pages':
                        $options[ $key ] = 'yes';
                        break;
                }
                $updated = true;
            }
        }
        
        if ( $updated ) {
            update_option( 'smartseo_ai_sitemap_options', $options );
            return true;
        }
        
        return false;
    }
    
    /**
     * Corrige les options de la meta box
     */
    private static function fix_metabox_options() {
        $options_to_check = array(
            'smartseo_ai_enhanced_metabox' => true,
            'smartseo_ai_metabox_position' => 'normal',
            'smartseo_ai_metabox_priority' => 'high',
            'smartseo_ai_auto_optimize' => false,
            'smartseo_ai_show_score_in_list' => true,
            'smartseo_ai_enable_og_fields' => true,
            'smartseo_ai_enable_advanced_fields' => true,
        );
        
        $updated = false;
        
        foreach ( $options_to_check as $option_name => $default_value ) {
            if ( false === get_option( $option_name ) ) {
                update_option( $option_name, $default_value );
                $updated = true;
            }
        }
        
        return $updated;
    }
    
    /**
     * Corrige les options d'optimisation en masse
     */
    private static function fix_bulk_optimizer_options() {
        $options_to_check = array(
            'smartseo_ai_enhanced_bulk_optimizer' => true,
            'smartseo_ai_bulk_batch_size' => 3,
            'smartseo_ai_bulk_delay' => 3000,
            'smartseo_ai_bulk_max_concurrent' => 2,
            'smartseo_ai_bulk_auto_retry' => true,
            'smartseo_ai_bulk_retry_attempts' => 3,
        );
        
        $updated = false;
        
        foreach ( $options_to_check as $option_name => $default_value ) {
            if ( false === get_option( $option_name ) ) {
                update_option( $option_name, $default_value );
                $updated = true;
            }
        }
        
        return $updated;
    }
    
    /**
     * Vérifie les constantes
     */
    private static function check_constants() {
        $required_constants = array(
            'SMARTSEO_AI_VERSION',
            'SMARTSEO_AI_PLUGIN_FILE',
            'SMARTSEO_AI_PLUGIN_DIR',
            'SMARTSEO_AI_PLUGIN_URL',
            'SMARTSEO_AI_PLUGIN_BASENAME'
        );
        
        $missing = array();
        
        foreach ( $required_constants as $constant ) {
            if ( ! defined( $constant ) ) {
                $missing[] = $constant;
            }
        }
        
        if ( ! empty( $missing ) ) {
            // Afficher un avertissement dans les logs
            error_log( 'SmartSEO AI - Constantes manquantes : ' . implode( ', ', $missing ) );
            return false;
        }
        
        return true;
    }
    
    /**
     * Affiche le statut des correctifs
     */
    public static function display_fix_status() {
        $fixes = self::apply_all_fixes();
        
        echo '<div class="notice notice-success is-dismissible">';
        echo '<h3>🔧 SmartSEO AI - Correctifs Appliqués</h3>';
        
        if ( empty( $fixes ) ) {
            echo '<p>✅ Aucun correctif nécessaire. Tout fonctionne correctement !</p>';
        } else {
            echo '<p>✅ Correctifs appliqués avec succès :</p>';
            echo '<ul>';
            foreach ( $fixes as $fix ) {
                echo '<li>• ' . esc_html( $fix ) . '</li>';
            }
            echo '</ul>';
        }
        
        echo '</div>';
    }
}

// Appliquer les correctifs automatiquement lors de l'activation
register_activation_hook( SMARTSEO_AI_PLUGIN_FILE, array( 'SmartSEO_AI_Quick_Fix', 'apply_all_fixes' ) );

// Ajouter un lien de réparation dans le menu admin
add_action( 'admin_menu', function() {
    if ( current_user_can( 'manage_options' ) ) {
        add_submenu_page(
            'tools.php',
            'Réparer SmartSEO AI',
            'Réparer SmartSEO AI',
            'manage_options',
            'smartseo-ai-fix',
            function() {
                echo '<div class="wrap">';
                echo '<h1>🔧 Réparation SmartSEO AI</h1>';
                echo '<p>Cette page applique automatiquement les correctifs pour résoudre les erreurs courantes.</p>';
                SmartSEO_AI_Quick_Fix::display_fix_status();
                echo '</div>';
            }
        );
    }
});

// Appliquer les correctifs lors du chargement de l'admin si nécessaire
add_action( 'admin_init', function() {
    // Vérifier si les options de base existent
    if ( false === get_option( 'smartseo_ai_sitemap_options' ) ) {
        SmartSEO_AI_Quick_Fix::apply_all_fixes();
    }
});
?>
