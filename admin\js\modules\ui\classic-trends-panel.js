/**
 * Module de gestion du panneau de tendances pour l'éditeur classique
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire du panneau de tendances pour l'éditeur classique
     */
    const ClassicTrendsPanel = {
        /**
         * Initialise le gestionnaire du panneau de tendances
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Rend le panneau de tendances
         * @return {string} HTML du panneau
         */
        render: function() {
            // Créer la structure du panneau
            const $panel = $('<div class="smartseo-ai-panel"></div>');
            
            // Ajouter l'en-tête
            $panel.append('<div class="smartseo-ai-panel-header">' +
                '<h3>Tendances et sujets populaires</h3>' +
                '<p>Découvrez les sujets tendances liés à votre mot-clé.</p>' +
                '</div>');
            
            // Ajouter le contenu
            const $content = $('<div class="smartseo-ai-panel-content"></div>');
            
            // Ajouter le champ de mot-clé
            $content.append('<div class="smartseo-ai-form-group">' +
                '<label for="smartseo-ai-trends-keyword">Mot-clé pour les tendances</label>' +
                '<input type="text" id="smartseo-ai-trends-keyword" class="widefat" placeholder="Entrez un mot-clé">' +
                '</div>');
            
            // Ajouter le bouton de recherche
            $content.append('<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-get-trends">Obtenir les tendances</button>' +
                '</div>');
            
            // Ajouter le conteneur de résultats
            $content.append('<div class="smartseo-ai-results-container" id="smartseo-ai-trends-results">' +
                '<div class="smartseo-ai-loading" style="display: none;">' +
                '<span class="spinner is-active"></span>' +
                '<p>Recherche des tendances...</p>' +
                '</div>' +
                '<div class="smartseo-ai-results" style="display: none;">' +
                '<h4>Sujets tendances</h4>' +
                '<div class="smartseo-ai-results-content"></div>' +
                '</div>' +
                '</div>');
            
            // Ajouter le contenu au panneau
            $panel.append($content);
            
            return $panel;
        },

        /**
         * Affiche les résultats de tendances
         * @param {Object} results Résultats de tendances
         */
        showResults: function(results) {
            const $container = $('#smartseo-ai-trends-results');
            const $loading = $container.find('.smartseo-ai-loading');
            const $results = $container.find('.smartseo-ai-results');
            const $content = $results.find('.smartseo-ai-results-content');
            
            // Masquer le chargement
            $loading.hide();
            
            // Vider le contenu précédent
            $content.empty();
            
            // Afficher les résultats
            $results.show();
            
            // Vérifier si les résultats sont valides
            if (!results || !results.trending_topics || results.trending_topics.length === 0) {
                $content.append('<p>Aucune tendance disponible.</p>');
                return;
            }
            
            // Afficher les sujets tendances
            const trendingTopics = results.trending_topics;
            
            const $trendsList = $('<div class="smartseo-ai-trends-list"></div>');
            
            trendingTopics.forEach(topic => {
                const $item = $('<div class="smartseo-ai-trend-item"></div>');
                
                // Déterminer la classe de popularité
                let popularityClass = 'smartseo-ai-popularity-low';
                if (topic.popularity === 'élevée' || topic.popularity === 'élevé') {
                    popularityClass = 'smartseo-ai-popularity-high';
                } else if (topic.popularity === 'moyenne' || topic.popularity === 'moyen') {
                    popularityClass = 'smartseo-ai-popularity-medium';
                }
                
                $item.append('<div class="smartseo-ai-trend-topic">' + topic.topic + '</div>');
                
                const $details = $('<div class="smartseo-ai-trend-details"></div>');
                $details.append('<div class="smartseo-ai-trend-popularity ' + popularityClass + '"><span>Popularité :</span> ' + topic.popularity + '</div>');
                $details.append('<div class="smartseo-ai-trend-seasonality"><span>Saisonnalité :</span> ' + topic.seasonality + '</div>');
                
                if (topic.explanation) {
                    $details.append('<div class="smartseo-ai-trend-explanation"><span>Explication :</span> ' + topic.explanation + '</div>');
                }
                
                $item.append($details);
                
                $item.append('<div class="smartseo-ai-trend-actions">' +
                    '<button class="button smartseo-ai-use-trend" data-trend="' + topic.topic + '">Utiliser comme sujet</button>' +
                    '<button class="button smartseo-ai-generate-content-for-trend" data-trend="' + topic.topic + '">Générer du contenu</button>' +
                    '</div>');
                
                $trendsList.append($item);
            });
            
            $content.append($trendsList);
            
            // Ajouter les écouteurs d'événements pour les boutons
            $('.smartseo-ai-use-trend').on('click', function() {
                const trend = $(this).data('trend');
                
                // Mettre à jour le champ de mot-clé
                $('#smartseo-ai-focus-keyword, #smartseo-ai-focus-keyword-analysis, #smartseo-ai-trends-keyword').val(trend);
                
                // Mettre à jour le champ de mot-clé principal (si Yoast SEO est installé)
                $('#yoast_wpseo_focuskw').val(trend);
                
                // Mettre à jour le champ de mot-clé principal personnalisé
                $('#smartseo_ai_focus_keyword').val(trend);
                
                // Afficher un message de succès
                window.SmartSEOAI.UIManager.showSuccess('Sujet sélectionné comme mot-clé.');
            });
            
            $('.smartseo-ai-generate-content-for-trend').on('click', function() {
                const trend = $(this).data('trend');
                
                // Mettre à jour le champ de mot-clé
                $('#smartseo-ai-focus-keyword').val(trend);
                
                // Afficher un message
                window.SmartSEOAI.UIManager.showInfo('Veuillez sélectionner un type de contenu à générer dans l\'onglet "Contenu".');
                
                // Basculer vers l'onglet de contenu
                $('.smartseo-ai-tab-nav[data-tab="content"]').click();
            });
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.ClassicTrendsPanel = ClassicTrendsPanel;

})(jQuery);
