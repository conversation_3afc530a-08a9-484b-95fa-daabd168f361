/**
 * Module de gestion du panneau de tendances pour Gutenberg
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire du panneau de tendances pour Gutenberg
     */
    const GutenbergTrendsPanel = {
        /**
         * Initialise le gestionnaire du panneau de tendances
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Rend le panneau de tendances
         * @return {string} HTML du panneau
         */
        render: function() {
            const { __ } = wp.i18n;
            const { Button, TextControl, Spinner } = wp.components;
            const { Fragment } = wp.element;
            
            return (
                <Fragment>
                    <div className="smartseo-ai-panel-header">
                        <h3>{__('Tendances et sujets populaires', 'smartseo-ai')}</h3>
                        <p>{__('Découvrez les sujets tendances liés à votre mot-clé.', 'smartseo-ai')}</p>
                    </div>
                    
                    <div className="smartseo-ai-panel-content">
                        <TextControl
                            label={__('Mot-clé pour les tendances', 'smartseo-ai')}
                            id="smartseo-ai-trends-keyword"
                            placeholder={__('Entrez un mot-clé', 'smartseo-ai')}
                        />
                        
                        <div className="smartseo-ai-button-group">
                            <Button
                                isPrimary
                                className="smartseo-ai-get-trends"
                            >
                                {__('Obtenir les tendances', 'smartseo-ai')}
                            </Button>
                        </div>
                    </div>
                    
                    <div className="smartseo-ai-results-container" id="smartseo-ai-trends-results">
                        <div className="smartseo-ai-loading" style={{ display: 'none' }}>
                            <Spinner />
                            <p>{__('Recherche des tendances...', 'smartseo-ai')}</p>
                        </div>
                        
                        <div className="smartseo-ai-results" style={{ display: 'none' }}>
                            <h4>{__('Sujets tendances', 'smartseo-ai')}</h4>
                            <div className="smartseo-ai-results-content"></div>
                        </div>
                    </div>
                </Fragment>
            );
        },

        /**
         * Affiche les résultats de tendances
         * @param {Object} results Résultats de tendances
         */
        showResults: function(results) {
            const { __ } = wp.i18n;
            const $container = $('#smartseo-ai-trends-results');
            const $loading = $container.find('.smartseo-ai-loading');
            const $results = $container.find('.smartseo-ai-results');
            const $content = $results.find('.smartseo-ai-results-content');
            
            // Masquer le chargement
            $loading.hide();
            
            // Vider le contenu précédent
            $content.empty();
            
            // Afficher les résultats
            $results.show();
            
            // Vérifier si les résultats sont valides
            if (!results || !results.trending_topics || results.trending_topics.length === 0) {
                $content.append('<p>' + __('Aucune tendance disponible.', 'smartseo-ai') + '</p>');
                return;
            }
            
            // Afficher les sujets tendances
            const trendingTopics = results.trending_topics;
            
            const $trendsList = $('<div class="smartseo-ai-trends-list"></div>');
            
            trendingTopics.forEach(topic => {
                const $item = $('<div class="smartseo-ai-trend-item"></div>');
                
                // Déterminer la classe de popularité
                let popularityClass = 'smartseo-ai-popularity-low';
                if (topic.popularity === 'élevée' || topic.popularity === 'élevé') {
                    popularityClass = 'smartseo-ai-popularity-high';
                } else if (topic.popularity === 'moyenne' || topic.popularity === 'moyen') {
                    popularityClass = 'smartseo-ai-popularity-medium';
                }
                
                $item.append('<div class="smartseo-ai-trend-topic">' + topic.topic + '</div>');
                
                const $details = $('<div class="smartseo-ai-trend-details"></div>');
                $details.append('<div class="smartseo-ai-trend-popularity ' + popularityClass + '"><span>' + __('Popularité', 'smartseo-ai') + ':</span> ' + topic.popularity + '</div>');
                $details.append('<div class="smartseo-ai-trend-seasonality"><span>' + __('Saisonnalité', 'smartseo-ai') + ':</span> ' + topic.seasonality + '</div>');
                
                if (topic.explanation) {
                    $details.append('<div class="smartseo-ai-trend-explanation"><span>' + __('Explication', 'smartseo-ai') + ':</span> ' + topic.explanation + '</div>');
                }
                
                $item.append($details);
                
                $item.append('<div class="smartseo-ai-trend-actions">' +
                    '<button class="button smartseo-ai-use-trend" data-trend="' + topic.topic + '">' + __('Utiliser comme sujet', 'smartseo-ai') + '</button>' +
                    '<button class="button smartseo-ai-generate-content-for-trend" data-trend="' + topic.topic + '">' + __('Générer du contenu', 'smartseo-ai') + '</button>' +
                    '</div>');
                
                $trendsList.append($item);
            });
            
            $content.append($trendsList);
            
            // Ajouter les écouteurs d'événements pour les boutons
            $('.smartseo-ai-use-trend').on('click', function() {
                const trend = $(this).data('trend');
                
                // Mettre à jour le champ de mot-clé
                $('#smartseo-ai-focus-keyword, #smartseo-ai-focus-keyword-analysis, #smartseo-ai-trends-keyword').val(trend);
                
                // Afficher un message de succès
                window.SmartSEOAI.UIManager.showSuccess(__('Sujet sélectionné comme mot-clé.', 'smartseo-ai'));
            });
            
            $('.smartseo-ai-generate-content-for-trend').on('click', function() {
                const trend = $(this).data('trend');
                
                // Mettre à jour le champ de mot-clé
                $('#smartseo-ai-focus-keyword').val(trend);
                
                // Afficher un message
                window.SmartSEOAI.UIManager.showInfo(__('Veuillez sélectionner un type de contenu à générer dans l\'onglet "Contenu".', 'smartseo-ai'));
                
                // Basculer vers l'onglet de contenu
                $('.smartseo-ai-tab-content').click();
            });
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.GutenbergTrendsPanel = GutenbergTrendsPanel;

})(jQuery);
