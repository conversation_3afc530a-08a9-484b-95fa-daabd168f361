<?php
/**
 * Classe de validation des paramètres du Sitemap XML
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui valide les paramètres du Sitemap XML
 */
class SmartSEO_AI_Sitemap_Settings_Validator {

    /**
     * Valide les paramètres du sitemap
     *
     * @param array $input Paramètres à valider.
     * @return array Paramètres validés.
     */
    public function validate( $input ) {
        $validated = array();
        
        // Valider l'activation
        $validated['enabled'] = isset( $input['enabled'] ) && 'yes' === $input['enabled'] ? 'yes' : 'no';
        
        // Valider les contenus à inclure
        $validated['include_posts'] = isset( $input['include_posts'] ) && 'yes' === $input['include_posts'] ? 'yes' : 'no';
        $validated['include_pages'] = isset( $input['include_pages'] ) && 'yes' === $input['include_pages'] ? 'yes' : 'no';
        $validated['include_products'] = isset( $input['include_products'] ) && 'yes' === $input['include_products'] ? 'yes' : 'no';
        $validated['include_categories'] = isset( $input['include_categories'] ) && 'yes' === $input['include_categories'] ? 'yes' : 'no';
        $validated['include_tags'] = isset( $input['include_tags'] ) && 'yes' === $input['include_tags'] ? 'yes' : 'no';
        $validated['include_custom_taxonomies'] = isset( $input['include_custom_taxonomies'] ) && 'yes' === $input['include_custom_taxonomies'] ? 'yes' : 'no';
        $validated['include_custom_post_types'] = isset( $input['include_custom_post_types'] ) && 'yes' === $input['include_custom_post_types'] ? 'yes' : 'no';
        $validated['include_media'] = isset( $input['include_media'] ) && 'yes' === $input['include_media'] ? 'yes' : 'no';
        
        // Valider les options avancées
        $validated['use_index'] = isset( $input['use_index'] ) && 'yes' === $input['use_index'] ? 'yes' : 'no';
        
        // Valider le nombre maximum d'entrées
        $max_entries = isset( $input['max_entries'] ) ? intval( $input['max_entries'] ) : 1000;
        $validated['max_entries'] = $this->validate_max_entries( $max_entries );
        
        // Valider les fréquences
        $validated['frequencies'] = $this->validate_frequencies( isset( $input['frequencies'] ) ? $input['frequencies'] : array() );
        
        // Valider les priorités
        $validated['priorities'] = $this->validate_priorities( isset( $input['priorities'] ) ? $input['priorities'] : array() );
        
        return $validated;
    }
    
    /**
     * Valide le nombre maximum d'entrées
     *
     * @param int $max_entries Nombre maximum d'entrées.
     * @return int Nombre maximum d'entrées validé.
     */
    private function validate_max_entries( $max_entries ) {
        // Vérifier que la valeur est un entier positif
        if ( ! is_numeric( $max_entries ) || $max_entries < 100 ) {
            return 1000;
        }
        
        // Limiter à 50000 entrées maximum
        if ( $max_entries > 50000 ) {
            return 50000;
        }
        
        return $max_entries;
    }
    
    /**
     * Valide les fréquences
     *
     * @param array $frequencies Fréquences à valider.
     * @return array Fréquences validées.
     */
    private function validate_frequencies( $frequencies ) {
        $valid_frequencies = array( 'always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never' );
        $validated = array();
        
        // Valeurs par défaut
        $defaults = array(
            'post' => 'weekly',
            'page' => 'monthly',
            'product' => 'daily',
            'category' => 'monthly',
            'post_tag' => 'monthly',
            'attachment' => 'yearly',
        );
        
        // Valider chaque fréquence
        foreach ( $defaults as $type => $default ) {
            if ( isset( $frequencies[ $type ] ) && in_array( $frequencies[ $type ], $valid_frequencies, true ) ) {
                $validated[ $type ] = $frequencies[ $type ];
            } else {
                $validated[ $type ] = $default;
            }
        }
        
        return $validated;
    }
    
    /**
     * Valide les priorités
     *
     * @param array $priorities Priorités à valider.
     * @return array Priorités validées.
     */
    private function validate_priorities( $priorities ) {
        $validated = array();
        
        // Valeurs par défaut
        $defaults = array(
            'post' => 0.7,
            'page' => 0.8,
            'product' => 0.9,
            'category' => 0.6,
            'post_tag' => 0.5,
            'attachment' => 0.3,
        );
        
        // Valider chaque priorité
        foreach ( $defaults as $type => $default ) {
            if ( isset( $priorities[ $type ] ) ) {
                $priority = (float) $priorities[ $type ];
                
                // Vérifier que la valeur est entre 0 et 1
                if ( $priority >= 0 && $priority <= 1 ) {
                    $validated[ $type ] = $priority;
                } else {
                    $validated[ $type ] = $default;
                }
            } else {
                $validated[ $type ] = $default;
            }
        }
        
        return $validated;
    }
}
