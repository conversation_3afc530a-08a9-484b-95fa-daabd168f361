<?php
/**
 * Classe de génération d'introductions pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la génération d'introductions
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Introduction_Generator {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Génère une introduction
     *
     * @param int    $post_id ID de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $topic   Sujet de l'article.
     * @return array|WP_Error Résultats de la génération ou erreur.
     */
    public function generate( $post_id, $keyword, $topic ) {
        // Vérifier si le mot-clé ou le sujet est fourni
        if ( empty( $keyword ) && empty( $topic ) ) {
            return new WP_Error( 'missing_data', __( 'Veuillez fournir un mot-clé ou un sujet.', 'smartseo-ai' ) );
        }

        // Utiliser le mot-clé comme sujet s'il n'est pas fourni
        if ( empty( $topic ) ) {
            $topic = $keyword;
        }

        // Récupérer les données de l'article
        $post = get_post( $post_id );
        $post_type = $post ? $post->post_type : 'post';
        $post_type_object = get_post_type_object( $post_type );
        $post_type_label = $post_type_object ? $post_type_object->labels->singular_name : __( 'Article', 'smartseo-ai' );

        // Construire le prompt
        $prompt = $this->build_prompt( $keyword, $topic, $post_type_label );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        $introduction = $this->process_response( $response );

        // Retourner les résultats
        return array(
            'introduction' => $introduction,
        );
    }

    /**
     * Construit le prompt pour l'IA
     *
     * @param string $keyword       Mot-clé principal.
     * @param string $topic         Sujet de l'article.
     * @param string $post_type_label Label du type de publication.
     * @return string Prompt pour l'IA.
     */
    private function build_prompt( $keyword, $topic, $post_type_label ) {
        $prompt = "Génère une introduction SEO captivante pour un {$post_type_label} sur le sujet : \"{$topic}\".";
        
        if ( ! empty( $keyword ) && $keyword !== $topic ) {
            $prompt .= " Le mot-clé principal à inclure est : \"{$keyword}\".";
        }
        
        $prompt .= " L'introduction doit :
1. Capter l'attention du lecteur dès les premières lignes
2. Présenter clairement le sujet et son importance
3. Inclure le mot-clé principal de manière naturelle
4. Annoncer ce que le lecteur va découvrir dans l'article
5. Être entre 100 et 150 mots
6. Utiliser un ton professionnel mais accessible

Réponds uniquement avec l'introduction, sans titre ni autres explications.";

        return $prompt;
    }

    /**
     * Traite la réponse de l'IA
     *
     * @param string $response Réponse de l'IA.
     * @return string Introduction générée.
     */
    private function process_response( $response ) {
        // Nettoyer la réponse
        $introduction = trim( $response );
        
        // Supprimer les titres éventuels
        $introduction = preg_replace( '/^#+ .*$/m', '', $introduction );
        
        // Supprimer les lignes vides au début et à la fin
        $introduction = trim( $introduction );
        
        return $introduction;
    }
}
