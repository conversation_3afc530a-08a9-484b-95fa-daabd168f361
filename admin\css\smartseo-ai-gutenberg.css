/**
 * Styles pour l'intégration avec <PERSON>
 */

/* Panneau latéral */
.smartseo-ai-sidebar {
    padding: 16px;
}

.smartseo-ai-sidebar-title {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
}

.smartseo-ai-sidebar-button {
    width: 100%;
    margin-bottom: 16px !important;
    justify-content: center;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%) !important;
    color: #fff !important;
    border: none !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

.smartseo-ai-sidebar-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

.smartseo-ai-sidebar-button svg {
    margin-right: 8px;
}

.smartseo-ai-sidebar-score {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.smartseo-ai-sidebar-score-value {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    color: #fff;
    margin-right: 12px;
}

.smartseo-ai-sidebar-score-good {
    background: linear-gradient(135deg, #43a047 0%, #2e7d32 100%);
}

.smartseo-ai-sidebar-score-average {
    background: linear-gradient(135deg, #fb8c00 0%, #ef6c00 100%);
}

.smartseo-ai-sidebar-score-poor {
    background: linear-gradient(135deg, #e53935 0%, #c62828 100%);
}

.smartseo-ai-sidebar-score-none {
    background: #f5f5f5;
    color: #757575;
}

.smartseo-ai-sidebar-score-label {
    font-weight: 600;
    color: #333;
}

.smartseo-ai-sidebar-field {
    margin-bottom: 16px;
}

.smartseo-ai-sidebar-field-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.smartseo-ai-sidebar-char-count {
    float: right;
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

.smartseo-ai-sidebar-char-count.over-limit {
    color: #e53935;
}

.smartseo-ai-sidebar-advice {
    background: #f8f9fa;
    border-left: 4px solid #2575fc;
    padding: 12px;
    border-radius: 0 4px 4px 0;
    margin-top: 16px;
}

.smartseo-ai-sidebar-advice ul {
    margin: 0;
    padding-left: 20px;
}

.smartseo-ai-sidebar-advice li {
    margin-bottom: 8px;
}

.smartseo-ai-sidebar-section-title {
    margin-top: 24px;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.smartseo-ai-sidebar-status {
    margin-top: 8px;
    font-style: italic;
    color: #666;
}

/* Animation de chargement */
@keyframes smartseo-ai-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.smartseo-ai-sidebar-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #2575fc;
    animation: smartseo-ai-spin 1s ease-in-out infinite;
    margin-right: 8px;
    vertical-align: middle;
}

/* Notification */
.smartseo-ai-sidebar-notice {
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;
}

.smartseo-ai-sidebar-notice-success {
    background-color: #e8f5e9;
    border-left: 4px solid #43a047;
    color: #2e7d32;
}

.smartseo-ai-sidebar-notice-error {
    background-color: #ffebee;
    border-left: 4px solid #e53935;
    color: #c62828;
}

.smartseo-ai-sidebar-notice-info {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
    color: #1976d2;
}
