/**
 * Script de correction des SVG pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Correcteur de SVG pour l'Assistant de Rédaction SEO
     */
    const SmartSEOAISVGFixer = {
        /**
         * Initialise le correcteur
         */
        init: function() {
            console.log('SmartSEO AI SVG Fixer: Initialisation');
            
            // Ajouter le bouton de correction
            this.addFixerButton();
            
            // Vérifier les erreurs SVG
            this.checkSVGErrors();
            
            console.log('SmartSEO AI SVG Fixer: Initialisation terminée');
        },
        
        /**
         * Ajoute le bouton de correction
         */
        addFixerButton: function() {
            console.log('SmartSEO AI SVG Fixer: Ajout du bouton de correction');
            
            // Créer le bouton
            const $button = $('<div id="smartseo-ai-svg-fixer-button" style="position: fixed; bottom: 100px; right: 20px; background-color: #4caf50; color: white; padding: 10px; border-radius: 5px; cursor: pointer; z-index: 9999;">Corriger SVG</div>');
            
            // Ajouter le bouton au corps de la page
            $('body').append($button);
            
            // Ajouter l'écouteur d'événement
            $button.on('click', function() {
                SmartSEOAISVGFixer.fixSVGErrors();
            });
            
            console.log('SmartSEO AI SVG Fixer: Bouton de correction ajouté');
        },
        
        /**
         * Vérifie les erreurs SVG
         */
        checkSVGErrors: function() {
            console.log('SmartSEO AI SVG Fixer: Vérification des erreurs SVG');
            
            // Écouter les erreurs de script
            window.addEventListener('error', function(event) {
                // Vérifier si l'erreur est liée à SVG
                if (event.message.includes('path') || event.message.includes('SVG') || event.message.includes('d:')) {
                    console.error('SmartSEO AI SVG Fixer: Erreur SVG détectée', {
                        message: event.message,
                        filename: event.filename,
                        lineno: event.lineno,
                        colno: event.colno,
                        error: event.error
                    });
                    
                    // Ajouter l'erreur à la liste
                    SmartSEOAISVGFixer.addErrorToList(event.message);
                }
            });
            
            console.log('SmartSEO AI SVG Fixer: Vérification des erreurs SVG terminée');
        },
        
        /**
         * Ajoute une erreur à la liste
         * @param {string} message Message d'erreur
         */
        addErrorToList: function(message) {
            console.log('SmartSEO AI SVG Fixer: Ajout d\'une erreur à la liste');
            
            // Créer la liste si elle n'existe pas
            if (!this.errorList) {
                this.errorList = [];
            }
            
            // Ajouter l'erreur à la liste
            this.errorList.push(message);
            
            console.log('SmartSEO AI SVG Fixer: Erreur ajoutée à la liste');
        },
        
        /**
         * Corrige les erreurs SVG
         */
        fixSVGErrors: function() {
            console.log('SmartSEO AI SVG Fixer: Correction des erreurs SVG');
            
            // Vérifier si la liste d'erreurs existe
            if (!this.errorList) {
                this.errorList = [];
            }
            
            // Afficher les erreurs
            if (this.errorList.length > 0) {
                console.log('SmartSEO AI SVG Fixer: Erreurs SVG détectées', this.errorList);
                
                // Créer le message
                let message = 'Erreurs SVG détectées :\\n\\n';
                
                this.errorList.forEach(function(error) {
                    message += error + '\\n\\n';
                });
                
                // Afficher le message
                alert(message);
                
                // Corriger les SVG
                this.fixSVG();
            } else {
                console.log('SmartSEO AI SVG Fixer: Aucune erreur SVG détectée');
                
                // Afficher le message
                alert('Aucune erreur SVG détectée.');
            }
            
            console.log('SmartSEO AI SVG Fixer: Correction des erreurs SVG terminée');
        },
        
        /**
         * Corrige les SVG
         */
        fixSVG: function() {
            console.log('SmartSEO AI SVG Fixer: Correction des SVG');
            
            // Trouver tous les SVG
            const $svgs = $('svg');
            
            console.log('SmartSEO AI SVG Fixer: ' + $svgs.length + ' SVG trouvés');
            
            // Parcourir les SVG
            $svgs.each(function() {
                const $svg = $(this);
                
                // Trouver tous les chemins
                const $paths = $svg.find('path');
                
                console.log('SmartSEO AI SVG Fixer: ' + $paths.length + ' chemins trouvés dans le SVG');
                
                // Parcourir les chemins
                $paths.each(function() {
                    const $path = $(this);
                    
                    // Récupérer l'attribut d
                    const d = $path.attr('d');
                    
                    if (d) {
                        console.log('SmartSEO AI SVG Fixer: Chemin trouvé avec d = ' + d);
                        
                        // Vérifier si l'attribut d contient des caractères invalides
                        if (d.includes('...') || d.includes('tc') || d.includes('undefined')) {
                            console.log('SmartSEO AI SVG Fixer: Chemin invalide trouvé');
                            
                            // Supprimer le chemin
                            $path.remove();
                            
                            console.log('SmartSEO AI SVG Fixer: Chemin supprimé');
                        }
                    }
                });
            });
            
            console.log('SmartSEO AI SVG Fixer: Correction des SVG terminée');
            
            // Afficher un message
            alert('Les SVG ont été corrigés.');
        }
    };
    
    // Initialiser le correcteur au chargement du document
    $(document).ready(function() {
        SmartSEOAISVGFixer.init();
    });
    
    // Exposer le correcteur pour pouvoir le forcer
    window.SmartSEOAISVGFixer = SmartSEOAISVGFixer;

})(jQuery);
