/**
 * Styles pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

/* Styles généraux */
.smartseo-ai-panel {
    margin-bottom: 20px;
}

.smartseo-ai-panel-header {
    margin-bottom: 15px;
}

.smartseo-ai-panel-header h3 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 16px;
    font-weight: 600;
}

.smartseo-ai-panel-header p {
    margin-top: 0;
    margin-bottom: 0;
    color: #666;
    font-size: 13px;
}

.smartseo-ai-panel-content {
    margin-bottom: 15px;
}

.smartseo-ai-form-group {
    margin-bottom: 15px;
}

.smartseo-ai-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.smartseo-ai-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.smartseo-ai-button-group button {
    flex: 1;
    min-width: 120px;
}

/* Styles des onglets */
.smartseo-ai-tabs {
    margin-bottom: 20px;
}

.smartseo-ai-tabs-nav {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: 1px solid #ccc;
}

.smartseo-ai-tab-nav {
    padding: 8px 12px;
    margin-right: 5px;
    margin-bottom: -1px;
    cursor: pointer;
    border: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    font-weight: 500;
    font-size: 13px;
}

.smartseo-ai-tab-nav:hover {
    background-color: #f0f0f0;
}

.smartseo-ai-tab-nav.active {
    border-color: #ccc;
    border-bottom-color: #fff;
    background-color: #fff;
}

.smartseo-ai-tab-panel {
    display: none;
    padding: 15px 0;
}

.smartseo-ai-tab-panel.active {
    display: block;
}

/* Styles des résultats */
.smartseo-ai-results-container {
    margin-top: 20px;
}

.smartseo-ai-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
}

.smartseo-ai-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 10px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top-color: #2271b1;
    border-radius: 50%;
    animation: smartseo-ai-spin 1s linear infinite;
}

@keyframes smartseo-ai-spin {
    to {
        transform: rotate(360deg);
    }
}

.smartseo-ai-results h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
}

.smartseo-ai-results h5 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 13px;
    font-weight: 600;
}

.smartseo-ai-results h6 {
    margin-top: 15px;
    margin-bottom: 5px;
    font-size: 12px;
    font-weight: 600;
}

.smartseo-ai-suggestions-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.smartseo-ai-suggestion-item {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: #f9f9f9;
}

.smartseo-ai-suggestion-content {
    margin-bottom: 10px;
}

.smartseo-ai-suggestion-actions {
    display: flex;
    gap: 10px;
}

/* Styles des mots-clés */
.smartseo-ai-keyword-list {
    margin-bottom: 20px;
}

.smartseo-ai-keyword-item {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: #f9f9f9;
}

.smartseo-ai-primary-keyword {
    border-color: #2271b1;
    background-color: #f0f6fc;
}

.smartseo-ai-keyword-name {
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 14px;
}

.smartseo-ai-keyword-details {
    margin-bottom: 10px;
    font-size: 12px;
    color: #666;
}

.smartseo-ai-keyword-details span {
    font-weight: 600;
}

.smartseo-ai-keyword-actions {
    display: flex;
    gap: 10px;
}

/* Styles des tendances */
.smartseo-ai-trends-list {
    margin-bottom: 20px;
}

.smartseo-ai-trend-item {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: #f9f9f9;
}

.smartseo-ai-trend-topic {
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 14px;
}

.smartseo-ai-trend-details {
    margin-bottom: 10px;
    font-size: 12px;
    color: #666;
}

.smartseo-ai-trend-details span {
    font-weight: 600;
}

.smartseo-ai-popularity-high {
    color: #46b450;
}

.smartseo-ai-popularity-medium {
    color: #ffb900;
}

.smartseo-ai-popularity-low {
    color: #dc3232;
}

.smartseo-ai-trend-actions {
    display: flex;
    gap: 10px;
}

/* Styles de l'analyse */
.smartseo-ai-score-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 3px;
    background-color: #f9f9f9;
    text-align: center;
}

.smartseo-ai-score-label {
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 14px;
}

.smartseo-ai-score-value {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    margin-bottom: 5px;
    border-radius: 50%;
    font-weight: 700;
    font-size: 24px;
    color: #fff;
}

.smartseo-ai-score-good {
    background-color: #46b450;
}

.smartseo-ai-score-average {
    background-color: #ffb900;
}

.smartseo-ai-score-poor {
    background-color: #dc3232;
}

.smartseo-ai-score-text {
    font-size: 12px;
    color: #666;
}

.smartseo-ai-checklist {
    margin-bottom: 20px;
}

.smartseo-ai-checklist-item {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 3px;
    background-color: #f9f9f9;
}

.smartseo-ai-status-good {
    border-left: 4px solid #46b450;
}

.smartseo-ai-status-average {
    border-left: 4px solid #ffb900;
}

.smartseo-ai-status-poor {
    border-left: 4px solid #dc3232;
}

.smartseo-ai-checklist-icon {
    margin-right: 10px;
}

.smartseo-ai-checklist-label {
    flex: 1;
    font-weight: 600;
}

.smartseo-ai-checklist-recommendation {
    flex-basis: 100%;
    margin-top: 5px;
    padding-left: 25px;
    font-size: 12px;
    color: #666;
}

.smartseo-ai-analysis-section {
    margin-bottom: 20px;
}

.smartseo-ai-analysis-details {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
}

.smartseo-ai-analysis-item {
    padding: 10px;
    border-radius: 3px;
    background-color: #f9f9f9;
}

.smartseo-ai-analysis-label {
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 12px;
}

.smartseo-ai-analysis-value {
    font-size: 12px;
    color: #666;
}

/* Styles des notifications */
#smartseo-ai-notifications {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 9999;
    width: 300px;
}

.smartseo-ai-notification-container {
    margin-bottom: 10px;
}

.smartseo-ai-notification {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 3px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    animation: smartseo-ai-fade-in 0.3s ease-in-out;
}

@keyframes smartseo-ai-fade-in {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.smartseo-ai-notification-success {
    border-left: 4px solid #46b450;
}

.smartseo-ai-notification-error {
    border-left: 4px solid #dc3232;
}

.smartseo-ai-notification-info {
    border-left: 4px solid #00a0d2;
}

.smartseo-ai-notification-warning {
    border-left: 4px solid #ffb900;
}

.smartseo-ai-notification-icon {
    margin-right: 10px;
}

.smartseo-ai-notification-message {
    flex: 1;
    font-size: 13px;
}

.smartseo-ai-notification-close {
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    color: #666;
}

.smartseo-ai-notification-close:hover {
    color: #dc3232;
}

/* Styles des modales */
.smartseo-ai-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.smartseo-ai-modal {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    overflow: auto;
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    animation: smartseo-ai-modal-in 0.3s ease-in-out;
}

@keyframes smartseo-ai-modal-in {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.smartseo-ai-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border-bottom: 1px solid #ddd;
}

.smartseo-ai-modal-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.smartseo-ai-modal-close {
    cursor: pointer;
    font-size: 20px;
    line-height: 1;
    color: #666;
    background: none;
    border: none;
    padding: 0;
}

.smartseo-ai-modal-close:hover {
    color: #dc3232;
}

.smartseo-ai-modal-body {
    padding: 15px;
    max-height: 500px;
    overflow-y: auto;
}

.smartseo-ai-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px;
    border-top: 1px solid #ddd;
}

/* Styles pour le plagiat */
.smartseo-ai-plagiarism-result {
    margin-bottom: 20px;
}

.smartseo-ai-plagiarism-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 3px;
    background-color: #f9f9f9;
    text-align: center;
}

.smartseo-ai-plagiarism-score-value {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin-bottom: 5px;
    border-radius: 50%;
    font-weight: 700;
    font-size: 24px;
    color: #fff;
    background-color: #46b450;
}

.smartseo-ai-plagiarism-score-label {
    font-size: 14px;
    font-weight: 600;
}

.smartseo-ai-plagiarism-sources {
    margin-bottom: 20px;
}

.smartseo-ai-plagiarism-sources h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
}

.smartseo-ai-plagiarism-sources ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.smartseo-ai-plagiarism-sources li {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: #f9f9f9;
}

.smartseo-ai-plagiarism-source-url {
    margin-bottom: 5px;
    font-weight: 600;
}

.smartseo-ai-plagiarism-source-similarity {
    margin-bottom: 5px;
    font-size: 12px;
    color: #666;
}

.smartseo-ai-plagiarism-source-text {
    font-size: 12px;
    font-style: italic;
    color: #666;
}

.smartseo-ai-plagiarism-no-sources {
    padding: 15px;
    border-radius: 3px;
    background-color: #f9f9f9;
    text-align: center;
}

/* Animations */
.smartseo-ai-pulse {
    animation: smartseo-ai-pulse 1s;
}

@keyframes smartseo-ai-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.smartseo-ai-bounce {
    animation: smartseo-ai-bounce 1s;
}

@keyframes smartseo-ai-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.smartseo-ai-rotate {
    animation: smartseo-ai-rotate 1s;
}

@keyframes smartseo-ai-rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.smartseo-ai-shake {
    animation: smartseo-ai-shake 0.5s;
}

@keyframes smartseo-ai-shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Styles pour Gutenberg */
.edit-post-sidebar .smartseo-ai-tabs {
    margin: -16px;
    margin-bottom: 16px;
}

.edit-post-sidebar .smartseo-ai-tabs-nav {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: 1px solid #e2e4e7;
}

.edit-post-sidebar .smartseo-ai-tab-nav {
    flex: 1;
    padding: 8px 0;
    margin-bottom: -1px;
    text-align: center;
    cursor: pointer;
    font-size: 13px;
}

.edit-post-sidebar .smartseo-ai-tab-nav.is-active {
    box-shadow: inset 0 -3px #007cba;
    font-weight: 600;
}

.edit-post-sidebar .smartseo-ai-tab-panel {
    padding: 16px;
}

.edit-post-sidebar .components-panel__body {
    padding: 0;
}

.edit-post-sidebar .smartseo-ai-panel-header {
    margin-bottom: 16px;
}

.edit-post-sidebar .smartseo-ai-button-group {
    margin-bottom: 16px;
}

/* Styles responsifs */
@media screen and (max-width: 782px) {
    .smartseo-ai-button-group {
        flex-direction: column;
    }
    
    .smartseo-ai-analysis-details {
        grid-template-columns: 1fr;
    }
    
    .smartseo-ai-modal {
        width: 95%;
    }
    
    #smartseo-ai-notifications {
        width: 90%;
        right: 5%;
    }
}
