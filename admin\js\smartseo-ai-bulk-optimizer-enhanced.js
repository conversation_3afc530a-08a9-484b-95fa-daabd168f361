/**
 * JavaScript pour l'optimisation en masse améliorée SmartSEO AI
 */
(function($) {
    'use strict';

    // Variables globales
    let isOptimizing = false;
    let optimizationQueue = [];
    let currentOptimization = 0;
    let totalOptimizations = 0;
    let startTime = null;
    let selectedPosts = new Set();

    // Initialisation
    $(document).ready(function() {
        initBulkOptimizer();
        bindEvents();
        updateSelectionInfo();
    });

    /**
     * Initialise l'optimiseur en masse
     */
    function initBulkOptimizer() {
        // Restaurer les sélections depuis le localStorage
        restoreSelections();
        
        // Initialiser les tooltips
        initTooltips();
        
        // Vérifier l'état d'optimisation en cours
        checkOptimizationStatus();
    }

    /**
     * Lie tous les événements
     */
    function bindEvents() {
        // Sélection/désélection
        $('#select-all-checkbox').on('change', toggleSelectAll);
        $('.post-checkbox').on('change', handlePostSelection);
        $('#select-all-visible').on('click', selectAllVisible);
        $('#deselect-all').on('click', deselectAll);

        // Actions d'optimisation
        $('#optimize-selected').on('click', optimizeSelected);
        $('#optimize-all-filtered').on('click', optimizeAllFiltered);
        $('#stop-optimization').on('click', stopOptimization);

        // Debug: vérifier les boutons optimize-single
        console.log('Boutons optimize-single trouvés:', $('.optimize-single').length);
        $('.optimize-single').each(function(index) {
            console.log('Bouton', index, '- Post ID:', $(this).data('post-id'));
        });

        $('.optimize-single').on('click', optimizeSingle);

        // Tri des colonnes
        $('.smartseo-content-table th.sortable').on('click', handleSort);

        // Sauvegarde des sélections
        $(window).on('beforeunload', saveSelections);
    }

    /**
     * Gère la sélection/désélection de tous les éléments
     */
    function toggleSelectAll() {
        const isChecked = $(this).is(':checked');
        $('.post-checkbox').prop('checked', isChecked).trigger('change');
    }

    /**
     * Gère la sélection d'un article individuel
     */
    function handlePostSelection() {
        const postId = $(this).val();
        const isChecked = $(this).is(':checked');
        
        if (isChecked) {
            selectedPosts.add(postId);
        } else {
            selectedPosts.delete(postId);
        }
        
        updateSelectionInfo();
        updateSelectAllCheckbox();
        updateActionButtons();
    }

    /**
     * Sélectionne tous les éléments visibles
     */
    function selectAllVisible() {
        $('.post-checkbox').prop('checked', true).trigger('change');
    }

    /**
     * Désélectionne tous les éléments
     */
    function deselectAll() {
        $('.post-checkbox').prop('checked', false).trigger('change');
        selectedPosts.clear();
        updateSelectionInfo();
        updateSelectAllCheckbox();
        updateActionButtons();
    }

    /**
     * Met à jour les informations de sélection
     */
    function updateSelectionInfo() {
        const count = selectedPosts.size;
        $('#selected-count').text(count);
        
        // Animation du compteur
        if (count > 0) {
            $('#selected-count').addClass('has-selection');
        } else {
            $('#selected-count').removeClass('has-selection');
        }
    }

    /**
     * Met à jour l'état de la case "Tout sélectionner"
     */
    function updateSelectAllCheckbox() {
        const totalVisible = $('.post-checkbox').length;
        const selectedVisible = $('.post-checkbox:checked').length;
        
        if (selectedVisible === 0) {
            $('#select-all-checkbox').prop('indeterminate', false).prop('checked', false);
        } else if (selectedVisible === totalVisible) {
            $('#select-all-checkbox').prop('indeterminate', false).prop('checked', true);
        } else {
            $('#select-all-checkbox').prop('indeterminate', true);
        }
    }

    /**
     * Met à jour l'état des boutons d'action
     */
    function updateActionButtons() {
        const hasSelection = selectedPosts.size > 0;
        $('#optimize-selected').prop('disabled', !hasSelection || isOptimizing);
        $('#optimize-all-filtered').prop('disabled', isOptimizing);
    }

    /**
     * Optimise les articles sélectionnés
     */
    function optimizeSelected() {
        if (selectedPosts.size === 0) {
            showNotification('warning', 'Veuillez sélectionner au moins un article.');
            return;
        }

        const message = `Êtes-vous sûr de vouloir optimiser ${selectedPosts.size} article(s) sélectionné(s) ?`;
        if (!confirm(message)) {
            return;
        }

        const postIds = Array.from(selectedPosts);
        startBulkOptimization(postIds);
    }

    /**
     * Optimise tous les articles filtrés
     */
    function optimizeAllFiltered() {
        const totalPosts = $('.post-checkbox').length;
        
        if (totalPosts === 0) {
            showNotification('warning', 'Aucun article à optimiser.');
            return;
        }

        const message = `Êtes-vous sûr de vouloir optimiser TOUS les articles filtrés (${totalPosts} articles) ? Cette opération peut prendre du temps.`;
        if (!confirm(message)) {
            return;
        }

        const postIds = $('.post-checkbox').map(function() {
            return $(this).val();
        }).get();

        startBulkOptimization(postIds);
    }

    /**
     * Optimise un article individuel
     */
    function optimizeSingle() {
        console.log('optimizeSingle appelée');
        const $button = $(this);
        const postId = $button.data('post-id');

        console.log('Post ID:', postId);
        console.log('Button:', $button);

        if (!postId) {
            console.error('Aucun post ID trouvé !');
            alert('Erreur: Aucun ID d\'article trouvé');
            return;
        }

        if ($button.hasClass('loading')) {
            console.log('Bouton déjà en cours de chargement');
            return;
        }

        console.log('Démarrage de l\'optimisation pour l\'article:', postId);
        $button.addClass('loading').prop('disabled', true);
        
        optimizePost(postId).then(function(result) {
            if (result.success) {
                updatePostRow(postId, result.data);
                showNotification('success', `Article ${postId} optimisé avec succès !`);
            } else {
                showNotification('error', `Erreur lors de l'optimisation de l'article ${postId}: ${result.message}`);
            }
        }).catch(function(error) {
            showNotification('error', `Erreur lors de l'optimisation de l'article ${postId}: ${error.message}`);
        }).finally(function() {
            $button.removeClass('loading').prop('disabled', false);
        });
    }

    /**
     * Démarre l'optimisation en masse
     */
    function startBulkOptimization(postIds) {
        if (isOptimizing) {
            return;
        }

        isOptimizing = true;
        optimizationQueue = [...postIds];
        currentOptimization = 0;
        totalOptimizations = postIds.length;
        startTime = Date.now();

        // Afficher la barre de progression
        showProgressBar();
        updateActionButtons();

        // Démarrer l'optimisation
        processOptimizationQueue();
    }

    /**
     * Traite la queue d'optimisation
     */
    function processOptimizationQueue() {
        if (optimizationQueue.length === 0 || !isOptimizing) {
            finishOptimization();
            return;
        }

        const postId = optimizationQueue.shift();
        currentOptimization++;

        updateProgress();
        updateProgressStatus(`Optimisation de l'article ${postId}...`);

        // Marquer l'article comme en cours
        markPostAsInProgress(postId);

        optimizePost(postId).then(function(result) {
            if (result.success) {
                updatePostRow(postId, result.data);
            } else {
                markPostAsError(postId, result.message);
            }
        }).catch(function(error) {
            markPostAsError(postId, error.message);
        }).finally(function() {
            // Délai entre les optimisations pour éviter la surcharge
            setTimeout(processOptimizationQueue, smartseoAiBulkOptimizer.delayBetweenBatches || 2000);
        });
    }

    /**
     * Optimise un article via AJAX
     */
    function optimizePost(postId) {
        console.log('optimizePost appelée avec ID:', postId);
        console.log('URL API:', smartseoAiBulkOptimizer.restUrl + '/optimize');
        console.log('Nonce:', smartseoAiBulkOptimizer.nonce);

        return new Promise(function(resolve, reject) {
            $.ajax({
                url: smartseoAiBulkOptimizer.restUrl + '/optimize',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': smartseoAiBulkOptimizer.nonce
                },
                data: {
                    post_id: postId
                },
                timeout: 60000, // 60 secondes
                success: function(response) {
                    console.log('Succès API:', response);
                    resolve({
                        success: true,
                        data: response
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Erreur API:', xhr, status, error);
                    console.error('Response Text:', xhr.responseText);

                    let message = 'Erreur inconnue';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    } else if (status === 'timeout') {
                        message = 'Délai d\'attente dépassé';
                    } else if (error) {
                        message = error;
                    }

                    resolve({
                        success: false,
                        message: message
                    });
                }
            });
        });
    }

    /**
     * Met à jour la ligne d'un article après optimisation
     */
    function updatePostRow(postId, data) {
        const $row = $(`.content-row[data-post-id="${postId}"]`);
        
        if (data.seo_score) {
            // Mettre à jour le score
            const scoreClass = getScoreClass(data.seo_score);
            const $scoreCell = $row.find('.column-score');
            $scoreCell.html(`
                <div class="score-badge score-${scoreClass}">
                    <span class="score-value">${data.seo_score}</span>
                    <span class="score-max">/100</span>
                </div>
            `);
        }

        // Mettre à jour le statut
        const $statusCell = $row.find('.column-status');
        $statusCell.html(`
            <div class="status-badge status-optimized">
                <span class="dashicons dashicons-yes-alt"></span>
                Optimisé
            </div>
        `);

        // Mettre à jour la classe de la ligne
        $row.removeClass('not-optimized in-progress error').addClass('optimized');
    }

    /**
     * Marque un article comme en cours d'optimisation
     */
    function markPostAsInProgress(postId) {
        const $row = $(`.content-row[data-post-id="${postId}"]`);
        const $statusCell = $row.find('.column-status');
        
        $statusCell.html(`
            <div class="status-badge status-in-progress">
                <span class="dashicons dashicons-update"></span>
                En cours...
            </div>
        `);
        
        $row.removeClass('not-optimized optimized error').addClass('in-progress');
    }

    /**
     * Marque un article comme ayant une erreur
     */
    function markPostAsError(postId, errorMessage) {
        const $row = $(`.content-row[data-post-id="${postId}"]`);
        const $statusCell = $row.find('.column-status');
        
        $statusCell.html(`
            <div class="status-badge status-error" title="${errorMessage}">
                <span class="dashicons dashicons-warning"></span>
                Erreur
            </div>
        `);
        
        $row.removeClass('not-optimized optimized in-progress').addClass('error');
    }

    /**
     * Affiche la barre de progression
     */
    function showProgressBar() {
        $('#optimization-progress').slideDown();
        $('#stop-optimization').show();
        $('#optimize-selected, #optimize-all-filtered').hide();
    }

    /**
     * Cache la barre de progression
     */
    function hideProgressBar() {
        $('#optimization-progress').slideUp();
        $('#stop-optimization').hide();
        $('#optimize-selected, #optimize-all-filtered').show();
    }

    /**
     * Met à jour la progression
     */
    function updateProgress() {
        const percentage = Math.round((currentOptimization / totalOptimizations) * 100);
        
        $('#progress-fill').css('width', percentage + '%');
        $('#progress-percentage').text(percentage + '%');
        $('#progress-current').text(currentOptimization);
        $('#progress-total').text(totalOptimizations);
        
        // Estimation du temps restant
        if (currentOptimization > 0 && startTime) {
            const elapsed = Date.now() - startTime;
            const avgTime = elapsed / currentOptimization;
            const remaining = (totalOptimizations - currentOptimization) * avgTime;
            const eta = formatTime(remaining);
            $('#progress-eta').text(`Temps restant estimé: ${eta}`);
        }
    }

    /**
     * Met à jour le statut de progression
     */
    function updateProgressStatus(status) {
        $('#progress-status').text(status);
    }

    /**
     * Termine l'optimisation
     */
    function finishOptimization() {
        isOptimizing = false;
        
        updateProgressStatus('Optimisation terminée !');
        showNotification('success', `Optimisation terminée ! ${currentOptimization} articles traités.`);
        
        setTimeout(function() {
            hideProgressBar();
            updateActionButtons();
        }, 3000);
        
        // Recharger les statistiques
        setTimeout(function() {
            location.reload();
        }, 5000);
    }

    /**
     * Arrête l'optimisation en cours
     */
    function stopOptimization() {
        if (!confirm('Êtes-vous sûr de vouloir arrêter l\'optimisation en cours ?')) {
            return;
        }
        
        isOptimizing = false;
        optimizationQueue = [];
        
        updateProgressStatus('Optimisation arrêtée par l\'utilisateur.');
        showNotification('info', 'Optimisation arrêtée.');
        
        setTimeout(function() {
            hideProgressBar();
            updateActionButtons();
        }, 2000);
    }

    /**
     * Gère le tri des colonnes
     */
    function handleSort() {
        const $th = $(this);
        const sortBy = $th.data('sort');
        const currentOrder = new URLSearchParams(window.location.search).get('order') || 'DESC';
        const newOrder = currentOrder === 'ASC' ? 'DESC' : 'ASC';

        // Construire la nouvelle URL
        const url = new URL(window.location);
        url.searchParams.set('orderby', sortBy);
        url.searchParams.set('order', newOrder);
        url.searchParams.set('paged', 1); // Retour à la première page

        window.location.href = url.toString();
    }

    /**
     * Sauvegarde les sélections dans le localStorage
     */
    function saveSelections() {
        if (selectedPosts.size > 0) {
            localStorage.setItem('smartseo_selected_posts', JSON.stringify(Array.from(selectedPosts)));
        } else {
            localStorage.removeItem('smartseo_selected_posts');
        }
    }

    /**
     * Restaure les sélections depuis le localStorage
     */
    function restoreSelections() {
        const saved = localStorage.getItem('smartseo_selected_posts');
        if (saved) {
            try {
                const postIds = JSON.parse(saved);
                postIds.forEach(function(postId) {
                    selectedPosts.add(postId);
                    $(`.post-checkbox[value="${postId}"]`).prop('checked', true);
                });
                updateSelectionInfo();
                updateSelectAllCheckbox();
                updateActionButtons();
            } catch (e) {
                localStorage.removeItem('smartseo_selected_posts');
            }
        }
    }

    /**
     * Vérifie l'état d'optimisation en cours
     */
    function checkOptimizationStatus() {
        // Vérifier s'il y a des articles en cours d'optimisation
        const inProgress = $('.content-row.in-progress').length;
        if (inProgress > 0) {
            // Il pourrait y avoir une optimisation en cours depuis une autre session
            // On pourrait implémenter une vérification via AJAX ici
        }
    }

    /**
     * Initialise les tooltips
     */
    function initTooltips() {
        // Ajouter des tooltips pour les éléments avec un attribut title
        $('[title]').each(function() {
            const $element = $(this);
            const title = $element.attr('title');

            $element.removeAttr('title').on('mouseenter', function() {
                showTooltip($element, title);
            }).on('mouseleave', function() {
                hideTooltip();
            });
        });
    }

    /**
     * Affiche un tooltip
     */
    function showTooltip($element, text) {
        const $tooltip = $('<div class="smartseo-tooltip"></div>').text(text);
        $('body').append($tooltip);

        const offset = $element.offset();
        const elementHeight = $element.outerHeight();

        $tooltip.css({
            position: 'absolute',
            top: offset.top + elementHeight + 5,
            left: offset.left,
            zIndex: 9999
        }).fadeIn(200);
    }

    /**
     * Cache le tooltip
     */
    function hideTooltip() {
        $('.smartseo-tooltip').fadeOut(200, function() {
            $(this).remove();
        });
    }

    /**
     * Affiche une notification
     */
    function showNotification(type, message, duration = 5000) {
        const $notification = $(`
            <div class="smartseo-notification notification-${type}">
                <span class="notification-icon"></span>
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `);

        // Ajouter au conteneur de notifications
        let $container = $('#smartseo-notifications');
        if ($container.length === 0) {
            $container = $('<div id="smartseo-notifications"></div>');
            $('body').append($container);
        }

        $container.append($notification);

        // Animation d'entrée
        setTimeout(() => $notification.addClass('show'), 100);

        // Fermeture automatique
        setTimeout(() => {
            $notification.removeClass('show');
            setTimeout(() => $notification.remove(), 300);
        }, duration);

        // Fermeture manuelle
        $notification.find('.notification-close').on('click', function() {
            $notification.removeClass('show');
            setTimeout(() => $notification.remove(), 300);
        });
    }

    /**
     * Retourne la classe CSS pour un score SEO
     */
    function getScoreClass(score) {
        if (score >= 80) return 'excellent';
        if (score >= 60) return 'good';
        if (score >= 40) return 'average';
        return 'poor';
    }

    /**
     * Formate un temps en millisecondes en format lisible
     */
    function formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    /**
     * Debounce function pour limiter les appels
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Styles pour les notifications
    const notificationStyles = `
        <style>
        #smartseo-notifications {
            position: fixed;
            top: 32px;
            right: 20px;
            z-index: 999999;
            max-width: 400px;
        }

        .smartseo-notification {
            display: flex;
            align-items: center;
            background: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-left: 4px solid #ddd;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .smartseo-notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .smartseo-notification.notification-success {
            border-left-color: #46b450;
        }

        .smartseo-notification.notification-error {
            border-left-color: #dc3232;
        }

        .smartseo-notification.notification-warning {
            border-left-color: #ffb900;
        }

        .smartseo-notification.notification-info {
            border-left-color: #00a0d2;
        }

        .notification-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 50%;
        }

        .notification-success .notification-icon {
            background-color: #46b450;
        }

        .notification-error .notification-icon {
            background-color: #dc3232;
        }

        .notification-warning .notification-icon {
            background-color: #ffb900;
        }

        .notification-info .notification-icon {
            background-color: #00a0d2;
        }

        .notification-message {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
        }

        .notification-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #666;
            padding: 0;
            margin-left: 10px;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-close:hover {
            color: #dc3232;
        }

        .smartseo-tooltip {
            background: #333;
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 200px;
            word-wrap: break-word;
            display: none;
        }

        #selected-count.has-selection {
            color: #0073aa;
            font-weight: bold;
            animation: pulse-selection 2s infinite;
        }

        @keyframes pulse-selection {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        </style>
    `;

    // Ajouter les styles
    $('head').append(notificationStyles);

})(jQuery);
