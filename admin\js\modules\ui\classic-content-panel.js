/**
 * Module de gestion du panneau de contenu pour l'éditeur classique
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire du panneau de contenu pour l'éditeur classique
     */
    const ClassicContentPanel = {
        /**
         * Initialise le gestionnaire du panneau de contenu
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Rend le panneau de contenu
         * @return {string} HTML du panneau
         */
        render: function() {
            // Créer la structure du panneau
            const $panel = $('<div class="smartseo-ai-panel"></div>');
            
            // Ajouter l'en-tête
            $panel.append('<div class="smartseo-ai-panel-header">' +
                '<h3>Génération de contenu</h3>' +
                '<p>Générez du contenu optimisé pour le SEO en un clic.</p>' +
                '</div>');
            
            // Ajouter le contenu
            const $content = $('<div class="smartseo-ai-panel-content"></div>');
            
            // Ajouter le champ de mot-clé
            $content.append('<div class="smartseo-ai-form-group">' +
                '<label for="smartseo-ai-focus-keyword">Mot-clé principal</label>' +
                '<input type="text" id="smartseo-ai-focus-keyword" class="widefat" placeholder="Entrez votre mot-clé principal">' +
                '</div>');
            
            // Ajouter les boutons de génération
            $content.append('<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-generate-title" data-type="title">Générer un titre</button>' +
                '<button type="button" class="button button-primary smartseo-ai-generate-meta" data-type="meta_description">Générer une meta description</button>' +
                '</div>');
            
            $content.append('<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-generate-intro" data-type="introduction">Générer une introduction</button>' +
                '<button type="button" class="button button-primary smartseo-ai-generate-conclusion" data-type="conclusion">Générer une conclusion</button>' +
                '</div>');
            
            $content.append('<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-generate-headings" data-type="h2_headings">Générer des titres H2</button>' +
                '<button type="button" class="button button-primary smartseo-ai-generate-paragraph" data-type="paragraph">Générer un paragraphe</button>' +
                '</div>');
            
            $content.append('<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-generate-article" data-type="full_article">Générer un article complet</button>' +
                '</div>');
            
            // Ajouter le conteneur de résultats
            $content.append('<div class="smartseo-ai-results-container" id="smartseo-ai-content-results">' +
                '<div class="smartseo-ai-loading" style="display: none;">' +
                '<span class="spinner is-active"></span>' +
                '<p>Génération en cours...</p>' +
                '</div>' +
                '<div class="smartseo-ai-results" style="display: none;">' +
                '<h4>Suggestions générées</h4>' +
                '<div class="smartseo-ai-results-content"></div>' +
                '</div>' +
                '</div>');
            
            // Ajouter le contenu au panneau
            $panel.append($content);
            
            return $panel;
        },

        /**
         * Affiche les résultats de génération de contenu
         * @param {Object} results Résultats de génération
         * @param {string} type    Type de contenu
         */
        showResults: function(results, type) {
            const $container = $('#smartseo-ai-content-results');
            const $loading = $container.find('.smartseo-ai-loading');
            const $results = $container.find('.smartseo-ai-results');
            const $content = $results.find('.smartseo-ai-results-content');
            
            // Masquer le chargement
            $loading.hide();
            
            // Vider le contenu précédent
            $content.empty();
            
            // Afficher les résultats
            $results.show();
            
            // Formater les résultats en fonction du type
            if (type === 'title' && results.titles) {
                const titles = results.titles;
                
                $content.append('<p>Voici quelques suggestions de titres :</p>');
                
                const $list = $('<ul class="smartseo-ai-suggestions-list"></ul>');
                
                titles.forEach(title => {
                    const $item = $('<li class="smartseo-ai-suggestion-item"></li>');
                    $item.append('<div class="smartseo-ai-suggestion-content">' + title + '</div>');
                    $item.append('<div class="smartseo-ai-suggestion-actions">' +
                        '<button class="button smartseo-ai-apply-suggestion" data-content="' + title + '" data-target="title">Appliquer</button>' +
                        '<button class="button smartseo-ai-copy-suggestion" data-content="' + title + '">Copier</button>' +
                        '</div>');
                    $list.append($item);
                });
                
                $content.append($list);
            } else if (type === 'meta_description' && results.descriptions) {
                const descriptions = results.descriptions;
                
                $content.append('<p>Voici quelques suggestions de meta descriptions :</p>');
                
                const $list = $('<ul class="smartseo-ai-suggestions-list"></ul>');
                
                descriptions.forEach(description => {
                    const $item = $('<li class="smartseo-ai-suggestion-item"></li>');
                    $item.append('<div class="smartseo-ai-suggestion-content">' + description + '</div>');
                    $item.append('<div class="smartseo-ai-suggestion-actions">' +
                        '<button class="button smartseo-ai-apply-suggestion" data-content="' + description + '" data-target="meta_description">Appliquer</button>' +
                        '<button class="button smartseo-ai-copy-suggestion" data-content="' + description + '">Copier</button>' +
                        '</div>');
                    $list.append($item);
                });
                
                $content.append($list);
            } else if (type === 'introduction' && results.introduction) {
                const introduction = results.introduction;
                
                $content.append('<p>Voici une suggestion d\'introduction :</p>');
                
                const $item = $('<div class="smartseo-ai-suggestion-item"></div>');
                $item.append('<div class="smartseo-ai-suggestion-content">' + introduction + '</div>');
                $item.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + introduction + '" data-target="introduction">Appliquer</button>' +
                    '<button class="button smartseo-ai-copy-suggestion" data-content="' + introduction + '">Copier</button>' +
                    '</div>');
                
                $content.append($item);
            } else if (type === 'conclusion' && results.conclusion) {
                const conclusion = results.conclusion;
                
                $content.append('<p>Voici une suggestion de conclusion :</p>');
                
                const $item = $('<div class="smartseo-ai-suggestion-item"></div>');
                $item.append('<div class="smartseo-ai-suggestion-content">' + conclusion + '</div>');
                $item.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + conclusion + '" data-target="conclusion">Appliquer</button>' +
                    '<button class="button smartseo-ai-copy-suggestion" data-content="' + conclusion + '">Copier</button>' +
                    '</div>');
                
                $content.append($item);
            } else if (type === 'h2_headings' && results.headings) {
                const headings = results.headings;
                
                $content.append('<p>Voici quelques suggestions de titres H2 :</p>');
                
                const $list = $('<ul class="smartseo-ai-suggestions-list"></ul>');
                
                // Joindre les titres en une seule chaîne pour l'application en bloc
                const headingsText = headings.join('\n');
                
                // Ajouter un bouton pour appliquer tous les titres
                $content.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + headingsText + '" data-target="h2_headings">Appliquer tous les titres</button>' +
                    '</div>');
                
                headings.forEach(heading => {
                    const $item = $('<li class="smartseo-ai-suggestion-item"></li>');
                    $item.append('<div class="smartseo-ai-suggestion-content">' + heading + '</div>');
                    $item.append('<div class="smartseo-ai-suggestion-actions">' +
                        '<button class="button smartseo-ai-apply-suggestion" data-content="' + heading + '" data-target="h2_headings">Appliquer</button>' +
                        '<button class="button smartseo-ai-copy-suggestion" data-content="' + heading + '">Copier</button>' +
                        '</div>');
                    $list.append($item);
                });
                
                $content.append($list);
            } else if (type === 'paragraph' && results.paragraph) {
                const paragraph = results.paragraph;
                
                $content.append('<p>Voici une suggestion de paragraphe :</p>');
                
                const $item = $('<div class="smartseo-ai-suggestion-item"></div>');
                $item.append('<div class="smartseo-ai-suggestion-content">' + paragraph + '</div>');
                $item.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + paragraph + '" data-target="paragraph">Appliquer</button>' +
                    '<button class="button smartseo-ai-copy-suggestion" data-content="' + paragraph + '">Copier</button>' +
                    '</div>');
                
                $content.append($item);
            } else if (type === 'full_article' && results.article) {
                const article = results.article;
                
                $content.append('<p>Voici une suggestion d\'article complet :</p>');
                
                const $item = $('<div class="smartseo-ai-suggestion-item"></div>');
                $item.append('<div class="smartseo-ai-suggestion-content">' + article + '</div>');
                $item.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + article + '" data-target="full_article">Appliquer</button>' +
                    '<button class="button smartseo-ai-copy-suggestion" data-content="' + article + '">Copier</button>' +
                    '</div>');
                
                $content.append($item);
            } else {
                // Afficher un message d'erreur
                $content.append('<p>Aucun résultat disponible.</p>');
            }
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.ClassicContentPanel = ClassicContentPanel;

})(jQuery);
