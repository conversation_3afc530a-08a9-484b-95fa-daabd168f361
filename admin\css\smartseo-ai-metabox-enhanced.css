/**
 * Styles améliorés pour la meta box SmartSEO AI
 */

/* Variables CSS */
:root {
    --smartseo-primary: #0073aa;
    --smartseo-success: #4caf50;
    --smartseo-warning: #ff9800;
    --smartseo-error: #f44336;
    --smartseo-info: #2196f3;
    --smartseo-gray-light: #f8f9fa;
    --smartseo-gray-medium: #e9ecef;
    --smartseo-gray-dark: #6c757d;
    --smartseo-border-radius: 8px;
    --smartseo-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --smartseo-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
    --smartseo-transition: all 0.3s ease;
}

/* Conteneur principal de la meta box */
.smartseo-ai-metabox-enhanced {
    padding: 0;
    margin: 0;
    background: #fff;
    border-radius: var(--smartseo-border-radius);
    overflow: hidden;
}

/* En-tête avec bouton d'optimisation et score */
.smartseo-ai-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 0;
}

.smartseo-ai-optimize-section {
    flex: 1;
}

/* Bouton d'optimisation amélioré */
.smartseo-ai-optimize-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: var(--smartseo-transition);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.smartseo-ai-optimize-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.smartseo-ai-optimize-btn:active {
    transform: translateY(0);
}

.smartseo-ai-optimize-btn.loading {
    pointer-events: none;
}

.smartseo-ai-optimize-btn.loading .smartseo-ai-btn-text {
    opacity: 0.7;
}

.smartseo-ai-optimize-btn.loading .smartseo-ai-btn-icon {
    display: none;
}

.smartseo-ai-optimize-btn.loading .smartseo-ai-btn-loader {
    display: block;
}

.smartseo-ai-btn-icon svg {
    width: 18px;
    height: 18px;
}

.smartseo-ai-btn-loader {
    display: none;
}

.smartseo-ai-btn-loader svg {
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Statut d'optimisation */
.smartseo-ai-status {
    margin-top: 10px;
    font-size: 13px;
    opacity: 0.9;
}

/* Affichage du score SEO */
.smartseo-ai-score-display {
    display: flex;
    align-items: center;
    gap: 15px;
}

.smartseo-ai-score-circle {
    position: relative;
    width: 80px;
    height: 80px;
}

.smartseo-ai-score-svg {
    transform: rotate(-90deg);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.smartseo-ai-score-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.smartseo-ai-score-number {
    display: block;
    font-size: 20px;
    font-weight: bold;
    line-height: 1;
}

.smartseo-ai-score-max {
    font-size: 12px;
    opacity: 0.8;
}

.smartseo-ai-score-label {
    text-align: left;
}

.smartseo-ai-score-title {
    display: block;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    opacity: 0.9;
}

.smartseo-ai-score-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: rgba(255, 255, 255, 0.2);
}

/* Placeholder pour le score */
.smartseo-ai-score-placeholder {
    display: flex;
    align-items: center;
    gap: 15px;
    opacity: 0.7;
}

.smartseo-ai-score-icon svg {
    width: 40px;
    height: 40px;
}

.smartseo-ai-score-placeholder-text {
    text-align: left;
}

.smartseo-ai-score-placeholder-text span {
    display: block;
    font-size: 13px;
    line-height: 1.3;
}

/* Sections */
.smartseo-ai-fields-section,
.smartseo-ai-advanced-section,
.smartseo-ai-opengraph-section,
.smartseo-ai-advice-section {
    padding: 25px;
    border-bottom: 1px solid var(--smartseo-gray-medium);
}

.smartseo-ai-advice-section {
    border-bottom: none;
}

.smartseo-ai-section-header {
    margin-bottom: 20px;
}

.smartseo-ai-section-header h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
}

.smartseo-ai-section-icon svg {
    width: 20px;
    height: 20px;
    color: var(--smartseo-primary);
}

.smartseo-ai-section-header p {
    margin: 0;
    font-size: 13px;
    color: var(--smartseo-gray-dark);
    line-height: 1.4;
}

/* Grille des champs */
.smartseo-ai-fields-grid {
    display: grid;
    gap: 25px;
}

.smartseo-ai-field-group {
    display: flex;
    flex-direction: column;
}

.smartseo-ai-field-group.smartseo-ai-field-full-width {
    grid-column: 1 / -1;
}

/* En-tête des champs */
.smartseo-ai-field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.smartseo-ai-field-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    font-size: 14px;
    color: #1d2327;
    margin: 0;
}

.smartseo-ai-field-icon svg {
    width: 16px;
    height: 16px;
    color: var(--smartseo-primary);
}

/* Compteurs de caractères */
.smartseo-ai-field-counter {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--smartseo-gray-dark);
    font-weight: 500;
}

.counter-number {
    font-weight: 600;
    color: var(--smartseo-primary);
}

.counter-separator {
    margin: 0 2px;
}

.counter-max {
    color: var(--smartseo-gray-dark);
}

.smartseo-ai-keywords-count {
    font-size: 12px;
    color: var(--smartseo-gray-dark);
    font-weight: 500;
}

/* Wrapper des champs */
.smartseo-ai-field-wrapper {
    position: relative;
    margin-bottom: 8px;
}

/* Champs de saisie */
.smartseo-ai-input,
.smartseo-ai-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--smartseo-gray-medium);
    border-radius: var(--smartseo-border-radius);
    font-size: 14px;
    line-height: 1.4;
    transition: var(--smartseo-transition);
    background: #fff;
    resize: vertical;
}

.smartseo-ai-input:focus,
.smartseo-ai-textarea:focus {
    border-color: var(--smartseo-primary);
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
    outline: none;
}

.smartseo-ai-input::placeholder,
.smartseo-ai-textarea::placeholder {
    color: #999;
    font-style: italic;
}

/* Barres de progression */
.smartseo-ai-field-progress {
    height: 3px;
    background: var(--smartseo-gray-light);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 4px;
}

.smartseo-ai-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--smartseo-success), var(--smartseo-info));
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

.smartseo-ai-progress-bar.warning {
    background: linear-gradient(90deg, var(--smartseo-warning), var(--smartseo-error));
}

/* Aide des champs */
.smartseo-ai-field-help {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    font-size: 12px;
    color: var(--smartseo-gray-dark);
    line-height: 1.4;
}

.smartseo-ai-help-icon {
    font-size: 14px;
    margin-top: 1px;
}

/* Tags des mots-clés */
.smartseo-ai-keywords-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
    min-height: 20px;
}

.smartseo-ai-keyword-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: var(--smartseo-primary);
    color: white;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.smartseo-ai-keyword-tag .remove-tag {
    cursor: pointer;
    font-weight: bold;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.smartseo-ai-keyword-tag .remove-tag:hover {
    opacity: 1;
}

/* Boutons d'action */
.smartseo-ai-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: var(--smartseo-primary);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--smartseo-transition);
}

.smartseo-ai-action-btn:hover {
    background: #005a87;
    transform: translateY(-1px);
}

.smartseo-ai-action-btn .smartseo-ai-btn-icon svg {
    width: 14px;
    height: 14px;
}

/* Aperçu d'image */
.smartseo-ai-image-preview {
    margin-top: 10px;
    border-radius: var(--smartseo-border-radius);
    overflow: hidden;
    border: 1px solid var(--smartseo-gray-medium);
}

.smartseo-ai-image-preview img {
    width: 100%;
    height: auto;
    max-height: 200px;
    object-fit: cover;
    display: block;
}

/* Section des conseils */
.smartseo-ai-advice-content {
    background: var(--smartseo-gray-light);
    border-radius: var(--smartseo-border-radius);
    padding: 20px;
    border-left: 4px solid var(--smartseo-info);
}

.smartseo-ai-advice-content ul {
    margin: 0;
    padding-left: 20px;
}

.smartseo-ai-advice-content li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* États de validation */
.smartseo-ai-field-wrapper.valid .smartseo-ai-input,
.smartseo-ai-field-wrapper.valid .smartseo-ai-textarea {
    border-color: var(--smartseo-success);
}

.smartseo-ai-field-wrapper.warning .smartseo-ai-input,
.smartseo-ai-field-wrapper.warning .smartseo-ai-textarea {
    border-color: var(--smartseo-warning);
}

.smartseo-ai-field-wrapper.error .smartseo-ai-input,
.smartseo-ai-field-wrapper.error .smartseo-ai-textarea {
    border-color: var(--smartseo-error);
}

/* Responsive */
@media (max-width: 768px) {
    .smartseo-ai-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .smartseo-ai-score-display {
        justify-content: center;
    }
    
    .smartseo-ai-fields-section,
    .smartseo-ai-advanced-section,
    .smartseo-ai-opengraph-section,
    .smartseo-ai-advice-section {
        padding: 20px 15px;
    }
    
    .smartseo-ai-field-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.smartseo-ai-field-group {
    animation: fadeInUp 0.4s ease forwards;
}

.smartseo-ai-field-group:nth-child(1) { animation-delay: 0.1s; }
.smartseo-ai-field-group:nth-child(2) { animation-delay: 0.2s; }
.smartseo-ai-field-group:nth-child(3) { animation-delay: 0.3s; }
.smartseo-ai-field-group:nth-child(4) { animation-delay: 0.4s; }
