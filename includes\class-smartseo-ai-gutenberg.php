<?php
/**
 * Classe pour l'intégration avec l'éditeur <PERSON>
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'intégration avec <PERSON>nberg
 */
class SmartSEO_AI_Gutenberg {

    /**
     * Constructeur
     */
    public function __construct() {
        // Enregistrer les scripts et styles pour Gutenberg
        add_action( 'enqueue_block_editor_assets', array( $this, 'enqueue_block_editor_assets' ) );
    }

    /**
     * Enregistre les scripts et styles pour l'éditeur de blocs
     */
    public function enqueue_block_editor_assets() {
        // Styles pour le panneau latéral
        wp_enqueue_style(
            'smartseo-ai-gutenberg',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-gutenberg.css',
            array(),
            SMARTSEO_AI_VERSION
        );

        // Script pour le panneau latéral
        wp_enqueue_script(
            'smartseo-ai-gutenberg',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-gutenberg.js',
            array( 'wp-blocks', 'wp-element', 'wp-components', 'wp-editor', 'wp-data', 'wp-plugins', 'wp-edit-post', 'wp-i18n', 'wp-api-fetch' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Localize script
        wp_localize_script(
            'smartseo-ai-gutenberg',
            'smartseoAiGutenberg',
            array(
                'restUrl' => rest_url( 'smartseo-ai/v1' ),
                'nonce' => wp_create_nonce( 'wp_rest' ),
                'i18n' => array(
                    'panelTitle' => __( 'SmartSEO AI', 'smartseo-ai' ),
                    'optimizeButton' => __( 'Optimiser avec l\'IA', 'smartseo-ai' ),
                    'optimizing' => __( 'Optimisation en cours...', 'smartseo-ai' ),
                    'success' => __( 'Optimisation réussie !', 'smartseo-ai' ),
                    'error' => __( 'Erreur lors de l\'optimisation.', 'smartseo-ai' ),
                    'seoTitle' => __( 'Titre SEO', 'smartseo-ai' ),
                    'metaDescription' => __( 'Meta Description', 'smartseo-ai' ),
                    'keywords' => __( 'Mots-clés', 'smartseo-ai' ),
                    'seoSlug' => __( 'Slug URL optimisé', 'smartseo-ai' ),
                    'applySlug' => __( 'Appliquer le slug', 'smartseo-ai' ),
                    'openGraph' => __( 'Données Open Graph', 'smartseo-ai' ),
                    'ogTitle' => __( 'Titre OG', 'smartseo-ai' ),
                    'ogDescription' => __( 'Description OG', 'smartseo-ai' ),
                    'ogImage' => __( 'Image OG', 'smartseo-ai' ),
                    'selectImage' => __( 'Sélectionner une image', 'smartseo-ai' ),
                    'seoAdvice' => __( 'Conseils d\'optimisation SEO', 'smartseo-ai' ),
                    'seoScore' => __( 'Score SEO', 'smartseo-ai' ),
                    'charactersLimit' => __( 'caractères', 'smartseo-ai' ),
                ),
            )
        );
    }
}
