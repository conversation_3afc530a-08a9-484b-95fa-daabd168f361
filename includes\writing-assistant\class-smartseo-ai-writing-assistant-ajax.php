<?php
/**
 * Classe de gestion des AJAX pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les AJAX de l'Assistant de Rédaction SEO
 */
class SmartSEO_AI_Writing_Assistant_Ajax {

    /**
     * Gestionnaire des AJAX de génération de contenu
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Content_Generator
     */
    private $content_generator;

    /**
     * Gestionnaire des AJAX d'analyse de contenu
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Content_Analyzer
     */
    private $content_analyzer;

    /**
     * Gestionnaire des AJAX d'analyse de mots-clés
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Keyword_Analyzer
     */
    private $keyword_analyzer;

    /**
     * Gestionnaire des AJAX de tendances
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Trends
     */
    private $trends;

    /**
     * Initialise le gestionnaire des AJAX
     */
    public function init() {
        // Charger les dépendances
        $this->load_dependencies();

        // Initialiser les composants
        $this->initialize_components();

        // Ajouter les hooks
        $this->add_hooks();
    }

    /**
     * Charge les dépendances
     */
    private function load_dependencies() {
        // Charger les classes nécessaires
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/class-smartseo-ai-writing-assistant-ajax-content-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/class-smartseo-ai-writing-assistant-ajax-content-analyzer.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/class-smartseo-ai-writing-assistant-ajax-keyword-analyzer.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/class-smartseo-ai-writing-assistant-ajax-trends.php';
    }

    /**
     * Initialise les composants
     */
    private function initialize_components() {
        $this->content_generator = new SmartSEO_AI_Writing_Assistant_Ajax_Content_Generator();
        $this->content_analyzer = new SmartSEO_AI_Writing_Assistant_Ajax_Content_Analyzer();
        $this->keyword_analyzer = new SmartSEO_AI_Writing_Assistant_Ajax_Keyword_Analyzer();
        $this->trends = new SmartSEO_AI_Writing_Assistant_Ajax_Trends();
    }

    /**
     * Ajoute les hooks
     */
    private function add_hooks() {
        // Hooks pour la génération de contenu
        add_action( 'wp_ajax_smartseo_ai_generate_content', array( $this->content_generator, 'generate_content' ) );
        add_action( 'wp_ajax_smartseo_ai_generate_meta_description', array( $this->content_generator, 'generate_meta_description' ) );
        add_action( 'wp_ajax_smartseo_ai_optimize_all', array( $this->content_generator, 'optimize_all' ) );
        add_action( 'wp_ajax_smartseo_ai_check_plagiarism', array( $this->content_generator, 'check_plagiarism' ) );

        // Hooks pour l'analyse de contenu
        add_action( 'wp_ajax_smartseo_ai_analyze_content', array( $this->content_analyzer, 'analyze_content' ) );

        // Hooks pour l'analyse de mots-clés
        add_action( 'wp_ajax_smartseo_ai_analyze_keywords', array( $this->keyword_analyzer, 'analyze_keywords' ) );
        add_action( 'wp_ajax_smartseo_ai_search_related_topics', array( $this->keyword_analyzer, 'search_related_topics' ) );

        // Hooks pour les tendances
        add_action( 'wp_ajax_smartseo_ai_get_trends', array( $this->trends, 'get_trends' ) );
    }
}
