/**
 * Styles pour l'administration du plugin SmartSEO AI
 */

/* <PERSON> gén<PERSON>ux */
.smartseo-ai-container {
    max-width: 1200px;
    margin: 20px 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.smartseo-ai-header {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: #fff;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.smartseo-ai-header h1 {
    color: #fff;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.smartseo-ai-header .smartseo-ai-version {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
}

.smartseo-ai-content {
    padding: 20px;
}

/* Styles pour les métaboxes */
.smartseo-ai-metabox {
    background: #fff;
    border-radius: 5px;
    overflow: hidden;
}

.smartseo-ai-optimize-button-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

#smartseo-ai-optimize-button {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    border: none;
    color: #fff;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#smartseo-ai-optimize-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

#smartseo-ai-optimize-button .dashicons {
    margin-right: 8px;
}

.smartseo-ai-status {
    margin-left: 15px;
    font-style: italic;
    color: #666;
}

.smartseo-ai-score-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.smartseo-ai-score {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    color: #fff;
    margin-right: 15px;
}

.smartseo-ai-score-good {
    background: linear-gradient(135deg, #43a047 0%, #2e7d32 100%);
}

.smartseo-ai-score-average {
    background: linear-gradient(135deg, #fb8c00 0%, #ef6c00 100%);
}

.smartseo-ai-score-poor {
    background: linear-gradient(135deg, #e53935 0%, #c62828 100%);
}

.smartseo-ai-score-none {
    background: #f5f5f5;
    color: #757575;
}

.smartseo-ai-score-label {
    font-weight: 600;
    color: #333;
}

.smartseo-ai-fields-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.smartseo-ai-field {
    margin-bottom: 15px;
}

.smartseo-ai-field label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.smartseo-ai-field input[type="text"],
.smartseo-ai-field textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.smartseo-ai-field .description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.smartseo-ai-char-count {
    float: right;
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

.smartseo-ai-char-count.over-limit {
    color: #e53935;
}

.smartseo-ai-advice {
    background: #f8f9fa;
    border-left: 4px solid #2575fc;
    padding: 15px;
    border-radius: 0 4px 4px 0;
}

.smartseo-ai-advice ul {
    margin: 0;
    padding-left: 20px;
}

.smartseo-ai-advice li {
    margin-bottom: 8px;
}

/* Styles pour le tableau de bord */
.smartseo-ai-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.smartseo-ai-stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.smartseo-ai-stat-card h3 {
    margin-top: 0;
    color: #333;
    font-size: 16px;
}

.smartseo-ai-stat-card .stat-value {
    font-size: 28px;
    font-weight: 700;
    color: #2575fc;
    margin: 10px 0;
}

.smartseo-ai-posts-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.smartseo-ai-posts-table th {
    background: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #ddd;
}

.smartseo-ai-posts-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
}

.smartseo-ai-posts-table tr:hover {
    background: #f9f9f9;
}

.smartseo-ai-posts-table .column-score {
    width: 100px;
    text-align: center;
}

.smartseo-ai-posts-table .column-actions {
    width: 150px;
    text-align: right;
}

.smartseo-ai-score-indicator {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
    color: #fff;
    text-align: center;
    min-width: 60px;
}

/* Styles pour les colonnes dans la liste des articles */
.column-smartseo_ai_score {
    width: 120px;
}

/* Animation de chargement */
@keyframes smartseo-ai-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.smartseo-ai-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: smartseo-ai-spin 1s ease-in-out infinite;
    margin-right: 10px;
}

/* Styles pour les paramètres */
.smartseo-ai-settings-form {
    max-width: 800px;
}

.smartseo-ai-settings-form .form-table th {
    width: 250px;
}

.smartseo-ai-api-key-field {
    position: relative;
}

.smartseo-ai-toggle-visibility {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
}

/* Styles pour les notifications */
.smartseo-ai-notice {
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
    display: flex;
    align-items: center;
}

.smartseo-ai-notice-success {
    background-color: #e8f5e9;
    border-left: 4px solid #43a047;
    color: #2e7d32;
}

.smartseo-ai-notice-error {
    background-color: #ffebee;
    border-left: 4px solid #e53935;
    color: #c62828;
}

.smartseo-ai-notice-warning {
    background-color: #fff8e1;
    border-left: 4px solid #ffb300;
    color: #ff8f00;
}

.smartseo-ai-notice-info {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
    color: #1976d2;
}

.smartseo-ai-notice .dashicons {
    margin-right: 10px;
    font-size: 20px;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .smartseo-ai-fields-container {
        grid-template-columns: 1fr;
    }

    .smartseo-ai-dashboard-stats {
        grid-template-columns: 1fr;
    }
}

/* Styles pour les onglets */
.smartseo-ai-tabs {
    margin-top: 20px;
}

.smartseo-ai-tabs-nav {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: 1px solid #ccc;
}

.smartseo-ai-tab-nav {
    margin: 0 5px 0 0;
}

.smartseo-ai-tab-nav a {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border: 1px solid #ccc;
    border-bottom: none;
    border-radius: 5px 5px 0 0;
    text-decoration: none;
    color: #555;
    font-weight: 500;
    transition: all 0.2s ease;
}

.smartseo-ai-tab-nav.active a {
    background-color: #fff;
    color: #2271b1;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
}

.smartseo-ai-tab-nav a:hover {
    background-color: #fff;
    color: #2271b1;
}

.smartseo-ai-tab-nav .dashicons {
    margin-right: 5px;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.smartseo-ai-tab-content {
    padding: 20px;
    background-color: #fff;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 5px 5px;
}
