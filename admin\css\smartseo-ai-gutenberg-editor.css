/**
 * Styles spécifiques pour l'éditeur G<PERSON>nberg
 *
 * @package SmartSEO_AI
 */

/* Styles pour la barre latérale */
.edit-post-sidebar .smartseo-ai-panel {
    margin: 0;
    padding: 0;
}

.edit-post-sidebar .smartseo-ai-panel-header {
    margin-bottom: 16px;
}

.edit-post-sidebar .smartseo-ai-panel-header h3 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.edit-post-sidebar .smartseo-ai-panel-header p {
    margin-top: 0;
    margin-bottom: 0;
    color: #666;
    font-size: 12px;
}

.edit-post-sidebar .smartseo-ai-panel-content {
    margin-bottom: 16px;
}

/* Styles pour les onglets */
.edit-post-sidebar .smartseo-ai-tabs {
    margin: -16px;
    margin-bottom: 16px;
}

.edit-post-sidebar .smartseo-ai-tabs-nav {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: 1px solid #e2e4e7;
}

.edit-post-sidebar .smartseo-ai-tab-nav {
    flex: 1;
    padding: 8px 0;
    margin-bottom: -1px;
    text-align: center;
    cursor: pointer;
    font-size: 13px;
}

.edit-post-sidebar .smartseo-ai-tab-nav.is-active {
    box-shadow: inset 0 -3px #007cba;
    font-weight: 600;
}

.edit-post-sidebar .smartseo-ai-tab-panel {
    padding: 16px;
}

/* Styles pour les boutons */
.edit-post-sidebar .smartseo-ai-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.edit-post-sidebar .smartseo-ai-button-group button {
    flex: 1;
    min-width: 120px;
}

/* Styles pour les résultats */
.edit-post-sidebar .smartseo-ai-results-container {
    margin-top: 16px;
}

.edit-post-sidebar .smartseo-ai-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    text-align: center;
}

.edit-post-sidebar .smartseo-ai-results h4 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 600;
}

/* Styles pour forcer l'affichage */
.edit-post-sidebar .smartseo-ai-panel {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Styles pour les notifications */
.edit-post-sidebar .smartseo-ai-notification-container {
    margin-bottom: 8px;
}

.edit-post-sidebar .smartseo-ai-notification {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.edit-post-sidebar .smartseo-ai-notification-success {
    border-left: 3px solid #46b450;
}

.edit-post-sidebar .smartseo-ai-notification-error {
    border-left: 3px solid #dc3232;
}

.edit-post-sidebar .smartseo-ai-notification-info {
    border-left: 3px solid #00a0d2;
}

.edit-post-sidebar .smartseo-ai-notification-warning {
    border-left: 3px solid #ffb900;
}

.edit-post-sidebar .smartseo-ai-notification-icon {
    margin-right: 8px;
}

.edit-post-sidebar .smartseo-ai-notification-message {
    flex: 1;
    font-size: 12px;
}

.edit-post-sidebar .smartseo-ai-notification-close {
    cursor: pointer;
    font-size: 14px;
    line-height: 1;
    color: #666;
}

/* Styles pour les modales */
.smartseo-ai-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.smartseo-ai-modal {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    overflow: auto;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.smartseo-ai-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #ddd;
}

.smartseo-ai-modal-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.smartseo-ai-modal-close {
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    color: #666;
    background: none;
    border: none;
    padding: 0;
}

.smartseo-ai-modal-body {
    padding: 12px;
    max-height: 500px;
    overflow-y: auto;
}

.smartseo-ai-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px;
    border-top: 1px solid #ddd;
}
