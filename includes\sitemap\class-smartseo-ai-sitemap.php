<?php
/**
 * Classe principale du Sitemap XML
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère le Sitemap XML
 */
class SmartSEO_AI_Sitemap {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_Sitemap
     */
    private static $instance = null;

    /**
     * Options du sitemap
     *
     * @var array
     */
    private $options;

    /**
     * Constructeur
     */
    private function __construct() {
        // Charger les options
        $this->options = get_option( 'smartseo_ai_sitemap_options', array(
            'enabled' => 'no',
            'include_posts' => 'yes',
            'include_pages' => 'yes',
            'include_products' => 'yes',
            'include_categories' => 'yes',
            'include_tags' => 'yes',
            'include_custom_taxonomies' => 'yes',
            'include_custom_post_types' => 'yes',
            'include_media' => 'no',
            'use_index' => 'no',
            'max_entries' => 1000,
            'frequencies' => array(
                'post' => 'weekly',
                'page' => 'monthly',
                'product' => 'daily',
                'category' => 'monthly',
                'post_tag' => 'monthly',
                'attachment' => 'yearly',
            ),
            'priorities' => array(
                'post' => 0.7,
                'page' => 0.8,
                'product' => 0.9,
                'category' => 0.6,
                'post_tag' => 0.5,
                'attachment' => 0.3,
            ),
        ) );

        // Charger les dépendances
        $this->load_dependencies();

        // Ajouter les hooks
        $this->add_hooks();
    }

    /**
     * Récupère l'instance de la classe
     *
     * @return SmartSEO_AI_Sitemap Instance de la classe.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Charge les dépendances
     */
    private function load_dependencies() {
        // Charger les classes nécessaires
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/class-smartseo-ai-sitemap-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/class-smartseo-ai-sitemap-settings.php';
    }

    /**
     * Ajoute les hooks
     */
    private function add_hooks() {
        // Vérifier si le sitemap est activé
        if ( ! isset( $this->options['enabled'] ) || 'yes' !== $this->options['enabled'] ) {
            return;
        }

        // Ajouter les règles de réécriture
        add_action( 'init', array( $this, 'add_rewrite_rules' ) );

        // Gérer les requêtes de sitemap
        add_action( 'template_redirect', array( $this, 'handle_sitemap_requests' ) );

        // Vider le cache de réécriture lors de la mise à jour des options
        add_action( 'update_option_smartseo_ai_sitemap_options', array( $this, 'flush_rewrite_rules' ) );

        // Ajouter le sitemap à la liste des sitemaps de WordPress (WP 5.5+)
        add_filter( 'wp_sitemaps_enabled', '__return_false' );

        // Ajouter le lien du sitemap dans l'en-tête
        add_action( 'wp_head', array( $this, 'add_sitemap_link' ) );

        // Ajouter le sitemap au robots.txt
        add_filter( 'robots_txt', array( $this, 'add_sitemap_to_robots_txt' ), 10, 2 );

        // Régénérer le sitemap lors de la publication d'un article
        add_action( 'save_post', array( $this, 'regenerate_sitemap_on_save' ), 10, 3 );
    }

    /**
     * Ajoute les règles de réécriture pour le sitemap
     */
    public function add_rewrite_rules() {
        // Règle pour le sitemap principal
        add_rewrite_rule( '^sitemap\.xml$', 'index.php?smartseo_ai_sitemap=index', 'top' );

        // Règle pour le sitemap d'index (si activé)
        if ( isset( $this->options['use_index'] ) && 'yes' === $this->options['use_index'] ) {
            add_rewrite_rule( '^sitemap_index\.xml$', 'index.php?smartseo_ai_sitemap=index', 'top' );
        }

        // Règles pour les sitemaps spécifiques
        add_rewrite_rule( '^sitemap-([^/]+)\.xml$', 'index.php?smartseo_ai_sitemap=$matches[1]', 'top' );

        // Ajouter la variable de requête
        add_rewrite_tag( '%smartseo_ai_sitemap%', '([^/]+)' );
    }

    /**
     * Gère les requêtes de sitemap
     */
    public function handle_sitemap_requests() {
        global $wp_query;

        // Vérifier si c'est une requête de sitemap
        if ( ! isset( $wp_query->query_vars['smartseo_ai_sitemap'] ) ) {
            return;
        }

        // Récupérer le type de sitemap
        $sitemap_type = $wp_query->query_vars['smartseo_ai_sitemap'];

        // Instancier le générateur de sitemap
        $generator = new SmartSEO_AI_Sitemap_Generator( $this->options );

        // Générer le sitemap en fonction du type
        if ( 'index' === $sitemap_type ) {
            // Générer le sitemap d'index
            $xml = $generator->generate_index_sitemap();
        } else {
            // Générer un sitemap spécifique
            $xml = $generator->generate_sitemap( $sitemap_type );
        }

        // Envoyer les en-têtes
        header( 'Content-Type: application/xml; charset=UTF-8' );
        header( 'X-Robots-Tag: noindex, follow' );

        // Envoyer le contenu
        echo $xml;

        // Terminer l'exécution
        exit;
    }

    /**
     * Vide le cache de réécriture
     */
    public function flush_rewrite_rules() {
        flush_rewrite_rules();
    }

    /**
     * Ajoute le lien du sitemap dans l'en-tête
     */
    public function add_sitemap_link() {
        echo '<link rel="sitemap" type="application/xml" title="Sitemap" href="' . esc_url( home_url( 'sitemap.xml' ) ) . '" />' . "\n";
    }

    /**
     * Ajoute le sitemap au robots.txt
     *
     * @param string $output Contenu actuel du robots.txt.
     * @param bool   $public Si le site est public ou non.
     * @return string Contenu modifié du robots.txt.
     */
    public function add_sitemap_to_robots_txt( $output, $public ) {
        if ( $public ) {
            $output .= "\n# SmartSEO AI Sitemap\n";
            $output .= "Sitemap: " . esc_url( home_url( 'sitemap.xml' ) ) . "\n";
        }
        return $output;
    }

    /**
     * Régénère le sitemap lors de la publication d'un article
     *
     * @param int     $post_id ID de l'article.
     * @param WP_Post $post    Objet de l'article.
     * @param bool    $update  Si c'est une mise à jour ou non.
     */
    public function regenerate_sitemap_on_save( $post_id, $post, $update ) {
        // Vérifier si c'est une révision ou un auto-save
        if ( wp_is_post_revision( $post_id ) || wp_is_post_autosave( $post_id ) ) {
            return;
        }

        // Vérifier si le statut est publié
        if ( 'publish' !== $post->post_status ) {
            return;
        }

        // Vérifier si le type de contenu est inclus dans le sitemap
        $post_type = $post->post_type;
        $include_key = 'include_' . $post_type . 's';

        // Si c'est un type de contenu personnalisé
        if ( ! in_array( $post_type, array( 'post', 'page', 'product' ), true ) ) {
            $include_key = 'include_custom_post_types';
        }

        // Vérifier si ce type de contenu est inclus
        if ( isset( $this->options[ $include_key ] ) && 'yes' === $this->options[ $include_key ] ) {
            // Régénérer le sitemap
            $generator = new SmartSEO_AI_Sitemap_Generator( $this->options );
            $generator->generate_sitemap( $post_type );

            // Régénérer le sitemap d'index si nécessaire
            if ( isset( $this->options['use_index'] ) && 'yes' === $this->options['use_index'] ) {
                $generator->generate_index_sitemap();
            }
        }
    }

    /**
     * Récupère l'URL du sitemap
     *
     * @return string URL du sitemap.
     */
    public function get_sitemap_url() {
        return home_url( 'sitemap.xml' );
    }

    /**
     * Vérifie si le sitemap est valide
     *
     * @return bool|WP_Error True si valide, WP_Error sinon.
     */
    public function validate_sitemap() {
        // Récupérer l'URL du sitemap
        $sitemap_url = $this->get_sitemap_url();

        // Faire une requête au sitemap
        $response = wp_remote_get( $sitemap_url );

        // Vérifier si la requête a réussi
        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Vérifier le code de statut
        $status_code = wp_remote_retrieve_response_code( $response );
        if ( 200 !== $status_code ) {
            return new WP_Error( 'sitemap_error', sprintf( __( 'Le sitemap a retourné un code d\'erreur : %d', 'smartseo-ai' ), $status_code ) );
        }

        // Vérifier le contenu
        $body = wp_remote_retrieve_body( $response );
        if ( empty( $body ) ) {
            return new WP_Error( 'sitemap_empty', __( 'Le sitemap est vide.', 'smartseo-ai' ) );
        }

        // Vérifier si c'est un XML valide
        libxml_use_internal_errors( true );
        $xml = simplexml_load_string( $body );
        $errors = libxml_get_errors();
        libxml_clear_errors();

        if ( false === $xml || ! empty( $errors ) ) {
            return new WP_Error( 'sitemap_invalid_xml', __( 'Le sitemap n\'est pas un XML valide.', 'smartseo-ai' ) );
        }

        return true;
    }

    /**
     * Suggère des contenus à inclure dans le sitemap
     *
     * @return array Suggestions de contenus.
     */
    public function suggest_content() {
        // Vérifier si l'IA est configurée
        $provider = get_option( 'smartseo_ai_provider', 'openai' );
        if ( 'openai' === $provider ) {
            $api_key = get_option( 'smartseo_ai_openai_api_key', '' );
            if ( empty( $api_key ) ) {
                return array();
            }
            $ai_api = new SmartSEO_AI_API();
        } else {
            $api_key = get_option( 'smartseo_ai_gemini_api_key', '' );
            if ( empty( $api_key ) ) {
                return array();
            }
            $ai_api = new SmartSEO_AI_Gemini();
        }

        // Récupérer les contenus actuellement inclus dans le sitemap
        $generator = new SmartSEO_AI_Sitemap_Generator( $this->options );
        $included_urls = $generator->get_all_urls();

        // Récupérer tous les contenus du site
        $all_urls = $this->get_all_site_urls();

        // Trouver les contenus non inclus
        $missing_urls = array_diff( $all_urls, $included_urls );

        // Si tous les contenus sont inclus, retourner un tableau vide
        if ( empty( $missing_urls ) ) {
            return array();
        }

        // Limiter le nombre d'URLs à analyser
        $missing_urls = array_slice( $missing_urls, 0, 10 );

        // Construire le prompt pour l'IA
        $prompt = "Voici une liste d'URLs de mon site qui ne sont pas incluses dans mon sitemap XML :\n\n";
        foreach ( $missing_urls as $url ) {
            $prompt .= "- $url\n";
        }
        $prompt .= "\nPour chaque URL, évaluez son importance pour le SEO et indiquez si elle devrait être incluse dans le sitemap. Donnez une note d'importance de 1 à 10 et une brève explication pour chaque URL.";

        // Appeler l'API IA
        $response = $ai_api->generate_content( $prompt );

        // Traiter la réponse
        if ( is_wp_error( $response ) ) {
            return array();
        }

        // Formater la réponse
        $suggestions = array(
            'missing_urls' => $missing_urls,
            'ai_analysis' => $response,
        );

        return $suggestions;
    }

    /**
     * Récupère toutes les URLs du site
     *
     * @return array Toutes les URLs du site.
     */
    private function get_all_site_urls() {
        $urls = array();

        // Récupérer les articles
        $posts = get_posts( array(
            'post_type' => 'post',
            'posts_per_page' => -1,
            'post_status' => 'publish',
        ) );

        foreach ( $posts as $post ) {
            $urls[] = get_permalink( $post->ID );
        }

        // Récupérer les pages
        $pages = get_posts( array(
            'post_type' => 'page',
            'posts_per_page' => -1,
            'post_status' => 'publish',
        ) );

        foreach ( $pages as $page ) {
            $urls[] = get_permalink( $page->ID );
        }

        // Récupérer les produits WooCommerce si disponible
        if ( class_exists( 'WooCommerce' ) ) {
            $products = get_posts( array(
                'post_type' => 'product',
                'posts_per_page' => -1,
                'post_status' => 'publish',
            ) );

            foreach ( $products as $product ) {
                $urls[] = get_permalink( $product->ID );
            }
        }

        // Récupérer les catégories
        $categories = get_categories( array(
            'hide_empty' => false,
        ) );

        foreach ( $categories as $category ) {
            $urls[] = get_category_link( $category->term_id );
        }

        // Récupérer les tags
        $tags = get_tags( array(
            'hide_empty' => false,
        ) );

        foreach ( $tags as $tag ) {
            $urls[] = get_tag_link( $tag->term_id );
        }

        return $urls;
    }
}
