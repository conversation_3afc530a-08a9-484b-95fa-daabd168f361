<?php
/**
 * Classe du générateur de Sitemap XML
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui génère le Sitemap XML
 */
class SmartSEO_AI_Sitemap_Generator {

    /**
     * Options du sitemap
     *
     * @var array
     */
    private $options;

    /**
     * Constructeur
     *
     * @param array $options Options du sitemap.
     */
    public function __construct( $options ) {
        $this->options = $options;
    }

    /**
     * Génère le sitemap d'index
     *
     * @return string Contenu XML du sitemap d'index.
     */
    public function generate_index_sitemap() {
        // Créer l'objet XML
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></sitemapindex>');

        // Ajouter les sitemaps spécifiques
        $this->add_sitemap_to_index( $xml, 'post' );
        $this->add_sitemap_to_index( $xml, 'page' );

        // Ajouter les produits WooCommerce si disponible
        if ( class_exists( 'WooCommerce' ) && 'yes' === $this->options['include_products'] ) {
            $this->add_sitemap_to_index( $xml, 'product' );
        }

        // Ajouter les taxonomies
        if ( 'yes' === $this->options['include_categories'] ) {
            $this->add_sitemap_to_index( $xml, 'category' );
        }

        if ( 'yes' === $this->options['include_tags'] ) {
            $this->add_sitemap_to_index( $xml, 'post_tag' );
        }

        // Ajouter les types de contenu personnalisés
        if ( 'yes' === $this->options['include_custom_post_types'] ) {
            $custom_post_types = $this->get_custom_post_types();
            foreach ( $custom_post_types as $post_type ) {
                $this->add_sitemap_to_index( $xml, $post_type );
            }
        }

        // Ajouter les taxonomies personnalisées
        if ( 'yes' === $this->options['include_custom_taxonomies'] ) {
            $custom_taxonomies = $this->get_custom_taxonomies();
            foreach ( $custom_taxonomies as $taxonomy ) {
                $this->add_sitemap_to_index( $xml, $taxonomy );
            }
        }

        // Ajouter les médias
        if ( 'yes' === $this->options['include_media'] ) {
            $this->add_sitemap_to_index( $xml, 'attachment' );
        }

        // Retourner le XML
        return $xml->asXML();
    }

    /**
     * Ajoute un sitemap au sitemap d'index
     *
     * @param SimpleXMLElement $xml        Objet XML du sitemap d'index.
     * @param string           $sitemap_id Identifiant du sitemap.
     */
    private function add_sitemap_to_index( $xml, $sitemap_id ) {
        // Créer l'élément sitemap
        $sitemap = $xml->addChild( 'sitemap' );

        // Ajouter l'URL du sitemap
        $sitemap->addChild( 'loc', esc_url( home_url( 'sitemap-' . $sitemap_id . '.xml' ) ) );

        // Ajouter la date de dernière modification
        $sitemap->addChild( 'lastmod', date( 'c' ) );
    }

    /**
     * Génère un sitemap spécifique
     *
     * @param string $sitemap_id Identifiant du sitemap.
     * @return string Contenu XML du sitemap.
     */
    public function generate_sitemap( $sitemap_id ) {
        // Créer l'objet XML
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd"></urlset>');

        // Générer le sitemap en fonction du type
        switch ( $sitemap_id ) {
            case 'post':
                $this->add_posts_to_sitemap( $xml, 'post' );
                break;

            case 'page':
                $this->add_posts_to_sitemap( $xml, 'page' );
                break;

            case 'product':
                if ( class_exists( 'WooCommerce' ) ) {
                    $this->add_posts_to_sitemap( $xml, 'product' );
                }
                break;

            case 'category':
                $this->add_terms_to_sitemap( $xml, 'category' );
                break;

            case 'post_tag':
                $this->add_terms_to_sitemap( $xml, 'post_tag' );
                break;

            case 'attachment':
                $this->add_posts_to_sitemap( $xml, 'attachment' );
                break;

            default:
                // Vérifier si c'est un type de contenu personnalisé
                $custom_post_types = $this->get_custom_post_types();
                if ( in_array( $sitemap_id, $custom_post_types, true ) ) {
                    $this->add_posts_to_sitemap( $xml, $sitemap_id );
                }

                // Vérifier si c'est une taxonomie personnalisée
                $custom_taxonomies = $this->get_custom_taxonomies();
                if ( in_array( $sitemap_id, $custom_taxonomies, true ) ) {
                    $this->add_terms_to_sitemap( $xml, $sitemap_id );
                }
                break;
        }

        // Retourner le XML
        return $xml->asXML();
    }

    /**
     * Ajoute des articles au sitemap
     *
     * @param SimpleXMLElement $xml       Objet XML du sitemap.
     * @param string           $post_type Type de contenu.
     */
    private function add_posts_to_sitemap( $xml, $post_type ) {
        // Récupérer les articles
        $posts = get_posts( array(
            'post_type'      => $post_type,
            'post_status'    => 'publish',
            'posts_per_page' => $this->options['max_entries'],
        ) );

        // Ajouter les articles au sitemap
        foreach ( $posts as $post ) {
            // Créer l'élément URL
            $url = $xml->addChild( 'url' );

            // Ajouter l'URL
            $url->addChild( 'loc', esc_url( get_permalink( $post->ID ) ) );

            // Ajouter la date de dernière modification
            $url->addChild( 'lastmod', date( 'c', strtotime( $post->post_modified_gmt ) ) );

            // Ajouter la fréquence de changement
            $url->addChild( 'changefreq', $this->get_post_changefreq( $post_type ) );

            // Ajouter la priorité
            $url->addChild( 'priority', $this->get_post_priority( $post_type ) );
        }
    }

    /**
     * Ajoute des termes au sitemap
     *
     * @param SimpleXMLElement $xml      Objet XML du sitemap.
     * @param string           $taxonomy Taxonomie.
     */
    private function add_terms_to_sitemap( $xml, $taxonomy ) {
        // Récupérer les termes
        $terms = get_terms( array(
            'taxonomy'   => $taxonomy,
            'hide_empty' => false,
            'number'     => $this->options['max_entries'],
        ) );

        // Ajouter les termes au sitemap
        foreach ( $terms as $term ) {
            // Créer l'élément URL
            $url = $xml->addChild( 'url' );

            // Ajouter l'URL
            $url->addChild( 'loc', esc_url( get_term_link( $term ) ) );

            // Ajouter la date de dernière modification
            $url->addChild( 'lastmod', date( 'c' ) );

            // Ajouter la fréquence de changement
            $url->addChild( 'changefreq', $this->get_term_changefreq( $taxonomy ) );

            // Ajouter la priorité
            $url->addChild( 'priority', $this->get_term_priority( $taxonomy ) );
        }
    }

    /**
     * Récupère la fréquence de changement d'un type de contenu
     *
     * @param string $post_type Type de contenu.
     * @return string Fréquence de changement.
     */
    private function get_post_changefreq( $post_type ) {
        // Vérifier si une fréquence est définie pour ce type de contenu
        if ( isset( $this->options['frequencies'][ $post_type ] ) ) {
            return $this->options['frequencies'][ $post_type ];
        }

        // Valeurs par défaut
        $default_frequencies = array(
            'post'       => 'weekly',
            'page'       => 'monthly',
            'product'    => 'daily',
            'attachment' => 'yearly',
        );

        // Retourner la valeur par défaut ou 'monthly' si non définie
        return isset( $default_frequencies[ $post_type ] ) ? $default_frequencies[ $post_type ] : 'monthly';
    }

    /**
     * Récupère la priorité d'un type de contenu
     *
     * @param string $post_type Type de contenu.
     * @return float Priorité.
     */
    private function get_post_priority( $post_type ) {
        // Vérifier si une priorité est définie pour ce type de contenu
        if ( isset( $this->options['priorities'][ $post_type ] ) ) {
            return $this->options['priorities'][ $post_type ];
        }

        // Valeurs par défaut
        $default_priorities = array(
            'post'       => 0.7,
            'page'       => 0.8,
            'product'    => 0.9,
            'attachment' => 0.3,
        );

        // Retourner la valeur par défaut ou 0.5 si non définie
        return isset( $default_priorities[ $post_type ] ) ? $default_priorities[ $post_type ] : 0.5;
    }

    /**
     * Récupère la fréquence de changement d'une taxonomie
     *
     * @param string $taxonomy Taxonomie.
     * @return string Fréquence de changement.
     */
    private function get_term_changefreq( $taxonomy ) {
        // Vérifier si une fréquence est définie pour cette taxonomie
        if ( isset( $this->options['frequencies'][ $taxonomy ] ) ) {
            return $this->options['frequencies'][ $taxonomy ];
        }

        // Valeurs par défaut
        $default_frequencies = array(
            'category' => 'monthly',
            'post_tag' => 'monthly',
        );

        // Retourner la valeur par défaut ou 'monthly' si non définie
        return isset( $default_frequencies[ $taxonomy ] ) ? $default_frequencies[ $taxonomy ] : 'monthly';
    }

    /**
     * Récupère la priorité d'une taxonomie
     *
     * @param string $taxonomy Taxonomie.
     * @return float Priorité.
     */
    private function get_term_priority( $taxonomy ) {
        // Vérifier si une priorité est définie pour cette taxonomie
        if ( isset( $this->options['priorities'][ $taxonomy ] ) ) {
            return $this->options['priorities'][ $taxonomy ];
        }

        // Valeurs par défaut
        $default_priorities = array(
            'category' => 0.6,
            'post_tag' => 0.5,
        );

        // Retourner la valeur par défaut ou 0.5 si non définie
        return isset( $default_priorities[ $taxonomy ] ) ? $default_priorities[ $taxonomy ] : 0.5;
    }

    /**
     * Récupère les types de contenu personnalisés
     *
     * @return array Types de contenu personnalisés.
     */
    private function get_custom_post_types() {
        // Récupérer tous les types de contenu
        $post_types = get_post_types( array(
            'public'   => true,
            '_builtin' => false,
        ), 'names' );

        // Exclure les produits WooCommerce (gérés séparément)
        if ( isset( $post_types['product'] ) ) {
            unset( $post_types['product'] );
        }

        return $post_types;
    }

    /**
     * Récupère les taxonomies personnalisées
     *
     * @return array Taxonomies personnalisées.
     */
    private function get_custom_taxonomies() {
        // Récupérer toutes les taxonomies
        $taxonomies = get_taxonomies( array(
            'public'   => true,
            '_builtin' => false,
        ), 'names' );

        // Exclure les taxonomies WooCommerce (gérées séparément)
        $woocommerce_taxonomies = array( 'product_cat', 'product_tag' );
        foreach ( $woocommerce_taxonomies as $taxonomy ) {
            if ( isset( $taxonomies[ $taxonomy ] ) ) {
                unset( $taxonomies[ $taxonomy ] );
            }
        }

        return $taxonomies;
    }

    /**
     * Récupère toutes les URLs du sitemap
     *
     * @return array Toutes les URLs du sitemap.
     */
    public function get_all_urls() {
        $urls = array();

        // Récupérer les URLs des articles
        if ( 'yes' === $this->options['include_posts'] ) {
            $urls = array_merge( $urls, $this->get_post_type_urls( 'post' ) );
        }

        // Récupérer les URLs des pages
        if ( 'yes' === $this->options['include_pages'] ) {
            $urls = array_merge( $urls, $this->get_post_type_urls( 'page' ) );
        }

        // Récupérer les URLs des produits WooCommerce
        if ( 'yes' === $this->options['include_products'] && class_exists( 'WooCommerce' ) ) {
            $urls = array_merge( $urls, $this->get_post_type_urls( 'product' ) );
        }

        // Récupérer les URLs des catégories
        if ( 'yes' === $this->options['include_categories'] ) {
            $urls = array_merge( $urls, $this->get_taxonomy_urls( 'category' ) );
        }

        // Récupérer les URLs des tags
        if ( 'yes' === $this->options['include_tags'] ) {
            $urls = array_merge( $urls, $this->get_taxonomy_urls( 'post_tag' ) );
        }

        // Récupérer les URLs des types de contenu personnalisés
        if ( 'yes' === $this->options['include_custom_post_types'] ) {
            $custom_post_types = $this->get_custom_post_types();
            foreach ( $custom_post_types as $post_type ) {
                $urls = array_merge( $urls, $this->get_post_type_urls( $post_type ) );
            }
        }

        // Récupérer les URLs des taxonomies personnalisées
        if ( 'yes' === $this->options['include_custom_taxonomies'] ) {
            $custom_taxonomies = $this->get_custom_taxonomies();
            foreach ( $custom_taxonomies as $taxonomy ) {
                $urls = array_merge( $urls, $this->get_taxonomy_urls( $taxonomy ) );
            }
        }

        // Récupérer les URLs des médias
        if ( 'yes' === $this->options['include_media'] ) {
            $urls = array_merge( $urls, $this->get_post_type_urls( 'attachment' ) );
        }

        return $urls;
    }

    /**
     * Récupère les URLs d'un type de contenu
     *
     * @param string $post_type Type de contenu.
     * @return array URLs du type de contenu.
     */
    private function get_post_type_urls( $post_type ) {
        $urls = array();

        // Récupérer les articles
        $posts = get_posts( array(
            'post_type'      => $post_type,
            'post_status'    => 'publish',
            'posts_per_page' => -1,
        ) );

        // Ajouter les URLs
        foreach ( $posts as $post ) {
            $urls[] = get_permalink( $post->ID );
        }

        return $urls;
    }

    /**
     * Récupère les URLs d'une taxonomie
     *
     * @param string $taxonomy Taxonomie.
     * @return array URLs de la taxonomie.
     */
    private function get_taxonomy_urls( $taxonomy ) {
        $urls = array();

        // Récupérer les termes
        $terms = get_terms( array(
            'taxonomy'   => $taxonomy,
            'hide_empty' => false,
        ) );

        // Ajouter les URLs
        foreach ( $terms as $term ) {
            $urls[] = get_term_link( $term );
        }

        return $urls;
    }
}
