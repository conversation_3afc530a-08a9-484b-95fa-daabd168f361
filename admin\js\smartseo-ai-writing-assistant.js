/**
 * Script principal pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    // Modules
    const UIManager = window.SmartSEOAI.UIManager;
    const ContentGenerator = window.SmartSEOAI.ContentGenerator;
    const KeywordAnalyzer = window.SmartSEOAI.KeywordAnalyzer;
    const LiveAnalyzer = window.SmartSEOAI.LiveAnalyzer;
    const TrendsManager = window.SmartSEOAI.TrendsManager;

    /**
     * Assistant de Rédaction SEO
     */
    const WritingAssistant = {
        /**
         * Initialise l'Assistant de Rédaction SEO
         */
        init: function() {
            // Initialiser les modules
            UIManager.init();
            ContentGenerator.init();
            KeywordAnalyzer.init();
            LiveAnalyzer.init();
            TrendsManager.init();

            // Ajouter les écouteurs d'événements
            this.bindEvents();

            // Initialiser l'interface
            this.initInterface();
        },

        /**
         * Ajoute les écouteurs d'événements
         */
        bindEvents: function() {
            // Événements pour la génération de contenu
            $(document).on('click', '.smartseo-ai-generate-title', this.handleGenerateTitle);
            $(document).on('click', '.smartseo-ai-generate-meta', this.handleGenerateMeta);
            $(document).on('click', '.smartseo-ai-generate-intro', this.handleGenerateIntro);
            $(document).on('click', '.smartseo-ai-generate-conclusion', this.handleGenerateConclusion);
            $(document).on('click', '.smartseo-ai-generate-headings', this.handleGenerateHeadings);
            $(document).on('click', '.smartseo-ai-generate-paragraph', this.handleGenerateParagraph);
            $(document).on('click', '.smartseo-ai-generate-article', this.handleGenerateArticle);

            // Événements pour l'analyse des mots-clés
            $(document).on('click', '.smartseo-ai-analyze-keywords', this.handleAnalyzeKeywords);
            $(document).on('click', '.smartseo-ai-get-trends', this.handleGetTrends);

            // Événements pour l'analyse en temps réel
            $(document).on('input', '#content, #title', this.handleContentChange);
            $(document).on('click', '.smartseo-ai-analyze-now', this.handleAnalyzeNow);

            // Événements pour l'optimisation automatique
            $(document).on('click', '.smartseo-ai-optimize-all', this.handleOptimizeAll);

            // Événements pour la vérification de plagiat
            $(document).on('click', '.smartseo-ai-check-plagiarism', this.handleCheckPlagiarism);

            // Événements pour l'application des suggestions
            $(document).on('click', '.smartseo-ai-apply-suggestion', this.handleApplySuggestion);
            $(document).on('click', '.smartseo-ai-copy-suggestion', this.handleCopySuggestion);
        },

        /**
         * Initialise l'interface
         */
        initInterface: function() {
            // Vérifier si nous sommes sur une page d'édition
            if ($('#post').length > 0 || $('.edit-post-layout').length > 0) {
                // Ajouter l'interface de l'Assistant de Rédaction SEO
                UIManager.addWritingAssistantInterface();
            }
        },

        /**
         * Gère la génération de titre
         * @param {Event} e Événement
         */
        handleGenerateTitle: function(e) {
            e.preventDefault();
            
            const postId = $('#post_ID').val();
            const keyword = $('.smartseo-ai-focus-keyword').val();
            const topic = $('#title').val() || keyword;
            
            ContentGenerator.generateContent('title', postId, keyword, topic);
        },

        /**
         * Gère la génération de meta description
         * @param {Event} e Événement
         */
        handleGenerateMeta: function(e) {
            e.preventDefault();
            
            const postId = $('#post_ID').val();
            const keyword = $('.smartseo-ai-focus-keyword').val();
            const topic = $('#title').val() || keyword;
            const content = $('#content').val();
            
            ContentGenerator.generateContent('meta_description', postId, keyword, topic, content);
        },

        /**
         * Gère la génération d'introduction
         * @param {Event} e Événement
         */
        handleGenerateIntro: function(e) {
            e.preventDefault();
            
            const postId = $('#post_ID').val();
            const keyword = $('.smartseo-ai-focus-keyword').val();
            const topic = $('#title').val() || keyword;
            
            ContentGenerator.generateContent('introduction', postId, keyword, topic);
        },

        /**
         * Gère la génération de conclusion
         * @param {Event} e Événement
         */
        handleGenerateConclusion: function(e) {
            e.preventDefault();
            
            const postId = $('#post_ID').val();
            const keyword = $('.smartseo-ai-focus-keyword').val();
            const topic = $('#title').val() || keyword;
            const content = $('#content').val();
            
            ContentGenerator.generateContent('conclusion', postId, keyword, topic, content);
        },

        /**
         * Gère la génération de titres
         * @param {Event} e Événement
         */
        handleGenerateHeadings: function(e) {
            e.preventDefault();
            
            const postId = $('#post_ID').val();
            const keyword = $('.smartseo-ai-focus-keyword').val();
            const topic = $('#title').val() || keyword;
            
            ContentGenerator.generateContent('h2_headings', postId, keyword, topic);
        },

        /**
         * Gère la génération de paragraphe
         * @param {Event} e Événement
         */
        handleGenerateParagraph: function(e) {
            e.preventDefault();
            
            const postId = $('#post_ID').val();
            const keyword = $('.smartseo-ai-focus-keyword').val();
            const topic = $('#title').val() || keyword;
            
            ContentGenerator.generateContent('paragraph', postId, keyword, topic);
        },

        /**
         * Gère la génération d'article complet
         * @param {Event} e Événement
         */
        handleGenerateArticle: function(e) {
            e.preventDefault();
            
            const postId = $('#post_ID').val();
            const keyword = $('.smartseo-ai-focus-keyword').val();
            const topic = $('#title').val() || keyword;
            
            ContentGenerator.generateContent('full_article', postId, keyword, topic);
        },

        /**
         * Gère l'analyse des mots-clés
         * @param {Event} e Événement
         */
        handleAnalyzeKeywords: function(e) {
            e.preventDefault();
            
            const postId = $('#post_ID').val();
            const content = $('#content').val();
            const keyword = $('.smartseo-ai-focus-keyword').val();
            
            KeywordAnalyzer.analyzeKeywords(postId, content, keyword);
        },

        /**
         * Gère la récupération des tendances
         * @param {Event} e Événement
         */
        handleGetTrends: function(e) {
            e.preventDefault();
            
            const keyword = $('.smartseo-ai-focus-keyword').val();
            
            TrendsManager.getTrends(keyword);
        },

        /**
         * Gère le changement de contenu
         */
        handleContentChange: function() {
            // Déclencher l'analyse en temps réel après un délai
            LiveAnalyzer.scheduleAnalysis();
        },

        /**
         * Gère l'analyse immédiate
         * @param {Event} e Événement
         */
        handleAnalyzeNow: function(e) {
            e.preventDefault();
            
            LiveAnalyzer.analyzeNow();
        },

        /**
         * Gère l'optimisation automatique
         * @param {Event} e Événement
         */
        handleOptimizeAll: function(e) {
            e.preventDefault();
            
            const postId = $('#post_ID').val();
            const keyword = $('.smartseo-ai-focus-keyword').val();
            
            ContentGenerator.optimizeAll(postId, keyword);
        },

        /**
         * Gère la vérification de plagiat
         * @param {Event} e Événement
         */
        handleCheckPlagiarism: function(e) {
            e.preventDefault();
            
            const content = $('#content').val();
            
            ContentGenerator.checkPlagiarism(content);
        },

        /**
         * Gère l'application d'une suggestion
         * @param {Event} e Événement
         */
        handleApplySuggestion: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const content = $button.data('content');
            const target = $button.data('target');
            
            UIManager.applySuggestion(content, target);
        },

        /**
         * Gère la copie d'une suggestion
         * @param {Event} e Événement
         */
        handleCopySuggestion: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const content = $button.data('content');
            
            UIManager.copySuggestion(content);
        }
    };

    // Initialiser l'Assistant de Rédaction SEO au chargement du document
    $(document).ready(function() {
        // Créer l'espace de noms global s'il n'existe pas
        window.SmartSEOAI = window.SmartSEOAI || {};
        
        // Initialiser l'Assistant de Rédaction SEO
        WritingAssistant.init();
    });

})(jQuery);
