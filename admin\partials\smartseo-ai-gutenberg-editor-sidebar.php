<?php
/**
 * Affiche la barre latérale de l'Assistant de Rédaction SEO pour l'éditeur Gutenberg
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<script type="text/javascript">
    (function(wp) {
        var registerPlugin = wp.plugins.registerPlugin;
        var PluginSidebar = wp.editPost.PluginSidebar;
        var PluginSidebarMoreMenuItem = wp.editPost.PluginSidebarMoreMenuItem;
        var __ = wp.i18n.__;
        var el = wp.element.createElement;
        var Fragment = wp.element.Fragment;
        var TabPanel = wp.components.TabPanel;
        var Button = wp.components.Button;
        var TextControl = wp.components.TextControl;
        var TextareaControl = wp.components.TextareaControl;
        var PanelBody = wp.components.PanelBody;
        var Spinner = wp.components.Spinner;
        var withSelect = wp.data.withSelect;
        var withDispatch = wp.data.withDispatch;
        var compose = wp.compose.compose;

        // Composant pour l'onglet Contenu
        var ContentTab = function(props) {
            return el(
                'div',
                { className: 'smartseo-ai-panel' },
                el(
                    'div',
                    { className: 'smartseo-ai-panel-header' },
                    el('h3', {}, __('Génération de contenu', 'smartseo-ai')),
                    el('p', {}, __('Générez du contenu optimisé pour le SEO.', 'smartseo-ai'))
                ),
                el(
                    'div',
                    { className: 'smartseo-ai-panel-content' },
                    el(
                        'div',
                        { className: 'smartseo-ai-form-group' },
                        el(
                            'label',
                            { htmlFor: 'smartseo-ai-focus-keyword' },
                            __('Mot-clé principal', 'smartseo-ai')
                        ),
                        el(
                            TextControl,
                            {
                                id: 'smartseo-ai-focus-keyword',
                                placeholder: __('Entrez votre mot-clé principal', 'smartseo-ai'),
                                value: props.focusKeyword,
                                onChange: props.onFocusKeywordChange
                            }
                        )
                    ),
                    el(
                        'div',
                        { className: 'smartseo-ai-button-group' },
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-generate-title',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.ContentGenerator) {
                                        window.SmartSEOAI.ContentGenerator.generateContent('title', props.postId, props.focusKeyword);
                                    }
                                }
                            },
                            __('Générer un titre', 'smartseo-ai')
                        ),
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-generate-meta',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.ContentGenerator) {
                                        window.SmartSEOAI.ContentGenerator.generateContent('meta_description', props.postId, props.focusKeyword);
                                    }
                                }
                            },
                            __('Générer une meta description', 'smartseo-ai')
                        )
                    ),
                    el(
                        'div',
                        { className: 'smartseo-ai-button-group' },
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-generate-intro',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.ContentGenerator) {
                                        window.SmartSEOAI.ContentGenerator.generateContent('introduction', props.postId, props.focusKeyword);
                                    }
                                }
                            },
                            __('Générer une introduction', 'smartseo-ai')
                        ),
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-generate-conclusion',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.ContentGenerator) {
                                        window.SmartSEOAI.ContentGenerator.generateContent('conclusion', props.postId, props.focusKeyword);
                                    }
                                }
                            },
                            __('Générer une conclusion', 'smartseo-ai')
                        )
                    ),
                    el(
                        'div',
                        { className: 'smartseo-ai-button-group' },
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-generate-headings',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.ContentGenerator) {
                                        window.SmartSEOAI.ContentGenerator.generateContent('h2_headings', props.postId, props.focusKeyword);
                                    }
                                }
                            },
                            __('Générer des titres H2', 'smartseo-ai')
                        ),
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-generate-paragraph',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.ContentGenerator) {
                                        window.SmartSEOAI.ContentGenerator.generateContent('paragraph', props.postId, props.focusKeyword);
                                    }
                                }
                            },
                            __('Générer un paragraphe', 'smartseo-ai')
                        )
                    ),
                    el(
                        'div',
                        { className: 'smartseo-ai-button-group' },
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-generate-article',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.ContentGenerator) {
                                        window.SmartSEOAI.ContentGenerator.generateContent('full_article', props.postId, props.focusKeyword);
                                    }
                                }
                            },
                            __('Générer un article complet', 'smartseo-ai')
                        )
                    )
                ),
                el(
                    'div',
                    {
                        id: 'smartseo-ai-content-results',
                        className: 'smartseo-ai-results-container'
                    },
                    el(
                        'div',
                        {
                            className: 'smartseo-ai-loading',
                            style: { display: 'none' }
                        },
                        el(Spinner, {}),
                        el('p', {}, __('Génération en cours...', 'smartseo-ai'))
                    ),
                    el(
                        'div',
                        {
                            className: 'smartseo-ai-results',
                            style: { display: 'none' }
                        },
                        el('h4', {}, __('Résultats de la génération', 'smartseo-ai')),
                        el('div', { className: 'smartseo-ai-results-content' })
                    )
                )
            );
        };

        // Composant pour l'onglet Mots-clés
        var KeywordsTab = function(props) {
            return el(
                'div',
                { className: 'smartseo-ai-panel' },
                el(
                    'div',
                    { className: 'smartseo-ai-panel-header' },
                    el('h3', {}, __('Analyse de mots-clés', 'smartseo-ai')),
                    el('p', {}, __('Analysez et trouvez les meilleurs mots-clés pour votre contenu.', 'smartseo-ai'))
                ),
                el(
                    'div',
                    { className: 'smartseo-ai-panel-content' },
                    el(
                        'div',
                        { className: 'smartseo-ai-form-group' },
                        el(
                            'label',
                            { htmlFor: 'smartseo-ai-focus-keyword-analysis' },
                            __('Mot-clé principal', 'smartseo-ai')
                        ),
                        el(
                            TextControl,
                            {
                                id: 'smartseo-ai-focus-keyword-analysis',
                                placeholder: __('Entrez votre mot-clé principal', 'smartseo-ai'),
                                value: props.focusKeyword,
                                onChange: props.onFocusKeywordChange
                            }
                        )
                    ),
                    el(
                        'div',
                        { className: 'smartseo-ai-button-group' },
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-analyze-keywords',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.KeywordAnalyzer) {
                                        window.SmartSEOAI.KeywordAnalyzer.analyzeKeywords(props.postId, null, props.focusKeyword);
                                    }
                                }
                            },
                            __('Analyser les mots-clés', 'smartseo-ai')
                        )
                    )
                ),
                el(
                    'div',
                    {
                        id: 'smartseo-ai-keyword-results',
                        className: 'smartseo-ai-results-container'
                    },
                    el(
                        'div',
                        {
                            className: 'smartseo-ai-loading',
                            style: { display: 'none' }
                        },
                        el(Spinner, {}),
                        el('p', {}, __('Analyse en cours...', 'smartseo-ai'))
                    ),
                    el(
                        'div',
                        {
                            className: 'smartseo-ai-results',
                            style: { display: 'none' }
                        },
                        el('h4', {}, __('Résultats de l\'analyse', 'smartseo-ai')),
                        el('div', { className: 'smartseo-ai-results-content' })
                    )
                )
            );
        };

        // Composant pour l'onglet Analyse
        var AnalysisTab = function(props) {
            return el(
                'div',
                { className: 'smartseo-ai-panel' },
                el(
                    'div',
                    { className: 'smartseo-ai-panel-header' },
                    el('h3', {}, __('Analyse SEO en temps réel', 'smartseo-ai')),
                    el('p', {}, __('Analysez votre contenu en temps réel pour optimiser le SEO.', 'smartseo-ai'))
                ),
                el(
                    'div',
                    { className: 'smartseo-ai-panel-content' },
                    el(
                        'div',
                        { className: 'smartseo-ai-button-group' },
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-analyze-now',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.LiveAnalyzer) {
                                        window.SmartSEOAI.LiveAnalyzer.analyzeNow();
                                    }
                                }
                            },
                            __('Analyser maintenant', 'smartseo-ai')
                        ),
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-optimize-all',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.ContentGenerator) {
                                        window.SmartSEOAI.ContentGenerator.optimizeAll(props.postId, props.focusKeyword);
                                    }
                                }
                            },
                            __('Optimiser automatiquement', 'smartseo-ai')
                        )
                    ),
                    el(
                        'div',
                        { className: 'smartseo-ai-button-group' },
                        el(
                            Button,
                            {
                                className: 'smartseo-ai-check-plagiarism',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.ContentGenerator) {
                                        var content = wp.data.select('core/editor').getEditedPostContent();
                                        window.SmartSEOAI.ContentGenerator.checkPlagiarism(content);
                                    }
                                }
                            },
                            __('Vérifier la non-duplication', 'smartseo-ai')
                        )
                    )
                ),
                el(
                    'div',
                    {
                        id: 'smartseo-ai-analysis-results',
                        className: 'smartseo-ai-results-container'
                    },
                    el(
                        'div',
                        {
                            className: 'smartseo-ai-loading',
                            style: { display: 'none' }
                        },
                        el(Spinner, {}),
                        el('p', {}, __('Analyse en cours...', 'smartseo-ai'))
                    ),
                    el(
                        'div',
                        {
                            className: 'smartseo-ai-results',
                            style: { display: 'none' }
                        },
                        el('h4', {}, __('Résultats de l\'analyse', 'smartseo-ai')),
                        el('div', { className: 'smartseo-ai-results-content' })
                    )
                )
            );
        };

        // Composant pour l'onglet Tendances
        var TrendsTab = function(props) {
            return el(
                'div',
                { className: 'smartseo-ai-panel' },
                el(
                    'div',
                    { className: 'smartseo-ai-panel-header' },
                    el('h3', {}, __('Tendances et sujets populaires', 'smartseo-ai')),
                    el('p', {}, __('Découvrez les sujets tendances liés à votre mot-clé.', 'smartseo-ai'))
                ),
                el(
                    'div',
                    { className: 'smartseo-ai-panel-content' },
                    el(
                        'div',
                        { className: 'smartseo-ai-form-group' },
                        el(
                            'label',
                            { htmlFor: 'smartseo-ai-trends-keyword' },
                            __('Mot-clé pour les tendances', 'smartseo-ai')
                        ),
                        el(
                            TextControl,
                            {
                                id: 'smartseo-ai-trends-keyword',
                                placeholder: __('Entrez un mot-clé', 'smartseo-ai'),
                                value: props.focusKeyword,
                                onChange: props.onFocusKeywordChange
                            }
                        )
                    ),
                    el(
                        'div',
                        { className: 'smartseo-ai-button-group' },
                        el(
                            Button,
                            {
                                isPrimary: true,
                                className: 'smartseo-ai-get-trends',
                                onClick: function() {
                                    if (window.SmartSEOAI && window.SmartSEOAI.TrendsManager) {
                                        window.SmartSEOAI.TrendsManager.getTrends(props.focusKeyword);
                                    }
                                }
                            },
                            __('Obtenir les tendances', 'smartseo-ai')
                        )
                    )
                ),
                el(
                    'div',
                    {
                        id: 'smartseo-ai-trends-results',
                        className: 'smartseo-ai-results-container'
                    },
                    el(
                        'div',
                        {
                            className: 'smartseo-ai-loading',
                            style: { display: 'none' }
                        },
                        el(Spinner, {}),
                        el('p', {}, __('Recherche des tendances...', 'smartseo-ai'))
                    ),
                    el(
                        'div',
                        {
                            className: 'smartseo-ai-results',
                            style: { display: 'none' }
                        },
                        el('h4', {}, __('Sujets tendances', 'smartseo-ai')),
                        el('div', { className: 'smartseo-ai-results-content' })
                    )
                )
            );
        };

        // Composant principal
        var SmartSEOAISidebar = compose(
            withSelect(function(select) {
                return {
                    postId: select('core/editor').getCurrentPostId(),
                    focusKeyword: select('core/editor').getEditedPostAttribute('meta').smartseo_ai_focus_keyword || ''
                };
            }),
            withDispatch(function(dispatch) {
                return {
                    onFocusKeywordChange: function(value) {
                        dispatch('core/editor').editPost({
                            meta: {
                                smartseo_ai_focus_keyword: value
                            }
                        });
                    }
                };
            })
        )(function(props) {
            return el(
                Fragment,
                {},
                el(
                    PluginSidebarMoreMenuItem,
                    {
                        target: 'smartseo-ai-sidebar'
                    },
                    __('Assistant de Rédaction SEO', 'smartseo-ai')
                ),
                el(
                    PluginSidebar,
                    {
                        name: 'smartseo-ai-sidebar',
                        title: __('Assistant de Rédaction SEO', 'smartseo-ai')
                    },
                    el(
                        TabPanel,
                        {
                            className: 'smartseo-ai-tabs',
                            activeClass: 'is-active',
                            tabs: [
                                {
                                    name: 'content',
                                    title: __('Contenu', 'smartseo-ai'),
                                    className: 'smartseo-ai-tab-content'
                                },
                                {
                                    name: 'keywords',
                                    title: __('Mots-clés', 'smartseo-ai'),
                                    className: 'smartseo-ai-tab-keywords'
                                },
                                {
                                    name: 'analysis',
                                    title: __('Analyse', 'smartseo-ai'),
                                    className: 'smartseo-ai-tab-analysis'
                                },
                                {
                                    name: 'trends',
                                    title: __('Tendances', 'smartseo-ai'),
                                    className: 'smartseo-ai-tab-trends'
                                }
                            ]
                        },
                        function(tab) {
                            if (tab.name === 'content') {
                                return el(ContentTab, props);
                            } else if (tab.name === 'keywords') {
                                return el(KeywordsTab, props);
                            } else if (tab.name === 'analysis') {
                                return el(AnalysisTab, props);
                            } else if (tab.name === 'trends') {
                                return el(TrendsTab, props);
                            }
                        }
                    )
                )
            );
        });

        // Enregistrer le plugin
        registerPlugin('smartseo-ai-sidebar', {
            render: SmartSEOAISidebar
        });
    })(window.wp);
</script>
