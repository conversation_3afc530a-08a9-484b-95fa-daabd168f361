/**
 * Script de débogage pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    console.log('SmartSEO AI Debug: Script de débogage chargé');

    $(document).ready(function() {
        console.log('SmartSEO AI Debug: Document prêt');
        
        // Vérifier si les modules sont chargés
        if (window.SmartSEOAI) {
            console.log('SmartSEO AI Debug: Namespace global SmartSEOAI trouvé');
            
            // Vérifier les modules individuels
            if (window.SmartSEOAI.UIManager) {
                console.log('SmartSEO AI Debug: Module UIManager trouvé');
            } else {
                console.error('SmartSEO AI Debug: Module UIManager NON trouvé');
            }
            
            if (window.SmartSEOAI.ContentGenerator) {
                console.log('SmartSEO AI Debug: Module ContentGenerator trouvé');
            } else {
                console.error('SmartSEO AI Debug: Module ContentGenerator NON trouvé');
            }
            
            if (window.SmartSEOAI.KeywordAnalyzer) {
                console.log('SmartSEO AI Debug: Module KeywordAnalyzer trouvé');
            } else {
                console.error('SmartSEO AI Debug: Module KeywordAnalyzer NON trouvé');
            }
            
            if (window.SmartSEOAI.LiveAnalyzer) {
                console.log('SmartSEO AI Debug: Module LiveAnalyzer trouvé');
            } else {
                console.error('SmartSEO AI Debug: Module LiveAnalyzer NON trouvé');
            }
            
            if (window.SmartSEOAI.TrendsManager) {
                console.log('SmartSEO AI Debug: Module TrendsManager trouvé');
            } else {
                console.error('SmartSEO AI Debug: Module TrendsManager NON trouvé');
            }
        } else {
            console.error('SmartSEO AI Debug: Namespace global SmartSEOAI NON trouvé');
        }
        
        // Vérifier si nous sommes dans l'éditeur Gutenberg
        if (window.wp && window.wp.data && window.wp.data.select('core/editor')) {
            console.log('SmartSEO AI Debug: Éditeur Gutenberg détecté');
            
            // Vérifier si le panneau latéral est enregistré
            if ($('.edit-post-sidebar .smartseo-ai-panel').length > 0) {
                console.log('SmartSEO AI Debug: Panneau latéral trouvé dans Gutenberg');
            } else {
                console.error('SmartSEO AI Debug: Panneau latéral NON trouvé dans Gutenberg');
            }
        } else if ($('#post').length > 0) {
            console.log('SmartSEO AI Debug: Éditeur classique détecté');
            
            // Vérifier si la métabox est présente
            if ($('#smartseo-ai-writing-assistant').length > 0) {
                console.log('SmartSEO AI Debug: Métabox trouvée dans l\'éditeur classique');
            } else {
                console.error('SmartSEO AI Debug: Métabox NON trouvée dans l\'éditeur classique');
            }
        } else {
            console.log('SmartSEO AI Debug: Aucun éditeur détecté');
        }
        
        // Ajouter un bouton de débogage
        $('body').append('<div id="smartseo-ai-debug-button" style="position: fixed; bottom: 20px; right: 20px; background-color: #0073aa; color: white; padding: 10px; border-radius: 5px; cursor: pointer; z-index: 9999;">SmartSEO AI Debug</div>');
        
        $('#smartseo-ai-debug-button').on('click', function() {
            // Forcer l'initialisation de l'interface
            if (window.SmartSEOAI && window.SmartSEOAI.UIManager) {
                console.log('SmartSEO AI Debug: Tentative de forcer l\'initialisation de l\'interface');
                
                if (window.wp && window.wp.data && window.wp.data.select('core/editor')) {
                    // Forcer l'initialisation pour Gutenberg
                    if (window.SmartSEOAI.UIManager.initGutenbergSidebar) {
                        window.SmartSEOAI.UIManager.initGutenbergSidebar();
                        console.log('SmartSEO AI Debug: Initialisation forcée pour Gutenberg');
                    }
                } else {
                    // Forcer l'initialisation pour l'éditeur classique
                    if (window.SmartSEOAI.UIManager.initClassicEditorSidebar) {
                        window.SmartSEOAI.UIManager.initClassicEditorSidebar();
                        console.log('SmartSEO AI Debug: Initialisation forcée pour l\'éditeur classique');
                    }
                }
            }
        });
    });

})(jQuery);
