<?php
/**
 * Classe de gestion des hooks pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les hooks de l'Assistant de Rédaction SEO
 */
class SmartSEO_AI_Writing_Assistant_Hooks {

    /**
     * Initialise le gestionnaire des hooks
     */
    public function init() {
        // Ajouter les hooks
        add_action( 'init', array( $this, 'register_meta' ) );
        add_action( 'enqueue_block_editor_assets', array( $this, 'enqueue_block_editor_assets' ) );
        add_filter( 'plugin_action_links_' . SMARTSEO_AI_PLUGIN_BASENAME, array( $this, 'add_action_links' ) );
    }

    /**
     * Enregistre les métadonnées
     */
    public function register_meta() {
        register_meta(
            'post',
            'smartseo_ai_focus_keyword',
            array(
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'auth_callback' => function() {
                    return current_user_can( 'edit_posts' );
                },
            )
        );

        register_meta(
            'post',
            'smartseo_ai_meta_description',
            array(
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'auth_callback' => function() {
                    return current_user_can( 'edit_posts' );
                },
            )
        );
    }

    /**
     * Charge les assets pour l'éditeur de blocs
     */
    public function enqueue_block_editor_assets() {
        // Enregistrer le script pour l'éditeur de blocs
        wp_enqueue_script(
            'smartseo-ai-block-editor',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-block-editor.js',
            array( 'wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n', 'wp-data' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Localiser le script
        wp_localize_script(
            'smartseo-ai-block-editor',
            'smartseoAiBlockEditor',
            array(
                'pluginName' => __( 'Assistant de Rédaction SEO', 'smartseo-ai' ),
                'metaDescription' => __( 'Meta Description', 'smartseo-ai' ),
                'focusKeyword' => __( 'Mot-clé principal', 'smartseo-ai' ),
                'enterMetaDescription' => __( 'Entrez une meta description...', 'smartseo-ai' ),
                'enterFocusKeyword' => __( 'Entrez un mot-clé principal...', 'smartseo-ai' ),
            )
        );

        // Inclure l'interface de l'Assistant de Rédaction SEO pour Gutenberg
        include_once SMARTSEO_AI_PLUGIN_DIR . 'admin/partials/smartseo-ai-gutenberg-editor-sidebar.php';
    }

    /**
     * Ajoute des liens d'action sur la page des plugins
     *
     * @param array $links Liens existants.
     * @return array Liens modifiés.
     */
    public function add_action_links( $links ) {
        $plugin_links = array(
            '<a href="' . admin_url( 'admin.php?page=smartseo-ai-settings' ) . '">' . __( 'Paramètres', 'smartseo-ai' ) . '</a>',
        );
        return array_merge( $plugin_links, $links );
    }
}
