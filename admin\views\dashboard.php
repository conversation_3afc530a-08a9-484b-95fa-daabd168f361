<?php
/**
 * Vue du tableau de bord
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Calculer les statistiques
$total_posts = count( $posts );
$optimized_posts = 0;
$good_score_posts = 0;
$average_score_posts = 0;
$poor_score_posts = 0;

foreach ( $posts as $post ) {
    if ( $post['has_score'] ) {
        $optimized_posts++;
        
        if ( $post['seo_score'] >= 80 ) {
            $good_score_posts++;
        } elseif ( $post['seo_score'] >= 50 ) {
            $average_score_posts++;
        } else {
            $poor_score_posts++;
        }
    }
}

$optimization_rate = $total_posts > 0 ? round( ( $optimized_posts / $total_posts ) * 100 ) : 0;
?>

<div class="wrap">
    <h1><?php _e( 'SmartSEO AI - Tableau de bord', 'smartseo-ai' ); ?></h1>
    
    <div class="smartseo-ai-container">
        <div class="smartseo-ai-header">
            <h1><?php _e( 'Tableau de bord SEO', 'smartseo-ai' ); ?></h1>
            <span class="smartseo-ai-version">v<?php echo SMARTSEO_AI_VERSION; ?></span>
        </div>
        
        <div class="smartseo-ai-content">
            <div class="smartseo-ai-dashboard-stats">
                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Articles analysés', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo $optimized_posts; ?>/<?php echo $total_posts; ?></div>
                    <div class="stat-description"><?php echo $optimization_rate; ?>% <?php _e( 'optimisés', 'smartseo-ai' ); ?></div>
                </div>
                
                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Score excellent', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo $good_score_posts; ?></div>
                    <div class="stat-description"><?php _e( 'Articles avec un score ≥ 80', 'smartseo-ai' ); ?></div>
                </div>
                
                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Score moyen', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo $average_score_posts; ?></div>
                    <div class="stat-description"><?php _e( 'Articles avec un score entre 50 et 79', 'smartseo-ai' ); ?></div>
                </div>
                
                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Score faible', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo $poor_score_posts; ?></div>
                    <div class="stat-description"><?php _e( 'Articles avec un score < 50', 'smartseo-ai' ); ?></div>
                </div>
            </div>
            
            <h2><?php _e( 'Articles récents', 'smartseo-ai' ); ?></h2>
            
            <table class="smartseo-ai-posts-table">
                <thead>
                    <tr>
                        <th><?php _e( 'Titre', 'smartseo-ai' ); ?></th>
                        <th><?php _e( 'Type', 'smartseo-ai' ); ?></th>
                        <th><?php _e( 'Date', 'smartseo-ai' ); ?></th>
                        <th class="column-score"><?php _e( 'Score SEO', 'smartseo-ai' ); ?></th>
                        <th class="column-actions"><?php _e( 'Actions', 'smartseo-ai' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( empty( $posts ) ) : ?>
                        <tr>
                            <td colspan="5"><?php _e( 'Aucun article trouvé.', 'smartseo-ai' ); ?></td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ( $posts as $post ) : ?>
                            <tr>
                                <td>
                                    <a href="<?php echo esc_url( $post['permalink'] ); ?>" target="_blank">
                                        <?php echo esc_html( $post['title'] ); ?>
                                    </a>
                                </td>
                                <td><?php echo esc_html( $post['post_type'] ); ?></td>
                                <td><?php echo esc_html( $post['date'] ); ?></td>
                                <td class="column-score">
                                    <?php if ( $post['has_score'] ) : ?>
                                        <div class="smartseo-ai-score-indicator smartseo-ai-score-<?php echo esc_attr( $this->get_score_class( $post['seo_score'] ) ); ?>">
                                            <?php echo esc_html( $post['seo_score'] ); ?>/100
                                        </div>
                                    <?php else : ?>
                                        <div class="smartseo-ai-score-indicator smartseo-ai-score-none">
                                            <?php _e( 'Non optimisé', 'smartseo-ai' ); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="column-actions">
                                    <a href="<?php echo esc_url( $post['edit_link'] ); ?>" class="button button-small">
                                        <span class="dashicons dashicons-edit" style="font-size: 16px; vertical-align: text-bottom;"></span>
                                        <?php _e( 'Éditer', 'smartseo-ai' ); ?>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
