<?php
/**
 * Classe d'optimisation de contenu pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'optimisation de contenu
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Content_Optimizer {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Optimise le contenu
     *
     * @param int    $post_id ID de l'article.
     * @param string $keyword Mot-clé principal.
     * @return array|WP_Error Résultats de l'optimisation ou erreur.
     */
    public function optimize( $post_id, $keyword ) {
        // Vérifier si le mot-clé est fourni
        if ( empty( $keyword ) ) {
            return new WP_Error( 'missing_data', __( 'Veuillez fournir un mot-clé principal.', 'smartseo-ai' ) );
        }

        // Récupérer les données de l'article
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            return new WP_Error( 'invalid_post', __( 'Article invalide.', 'smartseo-ai' ) );
        }
        
        $title = $post->post_title;
        $content = $post->post_content;
        $meta_description = get_post_meta( $post_id, 'smartseo_ai_meta_description', true );

        // Optimiser le titre
        $optimized_title = $this->optimize_title( $title, $keyword );
        
        // Optimiser la meta description
        $optimized_meta_description = $this->optimize_meta_description( $meta_description, $title, $content, $keyword );
        
        // Optimiser le contenu
        $optimized_content = $this->optimize_content( $content, $keyword );

        // Retourner les résultats
        return array(
            'title' => $optimized_title,
            'meta_description' => $optimized_meta_description,
            'content' => $optimized_content,
            'message' => __( 'Contenu optimisé avec succès !', 'smartseo-ai' ),
        );
    }

    /**
     * Optimise le titre
     *
     * @param string $title   Titre original.
     * @param string $keyword Mot-clé principal.
     * @return string Titre optimisé.
     */
    private function optimize_title( $title, $keyword ) {
        // Construire le prompt
        $prompt = "Optimise ce titre pour le SEO avec le mot-clé \"{$keyword}\" : \"{$title}\".

Le titre optimisé doit :
1. Inclure le mot-clé principal de manière naturelle, idéalement au début
2. Être accrocheur et engageant
3. Avoir entre 50 et 60 caractères
4. Être clair et précis
5. Conserver l'intention et le sujet du titre original

Réponds uniquement avec le titre optimisé, sans guillemets ni autres explications.";

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $title;
        }

        // Nettoyer la réponse
        $optimized_title = trim( $response );
        $optimized_title = trim( $optimized_title, " \t\n\r\0\x0B\"'" );

        return $optimized_title;
    }

    /**
     * Optimise la meta description
     *
     * @param string $meta_description Meta description originale.
     * @param string $title            Titre de l'article.
     * @param string $content          Contenu de l'article.
     * @param string $keyword          Mot-clé principal.
     * @return string Meta description optimisée.
     */
    private function optimize_meta_description( $meta_description, $title, $content, $keyword ) {
        // Extraire un résumé du contenu
        $summary = wp_strip_all_tags( $content );
        if ( strlen( $summary ) > 500 ) {
            $summary = substr( $summary, 0, 500 ) . '...';
        }

        // Construire le prompt
        $prompt = "Optimise cette meta description pour le SEO avec le mot-clé \"{$keyword}\" : ";
        
        if ( ! empty( $meta_description ) ) {
            $prompt .= "\"{$meta_description}\".";
        } else {
            $prompt .= "Crée une meta description pour un article intitulé \"{$title}\".";
            
            if ( ! empty( $summary ) ) {
                $prompt .= " Voici un résumé du contenu : \"{$summary}\".";
            }
        }
        
        $prompt .= "

La meta description optimisée doit :
1. Inclure le mot-clé principal de manière naturelle
2. Être incitative au clic
3. Avoir entre 150 et 160 caractères
4. Être claire et précise
5. Résumer le contenu de l'article
6. Inclure un appel à l'action

Réponds uniquement avec la meta description optimisée, sans guillemets ni autres explications.";

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $meta_description;
        }

        // Nettoyer la réponse
        $optimized_meta_description = trim( $response );
        $optimized_meta_description = trim( $optimized_meta_description, " \t\n\r\0\x0B\"'" );

        return $optimized_meta_description;
    }

    /**
     * Optimise le contenu
     *
     * @param string $content Contenu original.
     * @param string $keyword Mot-clé principal.
     * @return string Contenu optimisé.
     */
    private function optimize_content( $content, $keyword ) {
        // Construire le prompt
        $prompt = "Optimise ce contenu pour le SEO avec le mot-clé \"{$keyword}\" :

{$content}

Le contenu optimisé doit :
1. Inclure le mot-clé principal et ses variantes de manière naturelle
2. Avoir une densité de mot-clé optimale (environ 1-2%)
3. Améliorer la structure avec des titres H2 et H3 pertinents
4. Ajouter des listes à puces ou numérotées si pertinent
5. Améliorer la lisibilité avec des paragraphes courts
6. Inclure des liens internes si pertinent
7. Conserver l'intention et le sujet du contenu original
8. Utiliser le format HTML pour la structure

Réponds uniquement avec le contenu optimisé, en utilisant le format HTML.";

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $content;
        }

        // Nettoyer la réponse
        $optimized_content = trim( $response );

        return $optimized_content;
    }
}
