<?php
/**
 * Testeur pour les améliorations de la meta box SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour tester les nouvelles fonctionnalités de la meta box
 */
class SmartSEO_AI_Metabox_Tester {

    /**
     * Instance singleton
     *
     * @var SmartSEO_AI_Metabox_Tester
     */
    private static $instance = null;

    /**
     * Résultats des tests
     *
     * @var array
     */
    private $test_results = array();

    /**
     * Constructeur
     */
    private function __construct() {
        // Hook pour ajouter une page de test dans l'admin (uniquement en mode debug)
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            add_action( 'admin_menu', array( $this, 'add_test_menu' ) );
            add_action( 'wp_ajax_smartseo_ai_test_metabox', array( $this, 'ajax_run_tests' ) );
        }
    }

    /**
     * Récupère l'instance singleton
     *
     * @return SmartSEO_AI_Metabox_Tester
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Ajoute le menu de test
     */
    public function add_test_menu() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Tests Meta Box', 'smartseo-ai' ),
            __( 'Tests Meta Box', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-metabox-tests',
            array( $this, 'render_test_page' )
        );
    }

    /**
     * Affiche la page de test
     */
    public function render_test_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'Tests Meta Box SmartSEO AI', 'smartseo-ai' ); ?></h1>
            
            <div class="notice notice-info">
                <p><?php _e( 'Cette page permet de tester les nouvelles fonctionnalités de la meta box améliorée.', 'smartseo-ai' ); ?></p>
            </div>

            <div class="test-controls">
                <button type="button" class="button button-primary" id="run-metabox-tests">
                    <?php _e( 'Lancer tous les tests', 'smartseo-ai' ); ?>
                </button>
                
                <button type="button" class="button button-secondary" id="toggle-metabox-interface">
                    <?php _e( 'Basculer l\'interface', 'smartseo-ai' ); ?>
                </button>
                
                <button type="button" class="button button-secondary" id="reset-metabox-settings">
                    <?php _e( 'Réinitialiser les paramètres', 'smartseo-ai' ); ?>
                </button>
                
                <a href="<?php echo admin_url( 'post-new.php?post_type=post' ); ?>" class="button button-secondary">
                    <?php _e( 'Tester dans l\'éditeur', 'smartseo-ai' ); ?>
                </a>
            </div>

            <div id="test-results" style="margin-top: 20px;"></div>
        </div>

        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('#run-metabox-tests').on('click', function() {
                var $button = $(this);
                var $results = $('#test-results');
                
                $button.prop('disabled', true).text('<?php _e( 'Tests en cours...', 'smartseo-ai' ); ?>');
                $results.html('<div class="notice notice-info"><p><?php _e( 'Exécution des tests...', 'smartseo-ai' ); ?></p></div>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'smartseo_ai_test_metabox',
                        nonce: '<?php echo wp_create_nonce( 'smartseo_ai_metabox_tests' ); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $results.html(response.data.html);
                        } else {
                            $results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        $results.html('<div class="notice notice-error"><p><?php _e( 'Erreur lors de l\'exécution des tests.', 'smartseo-ai' ); ?></p></div>');
                    },
                    complete: function() {
                        $button.prop('disabled', false).text('<?php _e( 'Lancer tous les tests', 'smartseo-ai' ); ?>');
                    }
                });
            });

            $('#toggle-metabox-interface').on('click', function() {
                var current = <?php echo get_option( 'smartseo_ai_enhanced_metabox', true ) ? 'true' : 'false'; ?>;
                var newValue = !current;
                
                $.post(ajaxurl, {
                    action: 'update_option',
                    option_name: 'smartseo_ai_enhanced_metabox',
                    option_value: newValue ? '1' : '0',
                    _wpnonce: '<?php echo wp_create_nonce( 'update_option' ); ?>'
                }, function() {
                    alert('Interface basculée ! Rechargez une page d\'édition pour voir les changements.');
                });
            });

            $('#reset-metabox-settings').on('click', function() {
                if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres de la meta box ?')) {
                    $.post(ajaxurl, {
                        action: 'smartseo_ai_reset_metabox_settings',
                        _wpnonce: '<?php echo wp_create_nonce( 'reset_metabox_settings' ); ?>'
                    }, function() {
                        alert('Paramètres de la meta box réinitialisés !');
                    });
                }
            });
        });
        </script>

        <style>
        .test-controls {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        </style>
        <?php
    }

    /**
     * AJAX : Lance tous les tests
     */
    public function ajax_run_tests() {
        check_ajax_referer( 'smartseo_ai_metabox_tests', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'smartseo-ai' ) );
        }

        $this->test_results = array();

        // Lancer tous les tests
        $this->test_metabox_class();
        $this->test_enhanced_interface();
        $this->test_assets_loading();
        $this->test_settings_functionality();
        $this->test_metabox_positioning();

        // Générer le HTML des résultats
        $html = $this->generate_results_html();

        wp_send_json_success( array( 'html' => $html ) );
    }

    /**
     * Teste la classe des métaboxes
     */
    private function test_metabox_class() {
        $this->add_test_result( 'Metabox Class', 'Tests de la classe des métaboxes' );

        try {
            // Test 1: Vérification de l'instance
            $this->add_test_result( 'Class Instance', 
                class_exists( 'SmartSEO_AI_Metaboxes' ) ? 'PASS' : 'FAIL',
                'Classe SmartSEO_AI_Metaboxes disponible'
            );

            // Test 2: Méthodes disponibles
            $required_methods = array(
                'add_meta_boxes',
                'render_main_metabox',
                'save_meta_boxes',
                'enqueue_metabox_assets',
                'get_score_class',
                'get_score_color',
                'get_score_label',
            );

            foreach ( $required_methods as $method ) {
                $this->add_test_result( "Method $method", 
                    method_exists( 'SmartSEO_AI_Metaboxes', $method ) ? 'PASS' : 'FAIL',
                    "Méthode $method disponible"
                );
            }

        } catch ( Exception $e ) {
            $this->add_test_result( 'Metabox Class Exception', 'FAIL', $e->getMessage() );
        }
    }

    /**
     * Teste l'interface améliorée
     */
    private function test_enhanced_interface() {
        $this->add_test_result( 'Enhanced Interface', 'Tests de l\'interface améliorée' );

        // Test 1: Fichiers CSS/JS
        $asset_files = array(
            'admin/css/smartseo-ai-metabox-enhanced.css',
            'admin/js/smartseo-ai-metabox-enhanced.js',
        );

        foreach ( $asset_files as $file ) {
            $file_path = SMARTSEO_AI_PLUGIN_DIR . $file;
            $this->add_test_result( "Asset File " . basename( $file ), 
                file_exists( $file_path ) ? 'PASS' : 'FAIL',
                file_exists( $file_path ) ? "Fichier $file existe" : "Fichier $file manquant"
            );
        }

        // Test 2: Option d'interface améliorée
        $enhanced_option = get_option( 'smartseo_ai_enhanced_metabox' );
        $this->add_test_result( 'Enhanced Interface Option', 
            ! is_null( $enhanced_option ) ? 'PASS' : 'FAIL',
            'Option d\'interface : ' . ( $enhanced_option ? 'Activée' : 'Désactivée' )
        );
    }

    /**
     * Teste le chargement des assets
     */
    private function test_assets_loading() {
        $this->add_test_result( 'Assets Loading', 'Tests du chargement des assets' );

        // Test 1: Hooks WordPress
        $hooks_registered = array(
            'admin_enqueue_scripts' => has_action( 'admin_enqueue_scripts' ),
            'add_meta_boxes' => has_action( 'add_meta_boxes' ),
            'save_post' => has_action( 'save_post' ),
        );

        foreach ( $hooks_registered as $hook => $registered ) {
            $this->add_test_result( "Hook $hook", 
                $registered ? 'PASS' : 'FAIL',
                $registered ? "Hook $hook enregistré" : "Hook $hook manquant"
            );
        }
    }

    /**
     * Teste les fonctionnalités de paramètres
     */
    private function test_settings_functionality() {
        $this->add_test_result( 'Settings Functionality', 'Tests des paramètres' );

        // Test 1: Options de paramètres
        $settings_options = array(
            'smartseo_ai_enhanced_metabox',
            'smartseo_ai_metabox_position',
            'smartseo_ai_metabox_priority',
            'smartseo_ai_auto_optimize',
            'smartseo_ai_show_score_in_list',
            'smartseo_ai_enable_og_fields',
            'smartseo_ai_enable_advanced_fields',
        );

        foreach ( $settings_options as $option ) {
            $value = get_option( $option );
            $this->add_test_result( "Option $option", 
                ! is_null( $value ) ? 'PASS' : 'INFO',
                "Valeur : " . ( is_bool( $value ) ? ( $value ? 'true' : 'false' ) : $value )
            );
        }

        // Test 2: Page de paramètres
        $settings_page_file = SMARTSEO_AI_PLUGIN_DIR . 'admin/views/metabox-settings.php';
        $this->add_test_result( 'Settings Page File', 
            file_exists( $settings_page_file ) ? 'PASS' : 'FAIL',
            file_exists( $settings_page_file ) ? 'Fichier de paramètres existe' : 'Fichier de paramètres manquant'
        );
    }

    /**
     * Teste le positionnement de la meta box
     */
    private function test_metabox_positioning() {
        $this->add_test_result( 'Metabox Positioning', 'Tests du positionnement' );

        // Test 1: Positions valides
        $valid_positions = array( 'normal', 'side', 'advanced' );
        $current_position = get_option( 'smartseo_ai_metabox_position', 'side' );
        
        $this->add_test_result( 'Valid Position', 
            in_array( $current_position, $valid_positions, true ) ? 'PASS' : 'FAIL',
            "Position actuelle : $current_position"
        );

        // Test 2: Priorités valides
        $valid_priorities = array( 'high', 'core', 'default', 'low' );
        $current_priority = get_option( 'smartseo_ai_metabox_priority', 'high' );
        
        $this->add_test_result( 'Valid Priority', 
            in_array( $current_priority, $valid_priorities, true ) ? 'PASS' : 'FAIL',
            "Priorité actuelle : $current_priority"
        );
    }

    /**
     * Ajoute un résultat de test
     *
     * @param string $test_name Nom du test.
     * @param string $status Statut (PASS/FAIL/INFO).
     * @param string $message Message descriptif.
     */
    private function add_test_result( $test_name, $status, $message = '' ) {
        $this->test_results[] = array(
            'name' => $test_name,
            'status' => $status,
            'message' => $message,
            'timestamp' => current_time( 'mysql' ),
        );
    }

    /**
     * Génère le HTML des résultats de test
     *
     * @return string HTML des résultats.
     */
    private function generate_results_html() {
        $html = '<div class="test-results">';
        $html .= '<h2>' . __( 'Résultats des tests de la meta box', 'smartseo-ai' ) . '</h2>';

        $pass_count = 0;
        $fail_count = 0;
        $info_count = 0;

        foreach ( $this->test_results as $result ) {
            $status_class = strtolower( $result['status'] );
            $status_color = '';
            
            switch ( $result['status'] ) {
                case 'PASS':
                    $status_color = 'color: #46b450; font-weight: bold;';
                    $pass_count++;
                    break;
                case 'FAIL':
                    $status_color = 'color: #dc3232; font-weight: bold;';
                    $fail_count++;
                    break;
                default:
                    $status_color = 'color: #0073aa; font-weight: bold;';
                    $info_count++;
                    break;
            }

            $html .= '<div style="padding: 10px; border-left: 4px solid ' . 
                     ( $result['status'] === 'PASS' ? '#46b450' : ( $result['status'] === 'FAIL' ? '#dc3232' : '#0073aa' ) ) . 
                     '; margin-bottom: 10px; background: #f9f9f9;">';
            $html .= '<strong>' . esc_html( $result['name'] ) . '</strong> ';
            $html .= '<span style="' . $status_color . '">[' . esc_html( $result['status'] ) . ']</span>';
            
            if ( ! empty( $result['message'] ) ) {
                $html .= '<br><small>' . esc_html( $result['message'] ) . '</small>';
            }
            
            $html .= '</div>';
        }

        // Résumé
        $html .= '<div style="margin-top: 20px; padding: 15px; background: #f0f0f1; border-radius: 4px;">';
        $html .= '<h3>' . __( 'Résumé', 'smartseo-ai' ) . '</h3>';
        $html .= '<p>';
        $html .= '<span style="color: #46b450; font-weight: bold;">' . $pass_count . ' tests réussis</span>, ';
        $html .= '<span style="color: #dc3232; font-weight: bold;">' . $fail_count . ' tests échoués</span>, ';
        $html .= '<span style="color: #0073aa; font-weight: bold;">' . $info_count . ' informations</span>';
        $html .= '</p>';
        
        if ( $fail_count === 0 ) {
            $html .= '<div class="notice notice-success inline"><p><strong>' . 
                     __( 'Tous les tests sont passés avec succès ! La meta box améliorée est correctement implémentée.', 'smartseo-ai' ) . 
                     '</strong></p></div>';
        } else {
            $html .= '<div class="notice notice-warning inline"><p><strong>' . 
                     __( 'Certains tests ont échoué. Vérifiez la configuration et les fichiers.', 'smartseo-ai' ) . 
                     '</strong></p></div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }
}
