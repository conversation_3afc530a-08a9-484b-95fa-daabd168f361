<?php
/**
 * Classe pour la génération de contenu SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la génération de contenu SEO
 */
class SmartSEO_AI_Content_Generator {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     *
     * @param SmartSEO_AI_API|SmartSEO_AI_Gemini $ai_api Instance de l'API IA.
     */
    public function __construct( $ai_api ) {
        $this->ai_api = $ai_api;
    }

    /**
     * Génère du contenu SEO
     *
     * @param int    $post_id      ID de l'article.
     * @param string $content_type Type de contenu à générer.
     * @param string $keyword      Mot-clé principal.
     * @param string $topic        Sujet de l'article.
     * @param string $content      Contenu existant.
     * @return string|WP_Error Contenu généré ou erreur.
     */
    public function generate( $post_id, $content_type, $keyword = '', $topic = '', $content = '' ) {
        // Si le post_id est fourni, récupérer les informations de l'article
        if ( $post_id > 0 ) {
            $post = get_post( $post_id );
            if ( $post ) {
                $title = $post->post_title;
                $content = $post->post_content;
                $excerpt = $post->post_excerpt;
            }
        }

        // Si le mot-clé n'est pas fourni, essayer de le récupérer des métadonnées
        if ( empty( $keyword ) && $post_id > 0 ) {
            $keyword = get_post_meta( $post_id, 'smartseo_ai_focus_keyword', true );
            if ( empty( $keyword ) ) {
                $keyword = get_post_meta( $post_id, '_yoast_wpseo_focuskw', true );
            }
        }

        // Si le sujet n'est pas fourni, utiliser le titre ou le mot-clé
        if ( empty( $topic ) ) {
            $topic = isset( $title ) ? $title : $keyword;
        }

        // Générer le prompt en fonction du type de contenu
        switch ( $content_type ) {
            case 'title':
                return $this->generate_title( $topic, $keyword, $content );
            
            case 'meta_description':
                return $this->generate_meta_description( $topic, $keyword, $content );
            
            case 'introduction':
                return $this->generate_introduction( $topic, $keyword, $content );
            
            case 'conclusion':
                return $this->generate_conclusion( $topic, $keyword, $content );
            
            case 'h2_headings':
                return $this->generate_h2_headings( $topic, $keyword, $content );
            
            case 'h3_headings':
                return $this->generate_h3_headings( $topic, $keyword, $content );
            
            case 'paragraph':
                return $this->generate_paragraph( $topic, $keyword, $content );
            
            case 'full_article':
                return $this->generate_full_article( $topic, $keyword );
            
            default:
                return new WP_Error( 'invalid_content_type', __( 'Type de contenu non valide.', 'smartseo-ai' ) );
        }
    }

    /**
     * Génère un titre SEO
     *
     * @param string $topic   Sujet de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $content Contenu existant.
     * @return string|WP_Error Titre généré ou erreur.
     */
    private function generate_title( $topic, $keyword, $content ) {
        $prompt = $this->build_prompt(
            "Génère 5 titres (H1) SEO-friendly et accrocheurs pour un article sur le sujet suivant : $topic. " .
            ( ! empty( $keyword ) ? "Le mot-clé principal à inclure est : $keyword. " : "" ) .
            "Les titres doivent être concis (moins de 60 caractères), attrayants, et optimisés pour le référencement. " .
            "Réponds uniquement avec les 5 titres, un par ligne, sans numérotation ni formatage supplémentaire."
        );

        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Nettoyer la réponse
        $titles = explode( "\n", trim( $response ) );
        $titles = array_filter( $titles, 'trim' );

        // Formater la réponse
        $formatted_response = array(
            'titles' => $titles,
        );

        return $formatted_response;
    }

    /**
     * Génère une meta description SEO
     *
     * @param string $topic   Sujet de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $content Contenu existant.
     * @return string|WP_Error Meta description générée ou erreur.
     */
    private function generate_meta_description( $topic, $keyword, $content ) {
        // Limiter la taille du contenu pour l'API
        $content_excerpt = wp_trim_words( strip_tags( $content ), 100, '...' );

        $prompt = $this->build_prompt(
            "Génère 3 meta descriptions SEO-friendly pour un article sur le sujet suivant : $topic. " .
            ( ! empty( $keyword ) ? "Le mot-clé principal à inclure est : $keyword. " : "" ) .
            "Les meta descriptions doivent être concises (entre 120 et 155 caractères), informatives, et inciter au clic. " .
            "Voici un extrait du contenu de l'article : $content_excerpt. " .
            "Réponds uniquement avec les 3 meta descriptions, une par ligne, sans numérotation ni formatage supplémentaire."
        );

        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Nettoyer la réponse
        $descriptions = explode( "\n", trim( $response ) );
        $descriptions = array_filter( $descriptions, 'trim' );

        // Formater la réponse
        $formatted_response = array(
            'descriptions' => $descriptions,
        );

        return $formatted_response;
    }

    /**
     * Génère une introduction SEO
     *
     * @param string $topic   Sujet de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $content Contenu existant.
     * @return string|WP_Error Introduction générée ou erreur.
     */
    private function generate_introduction( $topic, $keyword, $content ) {
        $prompt = $this->build_prompt(
            "Génère une introduction SEO-friendly et engageante pour un article sur le sujet suivant : $topic. " .
            ( ! empty( $keyword ) ? "Le mot-clé principal à inclure est : $keyword. " : "" ) .
            "L'introduction doit être concise (environ 150 mots), captivante, et présenter clairement le sujet et l'objectif de l'article. " .
            "Elle doit inclure le mot-clé principal de manière naturelle et poser les bases du contenu à venir. " .
            "Réponds uniquement avec l'introduction, sans formatage supplémentaire."
        );

        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Formater la réponse
        $formatted_response = array(
            'introduction' => trim( $response ),
        );

        return $formatted_response;
    }

    /**
     * Génère une conclusion SEO
     *
     * @param string $topic   Sujet de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $content Contenu existant.
     * @return string|WP_Error Conclusion générée ou erreur.
     */
    private function generate_conclusion( $topic, $keyword, $content ) {
        // Limiter la taille du contenu pour l'API
        $content_excerpt = wp_trim_words( strip_tags( $content ), 200, '...' );

        $prompt = $this->build_prompt(
            "Génère une conclusion SEO-friendly et percutante pour un article sur le sujet suivant : $topic. " .
            ( ! empty( $keyword ) ? "Le mot-clé principal à inclure est : $keyword. " : "" ) .
            "La conclusion doit être concise (environ 100-150 mots), résumer les points clés de l'article, et inclure un appel à l'action. " .
            "Elle doit inclure le mot-clé principal de manière naturelle et laisser le lecteur avec une impression positive. " .
            "Voici un extrait du contenu de l'article : $content_excerpt. " .
            "Réponds uniquement avec la conclusion, sans formatage supplémentaire."
        );

        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Formater la réponse
        $formatted_response = array(
            'conclusion' => trim( $response ),
        );

        return $formatted_response;
    }

    /**
     * Génère des sous-titres H2 SEO
     *
     * @param string $topic   Sujet de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $content Contenu existant.
     * @return string|WP_Error Sous-titres H2 générés ou erreur.
     */
    private function generate_h2_headings( $topic, $keyword, $content ) {
        $prompt = $this->build_prompt(
            "Génère 5 sous-titres H2 SEO-friendly et structurés pour un article sur le sujet suivant : $topic. " .
            ( ! empty( $keyword ) ? "Le mot-clé principal à inclure est : $keyword. " : "" ) .
            "Les sous-titres doivent être concis, informatifs, et couvrir les aspects importants du sujet. " .
            "Ils doivent inclure des mots-clés pertinents et suivre une progression logique. " .
            "Réponds uniquement avec les 5 sous-titres, un par ligne, sans numérotation ni formatage supplémentaire."
        );

        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Nettoyer la réponse
        $headings = explode( "\n", trim( $response ) );
        $headings = array_filter( $headings, 'trim' );

        // Formater la réponse
        $formatted_response = array(
            'headings' => $headings,
        );

        return $formatted_response;
    }

    /**
     * Génère des sous-titres H3 SEO
     *
     * @param string $topic   Sujet de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $content Contenu existant.
     * @return string|WP_Error Sous-titres H3 générés ou erreur.
     */
    private function generate_h3_headings( $topic, $keyword, $content ) {
        // Extraire les sous-titres H2 existants
        preg_match_all( '/<h2[^>]*>(.*?)<\/h2>/i', $content, $h2_matches );
        $h2_headings = array();
        if ( ! empty( $h2_matches[1] ) ) {
            $h2_headings = array_map( 'strip_tags', $h2_matches[1] );
        }

        $h2_headings_text = implode( "\n", $h2_headings );

        $prompt = $this->build_prompt(
            "Génère des sous-titres H3 SEO-friendly pour chacun des sous-titres H2 suivants d'un article sur : $topic. " .
            ( ! empty( $keyword ) ? "Le mot-clé principal à inclure est : $keyword. " : "" ) .
            "Sous-titres H2 existants :\n$h2_headings_text\n\n" .
            "Pour chaque H2, génère 2-3 sous-titres H3 pertinents et optimisés pour le SEO. " .
            "Les sous-titres H3 doivent être concis, informatifs, et approfondir les aspects du H2 parent. " .
            "Réponds au format suivant :\n" .
            "H2: [titre H2]\n" .
            "- [sous-titre H3 1]\n" .
            "- [sous-titre H3 2]\n" .
            "- [sous-titre H3 3]\n\n" .
            "H2: [titre H2 suivant]\n" .
            "..."
        );

        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Formater la réponse
        $formatted_response = array(
            'h3_structure' => trim( $response ),
        );

        return $formatted_response;
    }

    /**
     * Génère un paragraphe SEO
     *
     * @param string $topic   Sujet de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $content Contenu existant.
     * @return string|WP_Error Paragraphe généré ou erreur.
     */
    private function generate_paragraph( $topic, $keyword, $content ) {
        $prompt = $this->build_prompt(
            "Génère un paragraphe SEO-friendly et informatif sur le sujet suivant : $topic. " .
            ( ! empty( $keyword ) ? "Le mot-clé principal à inclure est : $keyword. " : "" ) .
            "Le paragraphe doit être concis (environ 100-150 mots), informatif, et bien structuré. " .
            "Il doit inclure le mot-clé principal de manière naturelle et apporter une valeur ajoutée au lecteur. " .
            "Réponds uniquement avec le paragraphe, sans formatage supplémentaire."
        );

        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Formater la réponse
        $formatted_response = array(
            'paragraph' => trim( $response ),
        );

        return $formatted_response;
    }

    /**
     * Génère un article complet SEO
     *
     * @param string $topic   Sujet de l'article.
     * @param string $keyword Mot-clé principal.
     * @return string|WP_Error Article généré ou erreur.
     */
    private function generate_full_article( $topic, $keyword ) {
        $prompt = $this->build_prompt(
            "Génère un article de blog complet, SEO-friendly et informatif sur le sujet suivant : $topic. " .
            ( ! empty( $keyword ) ? "Le mot-clé principal à inclure est : $keyword. " : "" ) .
            "L'article doit être structuré avec :\n" .
            "1. Un titre H1 accrocheur\n" .
            "2. Une introduction engageante\n" .
            "3. 4-5 sections avec des sous-titres H2\n" .
            "4. Des sous-titres H3 si nécessaire\n" .
            "5. Une conclusion avec un appel à l'action\n\n" .
            "L'article doit faire environ 800-1000 mots, être informatif, engageant, et optimisé pour le SEO. " .
            "Utilise des paragraphes courts, des listes à puces lorsque c'est pertinent, et inclus le mot-clé principal de manière naturelle. " .
            "Réponds avec l'article complet au format HTML, en utilisant les balises appropriées (h1, h2, h3, p, ul, li, etc.)."
        );

        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Formater la réponse
        $formatted_response = array(
            'article' => trim( $response ),
        );

        return $formatted_response;
    }

    /**
     * Construit un prompt pour l'IA
     *
     * @param string $instruction Instruction pour l'IA.
     * @return string Prompt pour l'IA.
     */
    private function build_prompt( $instruction ) {
        $prompt = $instruction . "\n\n";
        $prompt .= "Réponds en français. Sois concis et précis.";

        return $prompt;
    }
}
