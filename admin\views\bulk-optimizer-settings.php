<?php
/**
 * Vue des paramètres de l'optimisation en masse
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Traitement du formulaire
if ( isset( $_POST['submit'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'smartseo_ai_bulk_settings' ) ) {
    // Sauvegarder les paramètres
    update_option( 'smartseo_ai_enhanced_bulk_optimizer', isset( $_POST['enhanced_interface'] ) );
    update_option( 'smartseo_ai_bulk_batch_size', absint( $_POST['batch_size'] ) );
    update_option( 'smartseo_ai_bulk_delay', absint( $_POST['delay_between_batches'] ) * 1000 ); // Convertir en millisecondes
    update_option( 'smartseo_ai_bulk_max_concurrent', absint( $_POST['max_concurrent'] ) );
    update_option( 'smartseo_ai_bulk_auto_retry', isset( $_POST['auto_retry'] ) );
    update_option( 'smartseo_ai_bulk_retry_attempts', absint( $_POST['retry_attempts'] ) );
    
    echo '<div class="notice notice-success is-dismissible"><p>' . __( 'Paramètres sauvegardés avec succès !', 'smartseo-ai' ) . '</p></div>';
}

// Récupérer les paramètres actuels
$enhanced_interface = get_option( 'smartseo_ai_enhanced_bulk_optimizer', true );
$batch_size = get_option( 'smartseo_ai_bulk_batch_size', 3 );
$delay_between_batches = get_option( 'smartseo_ai_bulk_delay', 3000 ) / 1000; // Convertir en secondes
$max_concurrent = get_option( 'smartseo_ai_bulk_max_concurrent', 2 );
$auto_retry = get_option( 'smartseo_ai_bulk_auto_retry', true );
$retry_attempts = get_option( 'smartseo_ai_bulk_retry_attempts', 3 );
?>

<div class="wrap">
    <h1><?php _e( 'Paramètres d\'optimisation en masse', 'smartseo-ai' ); ?></h1>
    
    <form method="post" action="">
        <?php wp_nonce_field( 'smartseo_ai_bulk_settings' ); ?>
        
        <table class="form-table">
            <tbody>
                <!-- Interface utilisateur -->
                <tr>
                    <th scope="row">
                        <label for="enhanced_interface"><?php _e( 'Interface utilisateur', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="enhanced_interface" 
                                       id="enhanced_interface" 
                                       value="1" 
                                       <?php checked( $enhanced_interface ); ?>>
                                <?php _e( 'Utiliser l\'interface améliorée avec filtres avancés', 'smartseo-ai' ); ?>
                            </label>
                            <p class="description">
                                <?php _e( 'L\'interface améliorée offre des filtres avancés, une meilleure visualisation et plus d\'options de tri.', 'smartseo-ai' ); ?>
                            </p>
                        </fieldset>
                    </td>
                </tr>

                <!-- Taille des lots -->
                <tr>
                    <th scope="row">
                        <label for="batch_size"><?php _e( 'Taille des lots', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <input type="number" 
                               name="batch_size" 
                               id="batch_size" 
                               value="<?php echo esc_attr( $batch_size ); ?>" 
                               min="1" 
                               max="10" 
                               class="small-text">
                        <p class="description">
                            <?php _e( 'Nombre d\'articles à optimiser simultanément. Une valeur plus élevée accélère le processus mais consomme plus de ressources.', 'smartseo-ai' ); ?>
                        </p>
                    </td>
                </tr>

                <!-- Délai entre les lots -->
                <tr>
                    <th scope="row">
                        <label for="delay_between_batches"><?php _e( 'Délai entre les lots', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <input type="number" 
                               name="delay_between_batches" 
                               id="delay_between_batches" 
                               value="<?php echo esc_attr( $delay_between_batches ); ?>" 
                               min="1" 
                               max="30" 
                               step="0.5" 
                               class="small-text">
                        <span><?php _e( 'secondes', 'smartseo-ai' ); ?></span>
                        <p class="description">
                            <?php _e( 'Temps d\'attente entre chaque lot d\'optimisation. Permet d\'éviter la surcharge des API.', 'smartseo-ai' ); ?>
                        </p>
                    </td>
                </tr>

                <!-- Requêtes concurrentes maximum -->
                <tr>
                    <th scope="row">
                        <label for="max_concurrent"><?php _e( 'Requêtes concurrentes max', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <input type="number" 
                               name="max_concurrent" 
                               id="max_concurrent" 
                               value="<?php echo esc_attr( $max_concurrent ); ?>" 
                               min="1" 
                               max="5" 
                               class="small-text">
                        <p class="description">
                            <?php _e( 'Nombre maximum de requêtes API simultanées. Limitez cette valeur pour respecter les quotas des fournisseurs IA.', 'smartseo-ai' ); ?>
                        </p>
                    </td>
                </tr>

                <!-- Retry automatique -->
                <tr>
                    <th scope="row">
                        <label for="auto_retry"><?php _e( 'Retry automatique', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="auto_retry" 
                                       id="auto_retry" 
                                       value="1" 
                                       <?php checked( $auto_retry ); ?>>
                                <?php _e( 'Réessayer automatiquement en cas d\'échec', 'smartseo-ai' ); ?>
                            </label>
                            <p class="description">
                                <?php _e( 'Tente automatiquement de relancer l\'optimisation en cas d\'erreur temporaire.', 'smartseo-ai' ); ?>
                            </p>
                        </fieldset>
                    </td>
                </tr>

                <!-- Nombre de tentatives -->
                <tr>
                    <th scope="row">
                        <label for="retry_attempts"><?php _e( 'Nombre de tentatives', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <input type="number" 
                               name="retry_attempts" 
                               id="retry_attempts" 
                               value="<?php echo esc_attr( $retry_attempts ); ?>" 
                               min="1" 
                               max="10" 
                               class="small-text">
                        <p class="description">
                            <?php _e( 'Nombre maximum de tentatives avant d\'abandonner l\'optimisation d\'un article.', 'smartseo-ai' ); ?>
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Informations sur les performances -->
        <h2><?php _e( 'Informations sur les performances', 'smartseo-ai' ); ?></h2>
        <div class="performance-info">
            <div class="performance-card">
                <h3><?php _e( 'Recommandations', 'smartseo-ai' ); ?></h3>
                <ul>
                    <li><?php _e( 'Pour un site avec peu de trafic : Taille de lot 3-5, délai 2-3 secondes', 'smartseo-ai' ); ?></li>
                    <li><?php _e( 'Pour un site avec beaucoup de trafic : Taille de lot 1-2, délai 5-10 secondes', 'smartseo-ai' ); ?></li>
                    <li><?php _e( 'Surveillez vos quotas API pour éviter les dépassements', 'smartseo-ai' ); ?></li>
                    <li><?php _e( 'Testez avec de petits lots avant d\'optimiser en masse', 'smartseo-ai' ); ?></li>
                </ul>
            </div>

            <div class="performance-card">
                <h3><?php _e( 'Estimation des temps', 'smartseo-ai' ); ?></h3>
                <div class="time-estimates">
                    <div class="estimate-item">
                        <strong>10 articles :</strong>
                        <span id="estimate-10">~<?php echo ceil( 10 / $batch_size ) * $delay_between_batches; ?> secondes</span>
                    </div>
                    <div class="estimate-item">
                        <strong>50 articles :</strong>
                        <span id="estimate-50">~<?php echo ceil( 50 / $batch_size ) * $delay_between_batches; ?> secondes</span>
                    </div>
                    <div class="estimate-item">
                        <strong>100 articles :</strong>
                        <span id="estimate-100">~<?php echo ceil( 100 / $batch_size ) * $delay_between_batches; ?> secondes</span>
                    </div>
                </div>
            </div>
        </div>

        <?php submit_button( __( 'Sauvegarder les paramètres', 'smartseo-ai' ) ); ?>
    </form>
</div>

<style>
.performance-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.performance-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.performance-card h3 {
    margin-top: 0;
    color: #0073aa;
}

.performance-card ul {
    margin: 0;
    padding-left: 20px;
}

.performance-card li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.time-estimates {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.estimate-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
}

.estimate-item strong {
    color: #1d2327;
}

.estimate-item span {
    color: #0073aa;
    font-weight: 600;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Mettre à jour les estimations en temps réel
    function updateEstimates() {
        const batchSize = parseInt($('#batch_size').val()) || 1;
        const delay = parseFloat($('#delay_between_batches').val()) || 1;
        
        $('#estimate-10').text('~' + Math.ceil(10 / batchSize) * delay + ' secondes');
        $('#estimate-50').text('~' + Math.ceil(50 / batchSize) * delay + ' secondes');
        $('#estimate-100').text('~' + Math.ceil(100 / batchSize) * delay + ' secondes');
    }
    
    $('#batch_size, #delay_between_batches').on('input', updateEstimates);
});
</script>
