<?php
/**
 * Gestionnaire d'interface utilisateur amélioré pour SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'interface utilisateur améliorée
 */
class SmartSEO_AI_UI_Manager {

    /**
     * Instance singleton
     *
     * @var SmartSEO_AI_UI_Manager
     */
    private static $instance = null;

    /**
     * Gestionnaire de cache
     *
     * @var SmartSEO_AI_Cache_Manager
     */
    private $cache_manager;

    /**
     * Gestionnaire de performance
     *
     * @var SmartSEO_AI_Performance_Manager
     */
    private $performance_manager;

    /**
     * Gestionnaire de queue
     *
     * @var SmartSEO_AI_Queue_Manager
     */
    private $queue_manager;

    /**
     * Constructeur
     */
    private function __construct() {
        $this->cache_manager = SmartSEO_AI_Cache_Manager::get_instance();
        $this->performance_manager = SmartSEO_AI_Performance_Manager::get_instance();
        $this->queue_manager = SmartSEO_AI_Queue_Manager::get_instance();

        // Hooks pour l'interface admin
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_enhanced_scripts' ) );
        add_action( 'wp_ajax_smartseo_ai_get_dashboard_data', array( $this, 'ajax_get_dashboard_data' ) );
        add_action( 'wp_ajax_smartseo_ai_clear_cache', array( $this, 'ajax_clear_cache' ) );
        add_action( 'wp_ajax_smartseo_ai_check_api_health', array( $this, 'ajax_check_api_health' ) );
        add_action( 'wp_ajax_smartseo_ai_get_queue_status', array( $this, 'ajax_get_queue_status' ) );
        add_action( 'wp_ajax_smartseo_ai_start_bulk_optimization', array( $this, 'ajax_start_bulk_optimization' ) );
        add_action( 'wp_ajax_smartseo_ai_stop_bulk_optimization', array( $this, 'ajax_stop_bulk_optimization' ) );

        // Hooks pour les notifications
        add_action( 'admin_notices', array( $this, 'show_admin_notices' ) );
    }

    /**
     * Récupère l'instance singleton
     *
     * @return SmartSEO_AI_UI_Manager
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Charge les scripts et styles améliorés
     *
     * @param string $hook Page actuelle.
     */
    public function enqueue_enhanced_scripts( $hook ) {
        // Vérifier si on est sur une page SmartSEO AI
        if ( ! $this->is_smartseo_page( $hook ) ) {
            return;
        }

        // Scripts de base
        wp_enqueue_script( 'jquery' );
        wp_enqueue_script( 'wp-util' );

        // Chart.js pour les graphiques
        wp_enqueue_script(
            'chartjs',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            array(),
            '3.9.1',
            true
        );

        // Script principal amélioré
        wp_enqueue_script(
            'smartseo-ai-enhanced-ui',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-enhanced-ui.js',
            array( 'jquery', 'wp-util', 'chartjs' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Styles améliorés
        wp_enqueue_style(
            'smartseo-ai-enhanced-ui',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-enhanced-ui.css',
            array(),
            SMARTSEO_AI_VERSION
        );

        // Localisation
        wp_localize_script( 'smartseo-ai-enhanced-ui', 'smartseoAiEnhanced', array(
            'ajaxUrl' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'smartseo_ai_enhanced_nonce' ),
            'i18n' => array(
                'loading' => __( 'Chargement...', 'smartseo-ai' ),
                'success' => __( 'Succès !', 'smartseo-ai' ),
                'error' => __( 'Erreur', 'smartseo-ai' ),
                'confirm' => __( 'Êtes-vous sûr ?', 'smartseo-ai' ),
                'cacheCleared' => __( 'Cache vidé avec succès', 'smartseo-ai' ),
                'optimizationStarted' => __( 'Optimisation en masse démarrée', 'smartseo-ai' ),
                'optimizationStopped' => __( 'Optimisation en masse arrêtée', 'smartseo-ai' ),
                'apiHealthy' => __( 'API en bonne santé', 'smartseo-ai' ),
                'apiError' => __( 'Problème avec l\'API', 'smartseo-ai' ),
            ),
            'settings' => array(
                'refreshInterval' => 5000, // 5 secondes
                'chartColors' => array(
                    'primary' => '#0073aa',
                    'success' => '#46b450',
                    'warning' => '#ffb900',
                    'error' => '#dc3232',
                ),
            ),
        ) );
    }

    /**
     * Vérifie si on est sur une page SmartSEO AI
     *
     * @param string $hook Page actuelle.
     * @return bool
     */
    private function is_smartseo_page( $hook ) {
        $smartseo_pages = array(
            'toplevel_page_smartseo-ai',
            'smartseo-ai_page_smartseo-ai-settings',
            'smartseo-ai_page_smartseo-ai-bulk-optimizer',
            'post.php',
            'post-new.php',
        );

        return in_array( $hook, $smartseo_pages, true );
    }

    /**
     * AJAX : Récupère les données du tableau de bord
     */
    public function ajax_get_dashboard_data() {
        check_ajax_referer( 'smartseo_ai_enhanced_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'smartseo-ai' ) );
        }

        $data = array(
            'cache_stats' => $this->cache_manager->get_stats(),
            'performance_stats' => $this->performance_manager->get_performance_stats(),
            'queue_status' => $this->queue_manager->get_queue_status(),
            'api_health' => $this->performance_manager->check_api_health(),
        );

        wp_send_json_success( $data );
    }

    /**
     * AJAX : Vide le cache
     */
    public function ajax_clear_cache() {
        check_ajax_referer( 'smartseo_ai_enhanced_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'smartseo-ai' ) );
        }

        $result = $this->cache_manager->flush();

        if ( $result ) {
            wp_send_json_success( array(
                'message' => __( 'Cache vidé avec succès.', 'smartseo-ai' ),
            ) );
        } else {
            wp_send_json_error( array(
                'message' => __( 'Erreur lors du vidage du cache.', 'smartseo-ai' ),
            ) );
        }
    }

    /**
     * AJAX : Vérifie la santé des API
     */
    public function ajax_check_api_health() {
        check_ajax_referer( 'smartseo_ai_enhanced_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'smartseo-ai' ) );
        }

        $health = $this->performance_manager->check_api_health();
        wp_send_json_success( $health );
    }

    /**
     * AJAX : Récupère le statut de la queue
     */
    public function ajax_get_queue_status() {
        check_ajax_referer( 'smartseo_ai_enhanced_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'smartseo-ai' ) );
        }

        $status = $this->queue_manager->get_queue_status();
        wp_send_json_success( $status );
    }

    /**
     * AJAX : Démarre l'optimisation en masse
     */
    public function ajax_start_bulk_optimization() {
        check_ajax_referer( 'smartseo_ai_enhanced_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'smartseo-ai' ) );
        }

        $post_ids = isset( $_POST['post_ids'] ) ? array_map( 'intval', $_POST['post_ids'] ) : array();
        $optimization_type = sanitize_text_field( $_POST['optimization_type'] ?? 'full_optimization' );
        $priority = intval( $_POST['priority'] ?? 5 );

        if ( empty( $post_ids ) ) {
            wp_send_json_error( array(
                'message' => __( 'Aucun article sélectionné.', 'smartseo-ai' ),
            ) );
        }

        $added = $this->queue_manager->add_to_queue( $post_ids, $optimization_type, $priority );

        wp_send_json_success( array(
            'message' => sprintf(
                __( '%d articles ajoutés à la queue d\'optimisation.', 'smartseo-ai' ),
                $added
            ),
            'added' => $added,
        ) );
    }

    /**
     * AJAX : Arrête l'optimisation en masse
     */
    public function ajax_stop_bulk_optimization() {
        check_ajax_referer( 'smartseo_ai_enhanced_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'smartseo-ai' ) );
        }

        $cleared = $this->queue_manager->clear_queue( 'pending' );

        wp_send_json_success( array(
            'message' => sprintf(
                __( '%d éléments supprimés de la queue.', 'smartseo-ai' ),
                $cleared
            ),
            'cleared' => $cleared,
        ) );
    }

    /**
     * Affiche les notifications admin
     */
    public function show_admin_notices() {
        // Vérifier la santé des API
        $health = $this->performance_manager->check_api_health();
        $has_errors = false;

        foreach ( $health as $provider => $status ) {
            if ( 'error' === $status['status'] ) {
                $has_errors = true;
                break;
            }
        }

        if ( $has_errors ) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>' . __( 'SmartSEO AI', 'smartseo-ai' ) . '</strong> : ';
            echo __( 'Certaines API IA ne sont pas disponibles. Vérifiez vos clés API dans les paramètres.', 'smartseo-ai' );
            echo '</p></div>';
        }

        // Vérifier la taille du cache
        $cache_stats = $this->cache_manager->get_stats();
        if ( ! empty( $cache_stats['general']['total_entries'] ) && $cache_stats['general']['total_entries'] > 800 ) {
            echo '<div class="notice notice-info is-dismissible">';
            echo '<p><strong>' . __( 'SmartSEO AI', 'smartseo-ai' ) . '</strong> : ';
            echo __( 'Le cache contient beaucoup d\'entrées. Considérez le vider pour optimiser les performances.', 'smartseo-ai' );
            echo '</p></div>';
        }
    }

    /**
     * Génère le HTML du tableau de bord amélioré
     *
     * @return string HTML du tableau de bord.
     */
    public function render_enhanced_dashboard() {
        ob_start();
        ?>
        <div class="wrap smartseo-ai-enhanced-dashboard">
            <h1><?php _e( 'Tableau de bord SmartSEO AI', 'smartseo-ai' ); ?></h1>
            
            <!-- Indicateurs de santé -->
            <div class="smartseo-health-indicators">
                <div class="health-card" id="api-health-card">
                    <h3><?php _e( 'Santé des API', 'smartseo-ai' ); ?></h3>
                    <div class="health-status" id="api-health-status">
                        <span class="spinner is-active"></span>
                    </div>
                </div>
                
                <div class="health-card" id="cache-health-card">
                    <h3><?php _e( 'Cache', 'smartseo-ai' ); ?></h3>
                    <div class="health-status" id="cache-health-status">
                        <span class="spinner is-active"></span>
                    </div>
                </div>
                
                <div class="health-card" id="queue-health-card">
                    <h3><?php _e( 'Queue d\'optimisation', 'smartseo-ai' ); ?></h3>
                    <div class="health-status" id="queue-health-status">
                        <span class="spinner is-active"></span>
                    </div>
                </div>
            </div>

            <!-- Graphiques de performance -->
            <div class="smartseo-charts-container">
                <div class="chart-card">
                    <h3><?php _e( 'Performance des API', 'smartseo-ai' ); ?></h3>
                    <canvas id="performance-chart"></canvas>
                </div>
                
                <div class="chart-card">
                    <h3><?php _e( 'Utilisation du cache', 'smartseo-ai' ); ?></h3>
                    <canvas id="cache-chart"></canvas>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="smartseo-quick-actions">
                <h3><?php _e( 'Actions rapides', 'smartseo-ai' ); ?></h3>
                <div class="action-buttons">
                    <button type="button" class="button button-secondary" id="clear-cache-btn">
                        <?php _e( 'Vider le cache', 'smartseo-ai' ); ?>
                    </button>
                    <button type="button" class="button button-secondary" id="check-api-health-btn">
                        <?php _e( 'Vérifier les API', 'smartseo-ai' ); ?>
                    </button>
                    <button type="button" class="button button-primary" id="refresh-dashboard-btn">
                        <?php _e( 'Actualiser', 'smartseo-ai' ); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
