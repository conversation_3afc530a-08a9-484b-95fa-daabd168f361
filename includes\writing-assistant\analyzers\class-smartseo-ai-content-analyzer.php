<?php
/**
 * Classe pour l'analyse du contenu
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'analyse du contenu
 */
class SmartSEO_AI_Content_Analyzer {

    /**
     * Analyse le contenu
     *
     * @param string $content Contenu à analyser.
     * @param string $title   Titre de l'article.
     * @return array Résultats de l'analyse.
     */
    public function analyze( $content, $title ) {
        // Nettoyer le contenu
        $clean_content = strip_tags( $content );
        
        // Analyser la longueur du contenu
        $content_length = $this->analyze_content_length( $clean_content );
        
        // Analyser la lisibilité
        $readability = $this->analyze_readability( $clean_content );
        
        // Analyser les paragraphes
        $paragraphs = $this->analyze_paragraphs( $content );
        
        // Créer la liste des vérifications
        $checks = array(
            array(
                'label' => __( 'Longueur du contenu', 'smartseo-ai' ),
                'status' => $content_length['status'],
                'recommendation' => $content_length['recommendation'],
            ),
            array(
                'label' => __( 'Lisibilité', 'smartseo-ai' ),
                'status' => $readability['status'],
                'recommendation' => $readability['recommendation'],
            ),
            array(
                'label' => __( 'Paragraphes', 'smartseo-ai' ),
                'status' => $paragraphs['status'],
                'recommendation' => $paragraphs['recommendation'],
            ),
        );
        
        // Calculer le score
        $score = $this->calculate_score( $content_length, $readability, $paragraphs );
        
        // Retourner les résultats
        return array(
            'score' => $score,
            'content_length' => $content_length,
            'readability' => $readability,
            'paragraphs' => $paragraphs,
            'checks' => $checks,
        );
    }

    /**
     * Analyse la longueur du contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_content_length( $content ) {
        // Compter les mots
        $word_count = str_word_count( $content );
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( $word_count >= 300 && $word_count < 600 ) {
            $status = 'average';
            $recommendation = __( 'Le contenu est de longueur moyenne. Pour un meilleur référencement, essayez d\'atteindre au moins 600 mots.', 'smartseo-ai' );
        } elseif ( $word_count >= 600 ) {
            $status = 'good';
            $recommendation = __( 'Bonne longueur de contenu pour le SEO.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'Le contenu est trop court. Pour un bon référencement, essayez d\'atteindre au moins 300 mots.', 'smartseo-ai' );
        }
        
        return array(
            'word_count' => $word_count,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Analyse la lisibilité du contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_readability( $content ) {
        // Calculer le score de lisibilité Flesch
        $flesch_score = $this->calculate_flesch_score( $content );
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        $level = '';
        
        if ( $flesch_score >= 60 && $flesch_score <= 70 ) {
            $status = 'good';
            $level = __( 'Standard', 'smartseo-ai' );
            $recommendation = __( 'Bonne lisibilité. Le contenu est facile à comprendre pour un lecteur moyen.', 'smartseo-ai' );
        } elseif ( $flesch_score > 70 ) {
            $status = 'good';
            $level = __( 'Facile', 'smartseo-ai' );
            $recommendation = __( 'Excellente lisibilité. Le contenu est très facile à comprendre.', 'smartseo-ai' );
        } elseif ( $flesch_score >= 50 && $flesch_score < 60 ) {
            $status = 'average';
            $level = __( 'Assez difficile', 'smartseo-ai' );
            $recommendation = __( 'Lisibilité moyenne. Essayez de simplifier certaines phrases pour améliorer la compréhension.', 'smartseo-ai' );
        } else {
            $level = __( 'Difficile', 'smartseo-ai' );
            $recommendation = __( 'Lisibilité faible. Le contenu est difficile à comprendre. Utilisez des phrases plus courtes et un vocabulaire plus simple.', 'smartseo-ai' );
        }
        
        return array(
            'flesch_score' => $flesch_score,
            'level' => $level,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Analyse les paragraphes du contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_paragraphs( $content ) {
        // Extraire les paragraphes
        preg_match_all( '/<p>(.*?)<\/p>/s', $content, $matches );
        $paragraphs = $matches[1];
        
        // Compter les paragraphes
        $paragraph_count = count( $paragraphs );
        
        // Calculer la longueur moyenne des paragraphes
        $total_length = 0;
        $long_paragraphs = 0;
        
        foreach ( $paragraphs as $paragraph ) {
            $length = str_word_count( strip_tags( $paragraph ) );
            $total_length += $length;
            
            if ( $length > 150 ) {
                $long_paragraphs++;
            }
        }
        
        $avg_length = $paragraph_count > 0 ? $total_length / $paragraph_count : 0;
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( $paragraph_count >= 3 && $avg_length <= 150 && $long_paragraphs === 0 ) {
            $status = 'good';
            $recommendation = __( 'Bonne structure de paragraphes. Les paragraphes sont de longueur appropriée.', 'smartseo-ai' );
        } elseif ( $paragraph_count >= 2 && $avg_length <= 200 && $long_paragraphs <= 1 ) {
            $status = 'average';
            $recommendation = __( 'Structure de paragraphes acceptable. Envisagez de diviser les paragraphes trop longs pour améliorer la lisibilité.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'Structure de paragraphes à améliorer. Utilisez plus de paragraphes et réduisez leur longueur pour améliorer la lisibilité.', 'smartseo-ai' );
        }
        
        return array(
            'paragraph_count' => $paragraph_count,
            'avg_length' => round( $avg_length ),
            'long_paragraphs' => $long_paragraphs,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Calcule le score de lisibilité Flesch
     *
     * @param string $text Texte à analyser.
     * @return float Score de lisibilité Flesch.
     */
    private function calculate_flesch_score( $text ) {
        // Nettoyer le texte
        $text = strip_tags( $text );
        
        // Compter les mots
        $word_count = str_word_count( $text );
        if ( $word_count === 0 ) {
            return 0;
        }
        
        // Compter les phrases
        $sentences = preg_split( '/[.!?]+/', $text );
        $sentence_count = count( array_filter( $sentences ) );
        if ( $sentence_count === 0 ) {
            $sentence_count = 1;
        }
        
        // Compter les syllabes (approximation)
        $syllable_count = $this->count_syllables( $text );
        
        // Calculer le score Flesch
        $words_per_sentence = $word_count / $sentence_count;
        $syllables_per_word = $syllable_count / $word_count;
        
        $flesch_score = 206.835 - ( 1.015 * $words_per_sentence ) - ( 84.6 * $syllables_per_word );
        
        // Limiter le score entre 0 et 100
        $flesch_score = max( 0, min( 100, $flesch_score ) );
        
        return round( $flesch_score, 1 );
    }

    /**
     * Compte approximativement le nombre de syllabes dans un texte
     *
     * @param string $text Texte à analyser.
     * @return int Nombre approximatif de syllabes.
     */
    private function count_syllables( $text ) {
        // Convertir en minuscules
        $text = strtolower( $text );
        
        // Remplacer les caractères non alphabétiques par des espaces
        $text = preg_replace( '/[^a-z]/', ' ', $text );
        
        // Diviser en mots
        $words = explode( ' ', $text );
        $words = array_filter( $words );
        
        $syllable_count = 0;
        
        foreach ( $words as $word ) {
            // Compter les voyelles
            $vowels = preg_match_all( '/[aeiouy]+/', $word, $matches );
            
            // Ajuster pour les diphtongues et les e muets à la fin
            if ( preg_match( '/[aeiouy]e$/', $word ) ) {
                $vowels--;
            }
            
            // Chaque mot a au moins une syllabe
            $syllable_count += max( 1, $vowels );
        }
        
        return $syllable_count;
    }

    /**
     * Calcule le score global de l'analyse de contenu
     *
     * @param array $content_length Résultats de l'analyse de longueur.
     * @param array $readability    Résultats de l'analyse de lisibilité.
     * @param array $paragraphs     Résultats de l'analyse de paragraphes.
     * @return int Score (0-100).
     */
    private function calculate_score( $content_length, $readability, $paragraphs ) {
        // Pondération des différents facteurs
        $weights = array(
            'content_length' => 0.4,
            'readability' => 0.4,
            'paragraphs' => 0.2,
        );
        
        // Convertir les statuts en scores numériques
        $scores = array(
            'content_length' => $this->status_to_score( $content_length['status'] ),
            'readability' => $this->status_to_score( $readability['status'] ),
            'paragraphs' => $this->status_to_score( $paragraphs['status'] ),
        );
        
        // Calculer le score pondéré
        $weighted_score = 
            $scores['content_length'] * $weights['content_length'] +
            $scores['readability'] * $weights['readability'] +
            $scores['paragraphs'] * $weights['paragraphs'];
        
        // Arrondir le score
        return round( $weighted_score );
    }

    /**
     * Convertit un statut en score numérique
     *
     * @param string $status Statut (good, average, poor).
     * @return int Score (0-100).
     */
    private function status_to_score( $status ) {
        switch ( $status ) {
            case 'good':
                return 100;
            case 'average':
                return 50;
            case 'poor':
                return 0;
            default:
                return 0;
        }
    }
}
