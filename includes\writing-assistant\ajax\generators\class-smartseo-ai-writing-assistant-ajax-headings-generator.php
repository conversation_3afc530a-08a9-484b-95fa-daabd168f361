<?php
/**
 * Classe de génération de titres H2/H3 pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la génération de titres H2/H3
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Headings_Generator {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Génère des titres H2/H3
     *
     * @param int    $post_id ID de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $topic   Sujet de l'article.
     * @param int    $level   Niveau des titres (2 ou 3).
     * @return array|WP_Error Résultats de la génération ou erreur.
     */
    public function generate( $post_id, $keyword, $topic, $level = 2 ) {
        // Vérifier si le mot-clé ou le sujet est fourni
        if ( empty( $keyword ) && empty( $topic ) ) {
            return new WP_Error( 'missing_data', __( 'Veuillez fournir un mot-clé ou un sujet.', 'smartseo-ai' ) );
        }

        // Utiliser le mot-clé comme sujet s'il n'est pas fourni
        if ( empty( $topic ) ) {
            $topic = $keyword;
        }

        // Récupérer les données de l'article
        $post = get_post( $post_id );
        $post_type = $post ? $post->post_type : 'post';
        $post_type_object = get_post_type_object( $post_type );
        $post_type_label = $post_type_object ? $post_type_object->labels->singular_name : __( 'Article', 'smartseo-ai' );

        // Construire le prompt
        $prompt = $this->build_prompt( $keyword, $topic, $post_type_label, $level );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        $headings = $this->process_response( $response );

        // Retourner les résultats
        return array(
            'headings' => $headings,
        );
    }

    /**
     * Construit le prompt pour l'IA
     *
     * @param string $keyword       Mot-clé principal.
     * @param string $topic         Sujet de l'article.
     * @param string $post_type_label Label du type de publication.
     * @param int    $level         Niveau des titres (2 ou 3).
     * @return string Prompt pour l'IA.
     */
    private function build_prompt( $keyword, $topic, $post_type_label, $level ) {
        $heading_type = $level === 3 ? 'H3' : 'H2';
        $count = $level === 3 ? '6 à 8' : '5 à 7';
        
        $prompt = "Génère {$count} titres {$heading_type} SEO pour structurer un {$post_type_label} sur le sujet : \"{$topic}\".";
        
        if ( ! empty( $keyword ) && $keyword !== $topic ) {
            $prompt .= " Le mot-clé principal à inclure dans certains titres est : \"{$keyword}\".";
        }
        
        $prompt .= " Les titres doivent :
1. Être optimisés pour le SEO
2. Être accrocheurs et engageants
3. Suivre une structure logique et cohérente
4. Couvrir les aspects importants du sujet
5. Inclure le mot-clé principal ou des variantes dans certains titres
6. Être concis (moins de 60 caractères)

Réponds uniquement avec les titres, un par ligne, sans numérotation ni autres explications.";

        return $prompt;
    }

    /**
     * Traite la réponse de l'IA
     *
     * @param string $response Réponse de l'IA.
     * @return array Titres générés.
     */
    private function process_response( $response ) {
        // Diviser la réponse en lignes
        $lines = explode( "\n", $response );
        
        // Filtrer les lignes vides et les lignes qui commencent par un chiffre suivi d'un point (numérotation)
        $headings = array_filter( array_map( 'trim', $lines ), function( $line ) {
            return ! empty( $line ) && ! preg_match( '/^\d+\./', $line );
        } );
        
        // Nettoyer les titres (supprimer les guillemets, les # pour les titres markdown, etc.)
        $headings = array_map( function( $heading ) {
            $heading = trim( $heading, " \t\n\r\0\x0B\"'" );
            $heading = preg_replace( '/^#+\s*/', '', $heading );
            return $heading;
        }, $headings );
        
        // Limiter à 8 titres
        $headings = array_slice( array_values( $headings ), 0, 8 );
        
        return $headings;
    }
}
