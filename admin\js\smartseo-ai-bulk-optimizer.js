/**
 * Script JavaScript pour l'optimisation en masse des articles
 */
(function($) {
    'use strict';

    // Variables globales
    let isOptimizing = false;
    let queue = [];
    let currentIndex = 0;
    let totalPosts = 0;
    let successCount = 0;
    let errorCount = 0;
    let batchSize = smartseoAiBulkOptimizer.batchSize || 1;
    let delayBetweenBatches = smartseoAiBulkOptimizer.delayBetweenBatches || 5000;
    let activeBatches = 0;
    let stopRequested = false;

    // Initialisation
    $(document).ready(function() {
        initOptimizeAllButton();
        initOptimizeSingleButtons();
        initStopButton();
        initPaginationLoader();
    });

    /**
     * Initialise le bouton d'optimisation globale
     */
    function initOptimizeAllButton() {
        $('#smartseo-ai-optimize-all').on('click', function() {
            if (isOptimizing) {
                return;
            }

            // Demander confirmation
            if (!confirm(smartseoAiBulkOptimizer.i18n.confirmOptimize)) {
                return;
            }

            // Initialiser l'optimisation
            startBulkOptimization();
        });
    }

    /**
     * Initialise le loader pour la pagination
     */
    function initPaginationLoader() {
        // Afficher le loader lors du changement de page
        $(document).on('click', '.smartseo-ai-pagination-links a', function() {
            showTableLoader();
        });

        // Afficher le loader lors du changement du nombre d'articles par page
        $('#smartseo-ai-per-page').on('change', function() {
            showTableLoader();
        });
    }

    /**
     * Initialise les boutons d'optimisation individuelle
     */
    function initOptimizeSingleButtons() {
        $('.optimize-single-post').on('click', function() {
            const button = $(this);
            const postId = button.data('post-id');

            if (button.hasClass('loading')) {
                return;
            }

            // Désactiver le bouton et afficher le chargement
            button.addClass('loading');

            // Mettre à jour le statut
            updatePostStatus(postId, 'in-progress');

            // Optimiser l'article
            optimizePost(postId)
                .then(function(response) {
                    // Mettre à jour le statut et le score
                    updatePostStatus(postId, 'optimized');
                    updatePostScore(postId, response.seo_score);

                    // Réactiver le bouton
                    button.removeClass('loading');
                })
                .catch(function(error) {
                    // Mettre à jour le statut
                    updatePostStatus(postId, 'error');

                    // Réactiver le bouton
                    button.removeClass('loading');

                    // Afficher l'erreur dans la console
                    console.error('Erreur lors de l\'optimisation :', error);

                    // Afficher un message d'erreur détaillé
                    let errorMessage = 'Erreur lors de l\'optimisation';

                    if (error.responseJSON && error.responseJSON.message) {
                        errorMessage += ' : ' + error.responseJSON.message;
                    } else if (error.statusText) {
                        errorMessage += ' : ' + error.statusText;
                    }

                    alert(errorMessage);
                });
        });
    }

    /**
     * Initialise le bouton d'arrêt
     */
    function initStopButton() {
        $('#smartseo-ai-stop-optimization').on('click', function() {
            if (!isOptimizing) {
                return;
            }

            // Demander confirmation
            if (!confirm(smartseoAiBulkOptimizer.i18n.confirmStop)) {
                return;
            }

            // Arrêter l'optimisation
            stopRequested = true;
            $('#smartseo-ai-progress-status').text('Arrêt en cours...');
        });
    }

    /**
     * Démarre l'optimisation en masse
     */
    function startBulkOptimization() {
        // Réinitialiser les variables
        isOptimizing = true;
        queue = [];
        currentIndex = 0;
        successCount = 0;
        errorCount = 0;
        activeBatches = 0;
        stopRequested = false;

        // Récupérer tous les articles non optimisés
        $('.post-row').each(function() {
            const postId = $(this).data('post-id');
            const status = $(this).hasClass('optimized') ? 'optimized' : 'not-optimized';

            if (status !== 'optimized') {
                queue.push(postId);
            }
        });

        totalPosts = queue.length;

        if (totalPosts === 0) {
            alert('Tous les articles sont déjà optimisés !');
            return;
        }

        // Afficher la barre de progression
        $('#smartseo-ai-progress-container').show();
        $('#smartseo-ai-optimize-all').hide();
        $('#smartseo-ai-stop-optimization').show();

        // Mettre à jour la barre de progression
        updateProgressBar(0);
        $('#smartseo-ai-progress-status').text(smartseoAiBulkOptimizer.i18n.optimizing);

        // Démarrer l'optimisation
        processNextBatch();
    }

    /**
     * Traite le prochain lot d'articles
     */
    function processNextBatch() {
        if (stopRequested) {
            finishOptimization();
            return;
        }

        if (currentIndex >= totalPosts) {
            if (activeBatches === 0) {
                finishOptimization();
            }
            return;
        }

        // Limiter le nombre de lots actifs
        if (activeBatches >= batchSize) {
            setTimeout(processNextBatch, 1000);
            return;
        }

        // Récupérer l'ID de l'article
        const postId = queue[currentIndex];
        currentIndex++;
        activeBatches++;

        // Mettre à jour le statut
        updatePostStatus(postId, 'in-progress');

        // Optimiser l'article
        optimizePost(postId)
            .then(function(response) {
                // Mettre à jour le statut et le score
                updatePostStatus(postId, 'optimized');
                updatePostScore(postId, response.seo_score);

                // Incrémenter le compteur de succès
                successCount++;
            })
            .catch(function(error) {
                // Mettre à jour le statut
                updatePostStatus(postId, 'error');

                // Incrémenter le compteur d'erreurs
                errorCount++;

                // Afficher l'erreur dans la console avec plus de détails
                console.error('Erreur lors de l\'optimisation de l\'article ' + postId + ' :', error);

                // Stocker les détails de l'erreur pour le rapport final
                let errorDetails = 'Article #' + postId + ' : ';

                if (error.responseJSON && error.responseJSON.message) {
                    errorDetails += error.responseJSON.message;
                } else if (error.statusText) {
                    errorDetails += error.statusText;
                } else {
                    errorDetails += 'Erreur inconnue';
                }

                // Mettre à jour le statut avec les détails de l'erreur
                $('#smartseo-ai-progress-status').text('Erreur sur article #' + postId + '. Continuation...');
            })
            .finally(function() {
                // Mettre à jour la barre de progression
                const progress = Math.round(((successCount + errorCount) / totalPosts) * 100);
                updateProgressBar(progress);

                // Décrémenter le compteur de lots actifs
                activeBatches--;

                // Attendre avant de traiter le prochain lot
                setTimeout(processNextBatch, delayBetweenBatches / batchSize);
            });

        // Traiter le prochain article immédiatement si possible
        if (activeBatches < batchSize) {
            processNextBatch();
        }
    }

    /**
     * Termine l'optimisation en masse
     */
    function finishOptimization() {
        isOptimizing = false;
        stopRequested = false;

        // Mettre à jour l'interface
        $('#smartseo-ai-optimize-all').show();
        $('#smartseo-ai-stop-optimization').hide();
        $('#smartseo-ai-progress-status').text(smartseoAiBulkOptimizer.i18n.complete);

        // Afficher un résumé détaillé
        let message = `Optimisation terminée !\n\n${successCount} articles optimisés avec succès.\n${errorCount} articles en erreur.`;

        // Ajouter des conseils de dépannage si des erreurs se sont produites
        if (errorCount > 0) {
            message += '\n\nConseil de dépannage :\n';
            message += '- Vérifiez que votre clé API est valide dans les paramètres\n';
            message += '- Vérifiez la connexion Internet\n';
            message += '- Consultez la console du navigateur pour plus de détails (F12)\n';
            message += '- Essayez d\'optimiser les articles individuellement';
        }

        alert(message);

        // Recharger la page pour mettre à jour les statistiques
        setTimeout(function() {
            window.location.reload();
        }, 2000);
    }

    /**
     * Optimise un article via l'API REST
     *
     * @param {number} postId ID de l'article à optimiser.
     * @return {Promise} Promesse résolue avec la réponse de l'API.
     */
    function optimizePost(postId) {
        return new Promise(function(resolve, reject) {
            // Vérifier si wp.apiRequest est disponible
            if (typeof wp !== 'undefined' && typeof wp.apiRequest === 'function') {
                // Utiliser wp.apiRequest si disponible
                wp.apiRequest({
                    path: '/smartseo-ai/v1/optimize',
                    method: 'POST',
                    data: {
                        post_id: postId
                    }
                })
                .done(function(response) {
                    resolve(response);
                })
                .fail(function(error) {
                    reject(error);
                });
            } else {
                // Utiliser jQuery.ajax comme alternative
                $.ajax({
                    url: smartseoAiBulkOptimizer.restUrl + '/optimize',
                    method: 'POST',
                    beforeSend: function(xhr) {
                        xhr.setRequestHeader('X-WP-Nonce', smartseoAiBulkOptimizer.nonce);
                    },
                    data: {
                        post_id: postId
                    },
                    success: function(response) {
                        resolve(response);
                    },
                    error: function(error) {
                        reject(error);
                    }
                });
            }
        });
    }

    /**
     * Met à jour le statut d'un article
     *
     * @param {number} postId ID de l'article.
     * @param {string} status Nouveau statut.
     */
    function updatePostStatus(postId, status) {
        const row = $(`#post-row-${postId}`);

        // Mettre à jour la classe de la ligne
        row.removeClass('not-optimized optimized in-progress error');
        row.addClass(status);

        // Mettre à jour le texte du statut
        let statusText = '';

        if (status === 'optimized') {
            statusText = 'Optimisé';
        } else if (status === 'in-progress') {
            statusText = 'En cours...';
        } else if (status === 'error') {
            statusText = 'Erreur';
        } else {
            statusText = 'Non optimisé';
        }

        row.find('.smartseo-ai-status-indicator')
            .removeClass('smartseo-ai-status-not-optimized smartseo-ai-status-optimized smartseo-ai-status-in-progress smartseo-ai-status-error')
            .addClass(`smartseo-ai-status-${status}`)
            .text(statusText);
    }

    /**
     * Met à jour le score SEO d'un article
     *
     * @param {number} postId ID de l'article.
     * @param {number} score  Nouveau score.
     */
    function updatePostScore(postId, score) {
        const row = $(`#post-row-${postId}`);
        const scoreCell = row.find('.column-score');

        // Déterminer la classe du score
        let scoreClass = 'poor';

        if (score >= 80) {
            scoreClass = 'good';
        } else if (score >= 50) {
            scoreClass = 'average';
        }

        // Mettre à jour le score
        scoreCell.html(`
            <div class="smartseo-ai-score-indicator smartseo-ai-score-${scoreClass}">
                ${score}/100
            </div>
        `);
    }

    /**
     * Met à jour la barre de progression
     *
     * @param {number} percent Pourcentage de progression.
     */
    function updateProgressBar(percent) {
        $('#smartseo-ai-progress-bar').css('width', `${percent}%`);
        $('#smartseo-ai-progress-text').text(`${percent}%`);
    }

    /**
     * Affiche le loader de la table
     */
    function showTableLoader() {
        $('#smartseo-ai-table-loader').show();
        $('#smartseo-ai-posts-table').css('opacity', '0.5');
    }

    /**
     * Masque le loader de la table
     */
    function hideTableLoader() {
        $('#smartseo-ai-table-loader').hide();
        $('#smartseo-ai-posts-table').css('opacity', '1');
    }

})(jQuery);
