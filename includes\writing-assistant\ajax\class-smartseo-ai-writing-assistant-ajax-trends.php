<?php
/**
 * Classe de gestion des AJAX de tendances pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les AJAX de tendances de l'Assistant de Rédaction SEO
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Trends {

    /**
     * Instance de l'API de tendances
     *
     * @var SmartSEO_AI_Trends_API
     */
    private $trends_api;

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Initialiser l'API de tendances
        $this->trends_api = new SmartSEO_AI_Trends_API();

        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Récupère les tendances
     */
    public function get_trends() {
        // Vérifier le nonce
        if ( ! check_ajax_referer( 'smartseo_ai_writing_assistant_nonce', 'nonce', false ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page.', 'smartseo-ai' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les données
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';

        // Vérifier si le mot-clé est fourni
        if ( empty( $keyword ) ) {
            wp_send_json_error( array( 'message' => __( 'Veuillez fournir un mot-clé.', 'smartseo-ai' ) ) );
        }

        // Récupérer les tendances
        $results = $this->get_trending_topics( $keyword );

        // Vérifier si la récupération a réussi
        if ( is_wp_error( $results ) ) {
            wp_send_json_error( array( 'message' => $results->get_error_message() ) );
        }

        // Envoyer la réponse
        wp_send_json_success( $results );
    }

    /**
     * Récupère les sujets tendances
     *
     * @param string $keyword Mot-clé principal.
     * @return array|WP_Error Résultats des tendances ou erreur.
     */
    private function get_trending_topics( $keyword ) {
        // Vérifier si l'API de tendances est disponible
        if ( $this->trends_api->is_available() ) {
            // Appeler l'API de tendances
            $response = $this->trends_api->get_trends( $keyword );

            if ( is_wp_error( $response ) ) {
                // Utiliser l'IA comme solution de secours
                return $this->get_ai_trends( $keyword );
            }

            return $response;
        }

        // Utiliser l'IA comme solution de secours
        return $this->get_ai_trends( $keyword );
    }

    /**
     * Récupère les tendances via l'IA
     *
     * @param string $keyword Mot-clé principal.
     * @return array|WP_Error Résultats des tendances ou erreur.
     */
    private function get_ai_trends( $keyword ) {
        // Construire le prompt
        $prompt = $this->build_trends_prompt( $keyword );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        return $this->process_trends_response( $response );
    }

    /**
     * Construit le prompt pour les tendances
     *
     * @param string $keyword Mot-clé principal.
     * @return string Prompt pour l'IA.
     */
    private function build_trends_prompt( $keyword ) {
        $prompt = "Identifie 8 sujets tendances actuels liés au mot-clé \"{$keyword}\".";
        
        $prompt .= " Pour chaque sujet tendance, fournis les informations suivantes :
1. Le sujet tendance
2. La popularité (élevée, moyenne, faible)
3. La saisonnalité (toute l'année, saisonnier, événementiel)
4. Une brève explication de pourquoi ce sujet est tendance

Réponds au format JSON avec la structure suivante :
{
  \"trending_topics\": [
    {
      \"topic\": \"sujet tendance 1\",
      \"popularity\": \"popularité\",
      \"seasonality\": \"saisonnalité\",
      \"explanation\": \"explication\"
    },
    ...
  ]
}";

        return $prompt;
    }

    /**
     * Traite la réponse des tendances
     *
     * @param string $response Réponse de l'IA.
     * @return array Résultats des tendances.
     */
    private function process_trends_response( $response ) {
        // Extraire le JSON de la réponse
        $json_start = strpos( $response, '{' );
        $json_end = strrpos( $response, '}' );
        
        if ( $json_start === false || $json_end === false ) {
            // Créer une réponse par défaut
            return $this->create_default_trends_response();
        }
        
        $json = substr( $response, $json_start, $json_end - $json_start + 1 );
        
        // Décoder le JSON
        $data = json_decode( $json, true );
        
        if ( json_last_error() !== JSON_ERROR_NONE ) {
            // Créer une réponse par défaut
            return $this->create_default_trends_response();
        }
        
        // Vérifier si les données sont valides
        if ( ! isset( $data['trending_topics'] ) ) {
            // Créer une réponse par défaut
            return $this->create_default_trends_response();
        }
        
        return $data;
    }

    /**
     * Crée une réponse par défaut pour les tendances
     *
     * @return array Réponse par défaut.
     */
    private function create_default_trends_response() {
        return array(
            'trending_topics' => array(
                array(
                    'topic' => __( 'Contenu généré par IA', 'smartseo-ai' ),
                    'popularity' => __( 'élevée', 'smartseo-ai' ),
                    'seasonality' => __( 'toute l\'année', 'smartseo-ai' ),
                    'explanation' => __( 'L\'utilisation de l\'IA pour générer du contenu est un sujet très discuté dans le domaine du SEO.', 'smartseo-ai' ),
                ),
                array(
                    'topic' => __( 'Optimisation pour la recherche vocale', 'smartseo-ai' ),
                    'popularity' => __( 'moyenne', 'smartseo-ai' ),
                    'seasonality' => __( 'toute l\'année', 'smartseo-ai' ),
                    'explanation' => __( 'De plus en plus d\'utilisateurs utilisent la recherche vocale, ce qui nécessite une optimisation spécifique.', 'smartseo-ai' ),
                ),
                array(
                    'topic' => __( 'SEO local', 'smartseo-ai' ),
                    'popularity' => __( 'élevée', 'smartseo-ai' ),
                    'seasonality' => __( 'toute l\'année', 'smartseo-ai' ),
                    'explanation' => __( 'L\'optimisation pour les recherches locales est cruciale pour les entreprises ayant une présence physique.', 'smartseo-ai' ),
                ),
            ),
        );
    }
}
