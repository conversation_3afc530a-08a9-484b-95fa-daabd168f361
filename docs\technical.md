# Documentation technique de l'Assistant de Rédaction SEO

Cette documentation technique est destinée aux développeurs qui souhaitent étendre ou modifier l'Assistant de Rédaction SEO.

## Architecture

L'Assistant de Rédaction SEO est construit selon une architecture modulaire. Il est composé des éléments suivants :

- **Namespace global** : `window.SmartSEOAI` est le namespace global qui contient tous les modules et sous-modules.
- **Modules principaux** : Les modules principaux sont des objets JavaScript qui gèrent des fonctionnalités spécifiques.
- **Sous-modules** : Les sous-modules sont des objets JavaScript qui gèrent des fonctionnalités plus spécifiques au sein d'un module principal.
- **Hooks et filtres** : Des hooks et des filtres sont disponibles pour étendre ou modifier les fonctionnalités.

### Modules principaux

Les modules principaux sont les suivants :

- `UIManager` : Gère l'interface utilisateur.
- `ContentGenerator` : <PERSON>ère la génération de contenu.
- `KeywordAnalyzer` : Gère l'analyse de mots-clés.
- `LiveAnalyzer` : Gère l'analyse en temps réel.
- `TrendsManager` : Gère les tendances et les sujets populaires.

### Sous-modules UI

Les sous-modules UI sont les suivants :

- `SidebarUI` : Gère la barre latérale.
- `NotificationUI` : Gère les notifications.
- `ModalUI` : Gère les modales.
- `AnimationUI` : Gère les animations.
- `GutenbergSidebar` : Gère la barre latérale de l'éditeur Gutenberg.
- `ClassicEditorSidebar` : Gère la barre latérale de l'éditeur classique.
- `GutenbergContentPanel` : Gère le panneau de contenu de l'éditeur Gutenberg.
- `ClassicContentPanel` : Gère le panneau de contenu de l'éditeur classique.

## Fichiers

Les fichiers sont organisés comme suit :

- `includes/writing-assistant/` : Contient les fichiers PHP de l'Assistant de Rédaction SEO.
  - `class-smartseo-ai-writing-assistant.php` : Classe principale de l'Assistant de Rédaction SEO.
  - `class-smartseo-ai-writing-assistant-assets.php` : Gère les assets (scripts et styles).
  - `class-smartseo-ai-writing-assistant-ajax.php` : Gère les requêtes AJAX.
  - `class-smartseo-ai-writing-assistant-hooks.php` : Gère les hooks WordPress.
  - `class-smartseo-ai-writing-assistant-metaboxes.php` : Gère les métaboxes.
  - `ajax/` : Contient les gestionnaires AJAX.
    - `class-smartseo-ai-writing-assistant-ajax-content-generator.php` : Gère les requêtes AJAX pour la génération de contenu.
    - `class-smartseo-ai-writing-assistant-ajax-content-analyzer.php` : Gère les requêtes AJAX pour l'analyse de contenu.
    - `class-smartseo-ai-writing-assistant-ajax-keyword-analyzer.php` : Gère les requêtes AJAX pour l'analyse de mots-clés.
    - `class-smartseo-ai-writing-assistant-ajax-trends.php` : Gère les requêtes AJAX pour les tendances.
- `admin/js/` : Contient les fichiers JavaScript.
  - `smartseo-ai-namespace.js` : Définit le namespace global.
  - `smartseo-ai-writing-assistant.js` : Script principal.
  - `smartseo-ai-init.js` : Script d'initialisation.
  - `smartseo-ai-ui-init.js` : Script d'initialisation de l'interface utilisateur.
  - `smartseo-ai-debug.js` : Script de débogage.
  - `smartseo-ai-test.js` : Script de test.
  - `smartseo-ai-gutenberg-editor-init.js` : Script d'initialisation pour l'éditeur Gutenberg.
  - `smartseo-ai-classic-editor-init.js` : Script d'initialisation pour l'éditeur classique.
  - `smartseo-ai-metabox-init.js` : Script d'initialisation pour la métabox.
  - `modules/` : Contient les modules JavaScript.
    - `ui-manager.js` : Module de gestion de l'interface utilisateur.
    - `content-generator.js` : Module de génération de contenu.
    - `keyword-analyzer.js` : Module d'analyse de mots-clés.
    - `live-analyzer.js` : Module d'analyse en temps réel.
    - `trends-manager.js` : Module de gestion des tendances.
    - `ui/` : Contient les sous-modules UI.
      - `sidebar-ui.js` : Sous-module de gestion de la barre latérale.
      - `notification-ui.js` : Sous-module de gestion des notifications.
      - `modal-ui.js` : Sous-module de gestion des modales.
      - `animation-ui.js` : Sous-module de gestion des animations.
      - `gutenberg-sidebar.js` : Sous-module de gestion de la barre latérale de l'éditeur Gutenberg.
      - `classic-editor-sidebar.js` : Sous-module de gestion de la barre latérale de l'éditeur classique.
      - `gutenberg-content-panel.js` : Sous-module de gestion du panneau de contenu de l'éditeur Gutenberg.
      - `classic-content-panel.js` : Sous-module de gestion du panneau de contenu de l'éditeur classique.
- `admin/css/` : Contient les fichiers CSS.
  - `smartseo-ai-writing-assistant.css` : Styles principaux.
  - `smartseo-ai-force-display.css` : Styles pour forcer l'affichage.
  - `smartseo-ai-gutenberg-editor.css` : Styles pour l'éditeur Gutenberg.
  - `smartseo-ai-gutenberg-sidebar.css` : Styles pour la barre latérale de l'éditeur Gutenberg.
  - `smartseo-ai-classic-editor.css` : Styles pour l'éditeur classique.
  - `smartseo-ai-metabox.css` : Styles pour la métabox.
  - `smartseo-ai-test.css` : Styles pour les tests.
- `admin/partials/` : Contient les fichiers de template.
  - `smartseo-ai-classic-editor-sidebar.php` : Template pour la barre latérale de l'éditeur classique.
  - `smartseo-ai-gutenberg-editor-sidebar.php` : Template pour la barre latérale de l'éditeur Gutenberg.

## Hooks et filtres

Les hooks et filtres suivants sont disponibles :

### Hooks d'action

- `smartseo_ai_writing_assistant_init` : Exécuté lors de l'initialisation de l'Assistant de Rédaction SEO.
- `smartseo_ai_writing_assistant_enqueue_scripts` : Exécuté lors du chargement des scripts.
- `smartseo_ai_writing_assistant_enqueue_styles` : Exécuté lors du chargement des styles.
- `smartseo_ai_writing_assistant_register_metaboxes` : Exécuté lors de l'enregistrement des métaboxes.
- `smartseo_ai_writing_assistant_save_post` : Exécuté lors de la sauvegarde d'un article.
- `smartseo_ai_writing_assistant_before_generate_content` : Exécuté avant la génération de contenu.
- `smartseo_ai_writing_assistant_after_generate_content` : Exécuté après la génération de contenu.
- `smartseo_ai_writing_assistant_before_analyze_keywords` : Exécuté avant l'analyse de mots-clés.
- `smartseo_ai_writing_assistant_after_analyze_keywords` : Exécuté après l'analyse de mots-clés.
- `smartseo_ai_writing_assistant_before_analyze_content` : Exécuté avant l'analyse de contenu.
- `smartseo_ai_writing_assistant_after_analyze_content` : Exécuté après l'analyse de contenu.
- `smartseo_ai_writing_assistant_before_get_trends` : Exécuté avant la récupération des tendances.
- `smartseo_ai_writing_assistant_after_get_trends` : Exécuté après la récupération des tendances.

### Filtres

- `smartseo_ai_writing_assistant_content_types` : Filtre pour modifier les types de contenu disponibles.
- `smartseo_ai_writing_assistant_generate_content` : Filtre pour modifier le contenu généré.
- `smartseo_ai_writing_assistant_analyze_keywords` : Filtre pour modifier les résultats de l'analyse de mots-clés.
- `smartseo_ai_writing_assistant_analyze_content` : Filtre pour modifier les résultats de l'analyse de contenu.
- `smartseo_ai_writing_assistant_get_trends` : Filtre pour modifier les résultats des tendances.
- `smartseo_ai_writing_assistant_post_types` : Filtre pour modifier les types de publication où l'Assistant de Rédaction SEO est disponible.
- `smartseo_ai_writing_assistant_metabox_title` : Filtre pour modifier le titre de la métabox.
- `smartseo_ai_writing_assistant_metabox_context` : Filtre pour modifier le contexte de la métabox.
- `smartseo_ai_writing_assistant_metabox_priority` : Filtre pour modifier la priorité de la métabox.
- `smartseo_ai_writing_assistant_sidebar_title` : Filtre pour modifier le titre de la barre latérale.
- `smartseo_ai_writing_assistant_sidebar_icon` : Filtre pour modifier l'icône de la barre latérale.

## API JavaScript

L'API JavaScript est accessible via le namespace global `window.SmartSEOAI`. Voici les principales méthodes disponibles :

### UIManager

- `init()` : Initialise le gestionnaire de l'interface utilisateur.
- `initGutenbergSidebar()` : Initialise la barre latérale pour Gutenberg.
- `initClassicEditorSidebar()` : Initialise la barre latérale pour l'éditeur classique.
- `addWritingAssistantInterface()` : Ajoute l'interface de l'Assistant de Rédaction SEO.
- `isGutenbergEditor()` : Vérifie si l'éditeur Gutenberg est actif.
- `getPostId()` : Récupère l'ID de l'article en cours d'édition.
- `showSuccess(message)` : Affiche un message de succès.
- `showError(message)` : Affiche un message d'erreur.
- `showInfo(message)` : Affiche un message d'information.
- `showWarning(message)` : Affiche un message d'avertissement.
- `showModal(title, content, options)` : Affiche une modale.
- `closeModal()` : Ferme la modale.
- `showLoading(message)` : Affiche un chargement.
- `hideLoading()` : Masque le chargement.
- `applySuggestion(content, target)` : Applique une suggestion.

### ContentGenerator

- `init()` : Initialise le générateur de contenu.
- `generateContent(type, postId, keyword)` : Génère du contenu.
- `optimizeAll(postId, keyword)` : Optimise tout le contenu.
- `checkPlagiarism(content)` : Vérifie la non-duplication du contenu.

### KeywordAnalyzer

- `init()` : Initialise l'analyseur de mots-clés.
- `analyzeKeywords(postId, content, keyword)` : Analyse les mots-clés.

### LiveAnalyzer

- `init()` : Initialise l'analyseur en temps réel.
- `analyzeNow()` : Analyse le contenu en temps réel.

### TrendsManager

- `init()` : Initialise le gestionnaire de tendances.
- `getTrends(keyword)` : Récupère les tendances.

## Débogage

Pour faciliter le débogage, un script de débogage est disponible en mode développement. Il ajoute un bouton de débogage en bas à droite de la page. Cliquez sur ce bouton pour afficher des informations de débogage dans la console.

Vous pouvez également utiliser le script de test pour vérifier que tout fonctionne correctement. Il ajoute un bouton de test en bas à gauche de la page. Cliquez sur ce bouton pour exécuter des tests et afficher les résultats dans la console.

## Conclusion

Cette documentation technique vous a présenté l'architecture, les fichiers, les hooks et filtres, et l'API JavaScript de l'Assistant de Rédaction SEO. Utilisez ces informations pour étendre ou modifier les fonctionnalités selon vos besoins.
