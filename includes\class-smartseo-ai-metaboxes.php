<?php
/**
 * Classe pour gérer les métaboxes du plugin SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les métaboxes pour l'éditeur classique
 */
class SmartSEO_AI_Metaboxes {

    /**
     * Constructeur
     */
    public function __construct() {
        // Ajouter les métaboxes
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );

        // Sauvegarder les données des métaboxes
        add_action( 'save_post', array( $this, 'save_meta_boxes' ), 10, 2 );

        // Charger les assets pour les métaboxes
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_metabox_assets' ) );

        // Ajouter la page de paramètres de la meta box
        add_action( 'admin_menu', array( $this, 'add_metabox_settings_page' ) );
    }

    /**
     * Ajoute les métaboxes à l'éditeur classique
     */
    public function add_meta_boxes() {
        // Types de publication supportés
        $post_types = array( 'post', 'page' );

        // Récupérer les paramètres de position et priorité
        $position = get_option( 'smartseo_ai_metabox_position', 'side' );
        $priority = get_option( 'smartseo_ai_metabox_priority', 'high' );

        // Ajouter la métabox principale
        add_meta_box(
            'smartseo_ai_metabox',
            __( 'SmartSEO AI - Optimisation SEO', 'smartseo-ai' ),
            array( $this, 'render_main_metabox' ),
            $post_types,
            $position,
            $priority
        );
    }

    /**
     * Affiche la métabox principale
     *
     * @param WP_Post $post Objet post actuel.
     */
    public function render_main_metabox( $post ) {
        // Récupérer les valeurs existantes
        $meta_description = get_post_meta( $post->ID, 'smartseo_ai_meta_description', true );
        $keywords = get_post_meta( $post->ID, 'smartseo_ai_keywords', true );
        $seo_title = get_post_meta( $post->ID, 'smartseo_ai_seo_title', true );
        $seo_slug = get_post_meta( $post->ID, 'smartseo_ai_seo_slug', true );
        $og_title = get_post_meta( $post->ID, 'smartseo_ai_og_title', true );
        $og_description = get_post_meta( $post->ID, 'smartseo_ai_og_description', true );
        $og_image = get_post_meta( $post->ID, 'smartseo_ai_og_image', true );
        $seo_advice = get_post_meta( $post->ID, 'smartseo_ai_seo_advice', true );
        $seo_score = get_post_meta( $post->ID, 'smartseo_ai_seo_score', true );
        
        // Nonce pour la sécurité
        wp_nonce_field( 'smartseo_ai_save_meta', 'smartseo_ai_meta_nonce' );
        
        // Afficher le formulaire
        ?>
        <div class="smartseo-ai-metabox-enhanced">
            <!-- En-tête avec bouton d'optimisation et score -->
            <div class="smartseo-ai-header">
                <div class="smartseo-ai-optimize-section">
                    <button type="button" id="smartseo-ai-optimize-button" class="smartseo-ai-optimize-btn">
                        <span class="smartseo-ai-btn-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                                <path d="M19 15L20.09 18.26L24 19L20.09 19.74L19 23L17.91 19.74L14 19L17.91 18.26L19 15Z" fill="currentColor"/>
                                <path d="M5 15L6.09 18.26L10 19L6.09 19.74L5 23L3.91 19.74L0 19L3.91 18.26L5 15Z" fill="currentColor"/>
                            </svg>
                        </span>
                        <span class="smartseo-ai-btn-text"><?php _e( 'Optimiser avec l\'IA', 'smartseo-ai' ); ?></span>
                        <span class="smartseo-ai-btn-loader">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                    <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </span>
                    </button>
                    <div id="smartseo-ai-optimize-status" class="smartseo-ai-status"></div>
                </div>

                <?php if ( ! empty( $seo_score ) ) : ?>
                    <div class="smartseo-ai-score-display">
                        <div class="smartseo-ai-score-circle">
                            <svg class="smartseo-ai-score-svg" width="80" height="80" viewBox="0 0 80 80">
                                <circle cx="40" cy="40" r="35" fill="none" stroke="#e6e6e6" stroke-width="6"/>
                                <circle cx="40" cy="40" r="35" fill="none"
                                        stroke="<?php echo $this->get_score_color( $seo_score ); ?>"
                                        stroke-width="6"
                                        stroke-linecap="round"
                                        stroke-dasharray="<?php echo 2 * pi() * 35; ?>"
                                        stroke-dashoffset="<?php echo 2 * pi() * 35 * (1 - $seo_score / 100); ?>"
                                        transform="rotate(-90 40 40)"/>
                            </svg>
                            <div class="smartseo-ai-score-text">
                                <span class="smartseo-ai-score-number"><?php echo esc_html( $seo_score ); ?></span>
                                <span class="smartseo-ai-score-max">/100</span>
                            </div>
                        </div>
                        <div class="smartseo-ai-score-label">
                            <span class="smartseo-ai-score-title"><?php _e( 'Score SEO', 'smartseo-ai' ); ?></span>
                            <span class="smartseo-ai-score-status smartseo-ai-score-<?php echo esc_attr( $this->get_score_class( $seo_score ) ); ?>">
                                <?php echo esc_html( $this->get_score_label( $seo_score ) ); ?>
                            </span>
                        </div>
                    </div>
                <?php else : ?>
                    <div class="smartseo-ai-score-placeholder">
                        <div class="smartseo-ai-score-icon">
                            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="#ddd"/>
                            </svg>
                        </div>
                        <div class="smartseo-ai-score-placeholder-text">
                            <span><?php _e( 'Optimisez pour obtenir', 'smartseo-ai' ); ?></span>
                            <span><?php _e( 'votre score SEO', 'smartseo-ai' ); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Section des champs SEO -->
            <div class="smartseo-ai-fields-section">
                <div class="smartseo-ai-section-header">
                    <h3><?php _e( 'Métadonnées SEO', 'smartseo-ai' ); ?></h3>
                    <p><?php _e( 'Optimisez vos métadonnées pour améliorer votre référencement', 'smartseo-ai' ); ?></p>
                </div>

                <div class="smartseo-ai-fields-grid">
                    <!-- Titre SEO -->
                    <div class="smartseo-ai-field-group">
                        <div class="smartseo-ai-field-header">
                            <label for="smartseo_ai_seo_title" class="smartseo-ai-field-label">
                                <span class="smartseo-ai-field-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5 4v3h5.5v12h3V7H19V4z" fill="currentColor"/>
                                    </svg>
                                </span>
                                <?php _e( 'Titre SEO', 'smartseo-ai' ); ?>
                            </label>
                            <div class="smartseo-ai-field-counter">
                                <span id="smartseo-ai-title-counter" class="counter-number"><?php echo strlen( $seo_title ); ?></span>
                                <span class="counter-separator">/</span>
                                <span class="counter-max">60</span>
                            </div>
                        </div>
                        <div class="smartseo-ai-field-wrapper">
                            <input type="text"
                                   id="smartseo_ai_seo_title"
                                   name="smartseo_ai_seo_title"
                                   value="<?php echo esc_attr( $seo_title ); ?>"
                                   class="smartseo-ai-input"
                                   maxlength="60"
                                   placeholder="<?php _e( 'Titre optimisé pour les moteurs de recherche', 'smartseo-ai' ); ?>" />
                            <div class="smartseo-ai-field-progress">
                                <div class="smartseo-ai-progress-bar" id="title-progress-bar" style="width: <?php echo min( 100, ( strlen( $seo_title ) / 60 ) * 100 ); ?>%"></div>
                            </div>
                        </div>
                        <div class="smartseo-ai-field-help">
                            <span class="smartseo-ai-help-icon">💡</span>
                            <?php _e( 'Idéal entre 50-60 caractères. Incluez votre mot-clé principal.', 'smartseo-ai' ); ?>
                        </div>
                    </div>

                    <!-- Méta description -->
                    <div class="smartseo-ai-field-group">
                        <div class="smartseo-ai-field-header">
                            <label for="smartseo_ai_meta_description" class="smartseo-ai-field-label">
                                <span class="smartseo-ai-field-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                                    </svg>
                                </span>
                                <?php _e( 'Méta description', 'smartseo-ai' ); ?>
                            </label>
                            <div class="smartseo-ai-field-counter">
                                <span id="smartseo-ai-description-counter" class="counter-number"><?php echo strlen( $meta_description ); ?></span>
                                <span class="counter-separator">/</span>
                                <span class="counter-max">160</span>
                            </div>
                        </div>
                        <div class="smartseo-ai-field-wrapper">
                            <textarea id="smartseo_ai_meta_description"
                                      name="smartseo_ai_meta_description"
                                      class="smartseo-ai-textarea"
                                      rows="3"
                                      maxlength="160"
                                      placeholder="<?php _e( 'Description engageante qui apparaîtra dans les résultats de recherche', 'smartseo-ai' ); ?>"><?php echo esc_textarea( $meta_description ); ?></textarea>
                            <div class="smartseo-ai-field-progress">
                                <div class="smartseo-ai-progress-bar" id="description-progress-bar" style="width: <?php echo min( 100, ( strlen( $meta_description ) / 160 ) * 100 ); ?>%"></div>
                            </div>
                        </div>
                        <div class="smartseo-ai-field-help">
                            <span class="smartseo-ai-help-icon">💡</span>
                            <?php _e( 'Idéal entre 120-160 caractères. Décrivez le contenu de manière attrayante.', 'smartseo-ai' ); ?>
                        </div>
                    </div>

                    <!-- Mots-clés -->
                    <div class="smartseo-ai-field-group">
                        <div class="smartseo-ai-field-header">
                            <label for="smartseo_ai_keywords" class="smartseo-ai-field-label">
                                <span class="smartseo-ai-field-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" fill="currentColor"/>
                                    </svg>
                                </span>
                                <?php _e( 'Mots-clés', 'smartseo-ai' ); ?>
                            </label>
                            <div class="smartseo-ai-keywords-count">
                                <span id="smartseo-ai-keywords-counter"><?php echo ! empty( $keywords ) ? count( explode( ',', $keywords ) ) : 0; ?></span> <?php _e( 'mots-clés', 'smartseo-ai' ); ?>
                            </div>
                        </div>
                        <div class="smartseo-ai-field-wrapper">
                            <input type="text"
                                   id="smartseo_ai_keywords"
                                   name="smartseo_ai_keywords"
                                   value="<?php echo esc_attr( $keywords ); ?>"
                                   class="smartseo-ai-input"
                                   placeholder="<?php _e( 'seo, optimisation, contenu, référencement', 'smartseo-ai' ); ?>" />
                            <div class="smartseo-ai-keywords-tags" id="smartseo-ai-keywords-tags"></div>
                        </div>
                        <div class="smartseo-ai-field-help">
                            <span class="smartseo-ai-help-icon">💡</span>
                            <?php _e( 'Séparez les mots-clés par des virgules. 3-5 mots-clés recommandés.', 'smartseo-ai' ); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section avancée -->
            <div class="smartseo-ai-advanced-section">
                <div class="smartseo-ai-section-header">
                    <h3><?php _e( 'Paramètres avancés', 'smartseo-ai' ); ?></h3>
                    <p><?php _e( 'Configuration avancée pour un contrôle précis', 'smartseo-ai' ); ?></p>
                </div>

                <div class="smartseo-ai-fields-grid">
                    <!-- Slug URL -->
                    <div class="smartseo-ai-field-group">
                        <div class="smartseo-ai-field-header">
                            <label for="smartseo_ai_seo_slug" class="smartseo-ai-field-label">
                                <span class="smartseo-ai-field-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.9,12C3.9,10.29 5.29,8.9 7,8.9H11V7H7A5,5 0 0,0 2,12A5,5 0 0,0 7,17H11V15.1H7C5.29,15.1 3.9,13.71 3.9,12M8,13H16V11H8V13M17,7H13V8.9H17C18.71,8.9 20.1,10.29 20.1,12C20.1,13.71 18.71,15.1 17,15.1H13V17H17A5,5 0 0,0 22,12A5,5 0 0,0 17,7Z" fill="currentColor"/>
                                    </svg>
                                </span>
                                <?php _e( 'Slug URL optimisé', 'smartseo-ai' ); ?>
                            </label>
                            <button type="button" id="smartseo-ai-apply-slug" class="smartseo-ai-action-btn">
                                <?php _e( 'Appliquer', 'smartseo-ai' ); ?>
                            </button>
                        </div>
                        <div class="smartseo-ai-field-wrapper">
                            <input type="text"
                                   id="smartseo_ai_seo_slug"
                                   name="smartseo_ai_seo_slug"
                                   value="<?php echo esc_attr( $seo_slug ); ?>"
                                   class="smartseo-ai-input"
                                   placeholder="<?php _e( 'slug-url-optimise-seo', 'smartseo-ai' ); ?>" />
                        </div>
                        <div class="smartseo-ai-field-help">
                            <span class="smartseo-ai-help-icon">💡</span>
                            <?php _e( 'URL courte et descriptive avec des mots-clés séparés par des tirets.', 'smartseo-ai' ); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Open Graph -->
            <div class="smartseo-ai-opengraph-section">
                <div class="smartseo-ai-section-header">
                    <h3>
                        <span class="smartseo-ai-section-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M5,3H19A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5A2,2 0 0,1 3,19V5A2,2 0 0,1 5,3M18,5H12V9H18V5M10,5H6V9H10V5M6,11V15H10V11H6M12,11V15H18V11H12M6,17V19H10V17H6M12,17V19H18V17H12Z" fill="currentColor"/>
                            </svg>
                        </span>
                        <?php _e( 'Open Graph & Réseaux Sociaux', 'smartseo-ai' ); ?>
                    </h3>
                    <p><?php _e( 'Optimisez l\'apparence de votre contenu sur les réseaux sociaux', 'smartseo-ai' ); ?></p>
                </div>

                <div class="smartseo-ai-fields-grid">
                    <!-- Titre OG -->
                    <div class="smartseo-ai-field-group">
                        <div class="smartseo-ai-field-header">
                            <label for="smartseo_ai_og_title" class="smartseo-ai-field-label">
                                <span class="smartseo-ai-field-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M18.5,4L19.66,8.35L24,9.5L19.66,10.65L18.5,15L17.34,10.65L13,9.5L17.34,8.35L18.5,4M6.5,12L7.66,16.35L12,17.5L7.66,18.65L6.5,23L5.34,18.65L1,17.5L5.34,16.35L6.5,12Z" fill="currentColor"/>
                                    </svg>
                                </span>
                                <?php _e( 'Titre Open Graph', 'smartseo-ai' ); ?>
                            </label>
                        </div>
                        <div class="smartseo-ai-field-wrapper">
                            <input type="text"
                                   id="smartseo_ai_og_title"
                                   name="smartseo_ai_og_title"
                                   value="<?php echo esc_attr( $og_title ); ?>"
                                   class="smartseo-ai-input"
                                   placeholder="<?php _e( 'Titre pour les réseaux sociaux', 'smartseo-ai' ); ?>" />
                        </div>
                        <div class="smartseo-ai-field-help">
                            <span class="smartseo-ai-help-icon">📱</span>
                            <?php _e( 'Titre qui apparaîtra lors du partage sur Facebook, Twitter, etc.', 'smartseo-ai' ); ?>
                        </div>
                    </div>

                    <!-- Description OG -->
                    <div class="smartseo-ai-field-group">
                        <div class="smartseo-ai-field-header">
                            <label for="smartseo_ai_og_description" class="smartseo-ai-field-label">
                                <span class="smartseo-ai-field-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                                    </svg>
                                </span>
                                <?php _e( 'Description Open Graph', 'smartseo-ai' ); ?>
                            </label>
                        </div>
                        <div class="smartseo-ai-field-wrapper">
                            <textarea id="smartseo_ai_og_description"
                                      name="smartseo_ai_og_description"
                                      class="smartseo-ai-textarea"
                                      rows="3"
                                      placeholder="<?php _e( 'Description engageante pour les réseaux sociaux', 'smartseo-ai' ); ?>"><?php echo esc_textarea( $og_description ); ?></textarea>
                        </div>
                        <div class="smartseo-ai-field-help">
                            <span class="smartseo-ai-help-icon">📱</span>
                            <?php _e( 'Description qui accompagnera le partage sur les réseaux sociaux.', 'smartseo-ai' ); ?>
                        </div>
                    </div>

                    <!-- Image OG -->
                    <div class="smartseo-ai-field-group smartseo-ai-field-full-width">
                        <div class="smartseo-ai-field-header">
                            <label for="smartseo_ai_og_image" class="smartseo-ai-field-label">
                                <span class="smartseo-ai-field-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" fill="currentColor"/>
                                    </svg>
                                </span>
                                <?php _e( 'Image Open Graph', 'smartseo-ai' ); ?>
                            </label>
                            <button type="button" id="smartseo-ai-select-image" class="smartseo-ai-action-btn">
                                <span class="smartseo-ai-btn-icon">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9,16V10H5L12,3L19,10H15V16H9M5,20V18H19V20H5Z" fill="currentColor"/>
                                    </svg>
                                </span>
                                <?php _e( 'Sélectionner', 'smartseo-ai' ); ?>
                            </button>
                        </div>
                        <div class="smartseo-ai-field-wrapper">
                            <input type="text"
                                   id="smartseo_ai_og_image"
                                   name="smartseo_ai_og_image"
                                   value="<?php echo esc_url( $og_image ); ?>"
                                   class="smartseo-ai-input"
                                   placeholder="<?php _e( 'URL de l\'image pour les réseaux sociaux', 'smartseo-ai' ); ?>" />
                            <?php if ( ! empty( $og_image ) ) : ?>
                                <div class="smartseo-ai-image-preview">
                                    <img src="<?php echo esc_url( $og_image ); ?>" alt="<?php _e( 'Aperçu image OG', 'smartseo-ai' ); ?>" />
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="smartseo-ai-field-help">
                            <span class="smartseo-ai-help-icon">📱</span>
                            <?php _e( 'Image recommandée : 1200x630 pixels. Formats supportés : JPG, PNG.', 'smartseo-ai' ); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section des conseils SEO -->
            <?php if ( ! empty( $seo_advice ) ) : ?>
                <div class="smartseo-ai-advice-section">
                    <div class="smartseo-ai-section-header">
                        <h3>
                            <span class="smartseo-ai-section-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z" fill="currentColor"/>
                                </svg>
                            </span>
                            <?php _e( 'Conseils d\'optimisation IA', 'smartseo-ai' ); ?>
                        </h3>
                        <p><?php _e( 'Recommandations personnalisées pour améliorer votre SEO', 'smartseo-ai' ); ?></p>
                    </div>
                    <div class="smartseo-ai-advice-content">
                        <?php echo wp_kses_post( $seo_advice ); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Sauvegarde les données des métaboxes
     *
     * @param int     $post_id ID du post.
     * @param WP_Post $post    Objet post.
     */
    public function save_meta_boxes( $post_id, $post ) {
        // Vérifier le nonce
        if ( ! isset( $_POST['smartseo_ai_meta_nonce'] ) || ! wp_verify_nonce( $_POST['smartseo_ai_meta_nonce'], 'smartseo_ai_save_meta' ) ) {
            return;
        }
        
        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }
        
        // Éviter les sauvegardes automatiques
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }
        
        // Sauvegarder les champs
        $fields = array(
            'smartseo_ai_meta_description',
            'smartseo_ai_keywords',
            'smartseo_ai_seo_title',
            'smartseo_ai_seo_slug',
            'smartseo_ai_og_title',
            'smartseo_ai_og_description',
            'smartseo_ai_og_image',
        );
        
        foreach ( $fields as $field ) {
            if ( isset( $_POST[$field] ) ) {
                update_post_meta( $post_id, $field, sanitize_text_field( $_POST[$field] ) );
            }
        }
    }

    /**
     * Retourne la classe CSS en fonction du score SEO
     *
     * @param int $score Score SEO.
     * @return string Classe CSS.
     */
    private function get_score_class( $score ) {
        if ( $score >= 80 ) {
            return 'excellent';
        } elseif ( $score >= 60 ) {
            return 'good';
        } elseif ( $score >= 40 ) {
            return 'average';
        } else {
            return 'poor';
        }
    }

    /**
     * Retourne la couleur en fonction du score SEO
     *
     * @param int $score Score SEO.
     * @return string Couleur hexadécimale.
     */
    private function get_score_color( $score ) {
        if ( $score >= 80 ) {
            return '#4caf50'; // Vert
        } elseif ( $score >= 60 ) {
            return '#2196f3'; // Bleu
        } elseif ( $score >= 40 ) {
            return '#ff9800'; // Orange
        } else {
            return '#f44336'; // Rouge
        }
    }

    /**
     * Retourne le label en fonction du score SEO
     *
     * @param int $score Score SEO.
     * @return string Label du score.
     */
    private function get_score_label( $score ) {
        if ( $score >= 80 ) {
            return __( 'Excellent', 'smartseo-ai' );
        } elseif ( $score >= 60 ) {
            return __( 'Bon', 'smartseo-ai' );
        } elseif ( $score >= 40 ) {
            return __( 'Moyen', 'smartseo-ai' );
        } else {
            return __( 'Faible', 'smartseo-ai' );
        }
    }

    /**
     * Enregistre les assets pour les métaboxes
     *
     * @param string $hook Hook de la page actuelle.
     */
    public function enqueue_metabox_assets( $hook ) {
        // Vérifier si nous sommes sur une page d'édition de post
        if ( ! in_array( $hook, array( 'post.php', 'post-new.php' ), true ) ) {
            return;
        }

        // Vérifier si on utilise l'interface améliorée
        $use_enhanced = get_option( 'smartseo_ai_enhanced_metabox', true );

        if ( $use_enhanced ) {
            // Enregistrer les styles améliorés
            wp_enqueue_style(
                'smartseo-ai-metabox-enhanced',
                SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-metabox-enhanced.css',
                array(),
                SMARTSEO_AI_VERSION
            );

            // Enregistrer le script amélioré
            wp_enqueue_script(
                'smartseo-ai-metabox-enhanced',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-metabox-enhanced.js',
                array( 'jquery', 'wp-media' ),
                SMARTSEO_AI_VERSION,
                true
            );

            // Localiser le script
            wp_localize_script(
                'smartseo-ai-metabox-enhanced',
                'smartseoAiMetabox',
                array(
                    'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                    'restUrl' => rest_url( 'smartseo-ai/v1' ),
                    'nonce' => wp_create_nonce( 'wp_rest' ),
                    'i18n' => array(
                        'optimizing' => __( 'Optimisation en cours...', 'smartseo-ai' ),
                        'success' => __( 'Optimisation réussie !', 'smartseo-ai' ),
                        'error' => __( 'Erreur lors de l\'optimisation.', 'smartseo-ai' ),
                        'selectImage' => __( 'Sélectionner une image', 'smartseo-ai' ),
                        'useThisImage' => __( 'Utiliser cette image', 'smartseo-ai' ),
                        'removeImage' => __( 'Supprimer l\'image', 'smartseo-ai' ),
                    ),
                )
            );
        } else {
            // Charger les assets classiques
            wp_enqueue_style(
                'smartseo-ai-metabox',
                SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-metabox.css',
                array(),
                SMARTSEO_AI_VERSION
            );

            wp_enqueue_script(
                'smartseo-ai-metabox',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-metabox.js',
                array( 'jquery', 'wp-media' ),
                SMARTSEO_AI_VERSION,
                true
            );
        }
    }

    /**
     * Ajoute la page de paramètres de la meta box
     */
    public function add_metabox_settings_page() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Paramètres Meta Box', 'smartseo-ai' ),
            __( 'Paramètres Meta Box', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-metabox-settings',
            array( $this, 'render_metabox_settings_page' )
        );
    }

    /**
     * Affiche la page de paramètres de la meta box
     */
    public function render_metabox_settings_page() {
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/metabox-settings.php';
    }
}
