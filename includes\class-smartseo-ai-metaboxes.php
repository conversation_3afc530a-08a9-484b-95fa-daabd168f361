<?php
/**
 * Classe pour gérer les métaboxes du plugin SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les métaboxes pour l'éditeur classique
 */
class SmartSEO_AI_Metaboxes {

    /**
     * Constructeur
     */
    public function __construct() {
        // Ajouter les métaboxes
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
        
        // Sauvegarder les données des métaboxes
        add_action( 'save_post', array( $this, 'save_meta_boxes' ), 10, 2 );
    }

    /**
     * Ajoute les métaboxes à l'éditeur classique
     */
    public function add_meta_boxes() {
        // Types de publication supportés
        $post_types = array( 'post', 'page' );
        
        // Ajouter la métabox principale
        add_meta_box(
            'smartseo_ai_metabox',
            __( 'SmartSEO AI - Optimisation SEO', 'smartseo-ai' ),
            array( $this, 'render_main_metabox' ),
            $post_types,
            'normal',
            'high'
        );
    }

    /**
     * Affiche la métabox principale
     *
     * @param WP_Post $post Objet post actuel.
     */
    public function render_main_metabox( $post ) {
        // Récupérer les valeurs existantes
        $meta_description = get_post_meta( $post->ID, 'smartseo_ai_meta_description', true );
        $keywords = get_post_meta( $post->ID, 'smartseo_ai_keywords', true );
        $seo_title = get_post_meta( $post->ID, 'smartseo_ai_seo_title', true );
        $seo_slug = get_post_meta( $post->ID, 'smartseo_ai_seo_slug', true );
        $og_title = get_post_meta( $post->ID, 'smartseo_ai_og_title', true );
        $og_description = get_post_meta( $post->ID, 'smartseo_ai_og_description', true );
        $og_image = get_post_meta( $post->ID, 'smartseo_ai_og_image', true );
        $seo_advice = get_post_meta( $post->ID, 'smartseo_ai_seo_advice', true );
        $seo_score = get_post_meta( $post->ID, 'smartseo_ai_seo_score', true );
        
        // Nonce pour la sécurité
        wp_nonce_field( 'smartseo_ai_save_meta', 'smartseo_ai_meta_nonce' );
        
        // Afficher le formulaire
        ?>
        <div class="smartseo-ai-metabox">
            <div class="smartseo-ai-optimize-button-container">
                <button type="button" id="smartseo-ai-optimize-button" class="button button-primary button-large">
                    <span class="dashicons dashicons-superhero"></span>
                    <?php _e( 'Optimiser avec l\'IA', 'smartseo-ai' ); ?>
                </button>
                <div id="smartseo-ai-optimize-status" class="smartseo-ai-status"></div>
            </div>
            
            <?php if ( ! empty( $seo_score ) ) : ?>
                <div class="smartseo-ai-score-container">
                    <div class="smartseo-ai-score smartseo-ai-score-<?php echo esc_attr( $this->get_score_class( $seo_score ) ); ?>">
                        <?php echo esc_html( $seo_score ); ?>/100
                    </div>
                    <div class="smartseo-ai-score-label">
                        <?php _e( 'Score SEO', 'smartseo-ai' ); ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="smartseo-ai-fields-container">
                <div class="smartseo-ai-field">
                    <label for="smartseo_ai_seo_title">
                        <?php _e( 'Titre SEO', 'smartseo-ai' ); ?>
                        <span class="smartseo-ai-char-count" data-max="60">
                            <span class="current"><?php echo strlen( $seo_title ); ?></span>/60
                        </span>
                    </label>
                    <input type="text" id="smartseo_ai_seo_title" name="smartseo_ai_seo_title" value="<?php echo esc_attr( $seo_title ); ?>" maxlength="60" />
                </div>
                
                <div class="smartseo-ai-field">
                    <label for="smartseo_ai_meta_description">
                        <?php _e( 'Meta Description', 'smartseo-ai' ); ?>
                        <span class="smartseo-ai-char-count" data-max="160">
                            <span class="current"><?php echo strlen( $meta_description ); ?></span>/160
                        </span>
                    </label>
                    <textarea id="smartseo_ai_meta_description" name="smartseo_ai_meta_description" rows="3" maxlength="160"><?php echo esc_textarea( $meta_description ); ?></textarea>
                </div>
                
                <div class="smartseo-ai-field">
                    <label for="smartseo_ai_keywords">
                        <?php _e( 'Mots-clés', 'smartseo-ai' ); ?>
                    </label>
                    <input type="text" id="smartseo_ai_keywords" name="smartseo_ai_keywords" value="<?php echo esc_attr( $keywords ); ?>" />
                    <p class="description"><?php _e( 'Séparez les mots-clés par des virgules.', 'smartseo-ai' ); ?></p>
                </div>
                
                <div class="smartseo-ai-field">
                    <label for="smartseo_ai_seo_slug">
                        <?php _e( 'Slug URL optimisé', 'smartseo-ai' ); ?>
                    </label>
                    <input type="text" id="smartseo_ai_seo_slug" name="smartseo_ai_seo_slug" value="<?php echo esc_attr( $seo_slug ); ?>" />
                    <button type="button" id="smartseo-ai-apply-slug" class="button">
                        <?php _e( 'Appliquer le slug', 'smartseo-ai' ); ?>
                    </button>
                </div>
                
                <h3><?php _e( 'Données Open Graph', 'smartseo-ai' ); ?></h3>
                
                <div class="smartseo-ai-field">
                    <label for="smartseo_ai_og_title">
                        <?php _e( 'Titre OG', 'smartseo-ai' ); ?>
                    </label>
                    <input type="text" id="smartseo_ai_og_title" name="smartseo_ai_og_title" value="<?php echo esc_attr( $og_title ); ?>" />
                </div>
                
                <div class="smartseo-ai-field">
                    <label for="smartseo_ai_og_description">
                        <?php _e( 'Description OG', 'smartseo-ai' ); ?>
                    </label>
                    <textarea id="smartseo_ai_og_description" name="smartseo_ai_og_description" rows="3"><?php echo esc_textarea( $og_description ); ?></textarea>
                </div>
                
                <div class="smartseo-ai-field">
                    <label for="smartseo_ai_og_image">
                        <?php _e( 'Image OG', 'smartseo-ai' ); ?>
                    </label>
                    <input type="text" id="smartseo_ai_og_image" name="smartseo_ai_og_image" value="<?php echo esc_url( $og_image ); ?>" />
                    <button type="button" id="smartseo-ai-select-image" class="button">
                        <?php _e( 'Sélectionner une image', 'smartseo-ai' ); ?>
                    </button>
                </div>
                
                <?php if ( ! empty( $seo_advice ) ) : ?>
                    <div class="smartseo-ai-field">
                        <label>
                            <?php _e( 'Conseils d\'optimisation SEO', 'smartseo-ai' ); ?>
                        </label>
                        <div class="smartseo-ai-advice">
                            <?php echo wp_kses_post( $seo_advice ); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Sauvegarde les données des métaboxes
     *
     * @param int     $post_id ID du post.
     * @param WP_Post $post    Objet post.
     */
    public function save_meta_boxes( $post_id, $post ) {
        // Vérifier le nonce
        if ( ! isset( $_POST['smartseo_ai_meta_nonce'] ) || ! wp_verify_nonce( $_POST['smartseo_ai_meta_nonce'], 'smartseo_ai_save_meta' ) ) {
            return;
        }
        
        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }
        
        // Éviter les sauvegardes automatiques
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }
        
        // Sauvegarder les champs
        $fields = array(
            'smartseo_ai_meta_description',
            'smartseo_ai_keywords',
            'smartseo_ai_seo_title',
            'smartseo_ai_seo_slug',
            'smartseo_ai_og_title',
            'smartseo_ai_og_description',
            'smartseo_ai_og_image',
        );
        
        foreach ( $fields as $field ) {
            if ( isset( $_POST[$field] ) ) {
                update_post_meta( $post_id, $field, sanitize_text_field( $_POST[$field] ) );
            }
        }
    }

    /**
     * Retourne la classe CSS en fonction du score SEO
     *
     * @param int $score Score SEO.
     * @return string Classe CSS.
     */
    private function get_score_class( $score ) {
        if ( $score >= 80 ) {
            return 'good';
        } elseif ( $score >= 50 ) {
            return 'average';
        } else {
            return 'poor';
        }
    }
}
