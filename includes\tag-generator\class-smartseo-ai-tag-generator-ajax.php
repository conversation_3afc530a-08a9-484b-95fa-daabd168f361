<?php
/**
 * Classe pour les requêtes AJAX du Générateur de Tags IA
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les requêtes AJAX du Générateur de Tags IA
 */
class SmartSEO_AI_Tag_Generator_Ajax {

    /**
     * Initialise les requêtes AJAX
     */
    public function init() {
        // Ajouter les hooks
        add_action( 'wp_ajax_smartseo_ai_generate_tags', array( $this, 'ajax_generate_tags' ) );
        add_action( 'wp_ajax_smartseo_ai_apply_tags', array( $this, 'ajax_apply_tags' ) );
        add_action( 'wp_ajax_smartseo_ai_get_optimal_combination', array( $this, 'ajax_get_optimal_combination' ) );
    }

    /**
     * Génère des tags via AJAX
     */
    public function ajax_generate_tags() {
        // Vérifier le nonce
        check_ajax_referer( 'smartseo_ai_tag_generator_nonce', 'nonce' );
        
        // Vérifier les permissions
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }
        
        // Récupérer les paramètres
        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $content = isset( $_POST['content'] ) ? sanitize_textarea_field( $_POST['content'] ) : '';
        $title = isset( $_POST['title'] ) ? sanitize_text_field( $_POST['title'] ) : '';
        $category = isset( $_POST['category'] ) ? sanitize_text_field( $_POST['category'] ) : '';
        $include_brands = isset( $_POST['include_brands'] ) ? (bool) $_POST['include_brands'] : true;
        $include_regions = isset( $_POST['include_regions'] ) ? (bool) $_POST['include_regions'] : true;
        $use_external_api = isset( $_POST['use_external_api'] ) ? (bool) $_POST['use_external_api'] : false;
        $external_api = isset( $_POST['external_api'] ) ? sanitize_text_field( $_POST['external_api'] ) : '';
        
        // Vérifier que l'article existe
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            wp_send_json_error( array( 'message' => __( 'Article introuvable.', 'smartseo-ai' ) ) );
        }
        
        // Si le contenu n'est pas fourni, utiliser le contenu de l'article
        if ( empty( $content ) ) {
            $content = $post->post_content;
        }
        
        // Si le titre n'est pas fourni, utiliser le titre de l'article
        if ( empty( $title ) ) {
            $title = $post->post_title;
        }
        
        // Si la catégorie n'est pas fournie, utiliser la catégorie de l'article
        if ( empty( $category ) ) {
            $categories = get_the_category( $post_id );
            
            if ( ! empty( $categories ) ) {
                $category = $categories[0]->name;
            }
        }
        
        // Préparer les options
        $options = array(
            'include_brands' => $include_brands,
            'include_regions' => $include_regions,
            'max_tags' => 20,
            'use_external_api' => $use_external_api,
            'external_api' => $external_api,
        );
        
        // Générer les tags
        $tag_generator = SmartSEO_AI_Tag_Generator::get_instance();
        $tags = $tag_generator->generate_tags( $post_id, $content, $title, $category, $options );
        
        if ( is_wp_error( $tags ) ) {
            wp_send_json_error( array( 'message' => $tags->get_error_message() ) );
        }
        
        // Suggérer une combinaison optimale
        $optimal_tags = $tag_generator->suggest_optimal_combination( $tags );
        
        // Envoyer la réponse
        wp_send_json_success( array(
            'tags' => $tags,
            'optimal_tags' => $optimal_tags,
        ) );
    }

    /**
     * Applique des tags via AJAX
     */
    public function ajax_apply_tags() {
        // Vérifier le nonce
        check_ajax_referer( 'smartseo_ai_tag_generator_nonce', 'nonce' );
        
        // Vérifier les permissions
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }
        
        // Récupérer les paramètres
        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $tags = isset( $_POST['tags'] ) ? $_POST['tags'] : array();
        
        // Vérifier que l'article existe
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            wp_send_json_error( array( 'message' => __( 'Article introuvable.', 'smartseo-ai' ) ) );
        }
        
        // Vérifier que les tags sont valides
        if ( ! is_array( $tags ) || empty( $tags ) ) {
            wp_send_json_error( array( 'message' => __( 'Tags invalides.', 'smartseo-ai' ) ) );
        }
        
        // Sanitize les tags
        $sanitized_tags = array();
        
        foreach ( $tags as $tag ) {
            if ( isset( $tag['name'] ) && isset( $tag['score'] ) ) {
                $sanitized_tags[] = array(
                    'name' => sanitize_text_field( $tag['name'] ),
                    'score' => intval( $tag['score'] ),
                );
            }
        }
        
        // Appliquer les tags
        $tag_generator = SmartSEO_AI_Tag_Generator::get_instance();
        $result = $tag_generator->apply_tags( $post_id, $sanitized_tags );
        
        if ( ! $result ) {
            wp_send_json_error( array( 'message' => __( 'Une erreur s\'est produite lors de l\'application des tags.', 'smartseo-ai' ) ) );
        }
        
        // Envoyer la réponse
        wp_send_json_success( array(
            'message' => __( 'Tags appliqués avec succès.', 'smartseo-ai' ),
        ) );
    }

    /**
     * Récupère une combinaison optimale de tags via AJAX
     */
    public function ajax_get_optimal_combination() {
        // Vérifier le nonce
        check_ajax_referer( 'smartseo_ai_tag_generator_nonce', 'nonce' );
        
        // Vérifier les permissions
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }
        
        // Récupérer les paramètres
        $tags = isset( $_POST['tags'] ) ? $_POST['tags'] : array();
        
        // Vérifier que les tags sont valides
        if ( ! is_array( $tags ) || empty( $tags ) ) {
            wp_send_json_error( array( 'message' => __( 'Tags invalides.', 'smartseo-ai' ) ) );
        }
        
        // Sanitize les tags
        $sanitized_tags = array();
        
        foreach ( $tags as $tag ) {
            if ( isset( $tag['name'] ) && isset( $tag['score'] ) ) {
                $sanitized_tags[] = array(
                    'name' => sanitize_text_field( $tag['name'] ),
                    'score' => intval( $tag['score'] ),
                );
            }
        }
        
        // Suggérer une combinaison optimale
        $tag_generator = SmartSEO_AI_Tag_Generator::get_instance();
        $optimal_tags = $tag_generator->suggest_optimal_combination( $sanitized_tags );
        
        // Envoyer la réponse
        wp_send_json_success( array(
            'optimal_tags' => $optimal_tags,
        ) );
    }
}
