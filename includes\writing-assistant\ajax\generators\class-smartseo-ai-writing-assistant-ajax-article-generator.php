<?php
/**
 * Classe de génération d'articles complets pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la génération d'articles complets
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Article_Generator {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Génère un article complet
     *
     * @param int    $post_id ID de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $topic   Sujet de l'article.
     * @return array|WP_Error Résultats de la génération ou erreur.
     */
    public function generate( $post_id, $keyword, $topic ) {
        // Vérifier si le mot-clé ou le sujet est fourni
        if ( empty( $keyword ) && empty( $topic ) ) {
            return new WP_Error( 'missing_data', __( 'Veuillez fournir un mot-clé ou un sujet.', 'smartseo-ai' ) );
        }

        // Utiliser le mot-clé comme sujet s'il n'est pas fourni
        if ( empty( $topic ) ) {
            $topic = $keyword;
        }

        // Récupérer les données de l'article
        $post = get_post( $post_id );
        $post_type = $post ? $post->post_type : 'post';
        $post_type_object = get_post_type_object( $post_type );
        $post_type_label = $post_type_object ? $post_type_object->labels->singular_name : __( 'Article', 'smartseo-ai' );

        // Construire le prompt
        $prompt = $this->build_prompt( $keyword, $topic, $post_type_label );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        $article = $this->process_response( $response );

        // Retourner les résultats
        return array(
            'article' => $article,
        );
    }

    /**
     * Construit le prompt pour l'IA
     *
     * @param string $keyword       Mot-clé principal.
     * @param string $topic         Sujet de l'article.
     * @param string $post_type_label Label du type de publication.
     * @return string Prompt pour l'IA.
     */
    private function build_prompt( $keyword, $topic, $post_type_label ) {
        $prompt = "Génère un article complet optimisé pour le SEO sur le sujet : \"{$topic}\".";
        
        if ( ! empty( $keyword ) && $keyword !== $topic ) {
            $prompt .= " Le mot-clé principal à inclure est : \"{$keyword}\".";
        }
        
        $prompt .= " L'article doit :
1. Être optimisé pour le SEO
2. Avoir une introduction captivante
3. Être structuré avec des titres H2 et H3
4. Inclure le mot-clé principal et des variantes de manière naturelle
5. Contenir environ 800 à 1000 mots
6. Utiliser un ton professionnel mais accessible
7. Inclure des faits, statistiques ou exemples pertinents
8. Se terminer par une conclusion avec un appel à l'action

Utilise le format HTML pour structurer l'article, avec des balises <h2> pour les sections principales et <h3> pour les sous-sections. Utilise des balises <p> pour les paragraphes et <ul> ou <ol> pour les listes si nécessaire.";

        return $prompt;
    }

    /**
     * Traite la réponse de l'IA
     *
     * @param string $response Réponse de l'IA.
     * @return string Article généré.
     */
    private function process_response( $response ) {
        // Nettoyer la réponse
        $article = trim( $response );
        
        // Convertir le markdown en HTML si nécessaire
        if ( strpos( $article, '#' ) !== false && strpos( $article, '<h' ) === false ) {
            // Convertir les titres
            $article = preg_replace( '/^# (.*?)$/m', '<h1>$1</h1>', $article );
            $article = preg_replace( '/^## (.*?)$/m', '<h2>$1</h2>', $article );
            $article = preg_replace( '/^### (.*?)$/m', '<h3>$1</h3>', $article );
            $article = preg_replace( '/^#### (.*?)$/m', '<h4>$1</h4>', $article );
            
            // Convertir les listes
            $article = preg_replace( '/^- (.*?)$/m', '<li>$1</li>', $article );
            $article = preg_replace( '/^[0-9]+\. (.*?)$/m', '<li>$1</li>', $article );
            $article = preg_replace( '/(<li>.*?<\/li>\n)+/s', '<ul>$0</ul>', $article );
            
            // Convertir les paragraphes
            $article = preg_replace( '/^([^<].*?)$/m', '<p>$1</p>', $article );
            
            // Nettoyer les balises imbriquées
            $article = preg_replace( '/<p><(h[1-6]|ul|ol|li)>/i', '<$1>', $article );
            $article = preg_replace( '/<\/(h[1-6]|ul|ol|li)><\/p>/i', '</$1>', $article );
            $article = preg_replace( '/<p><\/p>/', '', $article );
        }
        
        return $article;
    }
}
