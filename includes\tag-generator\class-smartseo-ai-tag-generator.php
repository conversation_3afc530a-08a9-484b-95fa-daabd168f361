<?php
/**
 * Classe principale du Générateur de Tags IA
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère le Générateur de Tags IA
 */
class SmartSEO_AI_Tag_Generator {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_Tag_Generator
     */
    private static $instance = null;

    /**
     * Constructeur
     */
    private function __construct() {
        // Charger les dépendances
        $this->load_dependencies();

        // Ajouter les hooks
        $this->add_hooks();
    }

    /**
     * Récupère l'instance de la classe
     *
     * @return SmartSEO_AI_Tag_Generator Instance de la classe.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Charge les dépendances
     */
    private function load_dependencies() {
        // Charger les classes nécessaires
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/tag-generator/class-smartseo-ai-tag-generator-metabox.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/tag-generator/class-smartseo-ai-tag-generator-ajax.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/tag-generator/class-smartseo-ai-tag-generator-assets.php';
    }

    /**
     * Ajoute les hooks
     */
    private function add_hooks() {
        // Initialiser les classes
        $metabox = new SmartSEO_AI_Tag_Generator_Metabox();
        $ajax = new SmartSEO_AI_Tag_Generator_Ajax();
        $assets = new SmartSEO_AI_Tag_Generator_Assets();

        // Ajouter les hooks
        add_action( 'admin_init', array( $metabox, 'init' ) );
        add_action( 'admin_init', array( $ajax, 'init' ) );
        add_action( 'admin_init', array( $assets, 'init' ) );
    }

    /**
     * Génère des tags pour un article
     *
     * @param int    $post_id   ID de l'article.
     * @param string $content   Contenu de l'article.
     * @param string $title     Titre de l'article.
     * @param string $category  Catégorie de l'article.
     * @param array  $options   Options de génération.
     * @return array|WP_Error   Tags générés ou erreur.
     */
    public function generate_tags( $post_id, $content, $title, $category = '', $options = array() ) {
        // Options par défaut
        $default_options = array(
            'include_brands' => true,
            'include_regions' => true,
            'max_tags' => 20,
            'use_external_api' => false,
            'external_api' => '',
        );

        // Fusionner les options
        $options = wp_parse_args( $options, $default_options );

        // Récupérer les tags existants
        $existing_tags = wp_get_post_tags( $post_id, array( 'fields' => 'names' ) );

        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $ai_api = new SmartSEO_AI_API();
        } else {
            $ai_api = new SmartSEO_AI_Gemini();
        }

        // Préparer le prompt pour l'IA
        $prompt = $this->prepare_prompt( $title, $content, $category, $existing_tags, $options );

        // Générer les tags avec l'IA
        $response = $ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse pour extraire les tags
        $tags = $this->process_ai_response( $response, $options );

        // Enrichir les tags avec des données externes si demandé
        if ( $options['use_external_api'] ) {
            $tags = $this->enrich_tags_with_external_data( $tags, $options['external_api'] );
        }

        return $tags;
    }

    /**
     * Prépare le prompt pour l'IA
     *
     * @param string $title         Titre de l'article.
     * @param string $content       Contenu de l'article.
     * @param string $category      Catégorie de l'article.
     * @param array  $existing_tags Tags existants.
     * @param array  $options       Options de génération.
     * @return string               Prompt pour l'IA.
     */
    private function prepare_prompt( $title, $content, $category, $existing_tags, $options ) {
        // Limiter la taille du contenu pour éviter de dépasser les limites de l'API
        $content = substr( $content, 0, 3000 );

        // Construire le prompt
        $prompt = "Génère une liste de tags pertinents pour un article WordPress avec les caractéristiques suivantes:\n\n";
        $prompt .= "Titre: $title\n\n";
        
        if ( ! empty( $category ) ) {
            $prompt .= "Catégorie: $category\n\n";
        }
        
        $prompt .= "Contenu: $content\n\n";
        
        if ( ! empty( $existing_tags ) ) {
            $prompt .= "Tags existants à éviter: " . implode( ', ', $existing_tags ) . "\n\n";
        }
        
        $prompt .= "Consignes:\n";
        $prompt .= "- Génère exactement " . $options['max_tags'] . " tags maximum (moins si pertinent)\n";
        $prompt .= "- Les tags doivent être courts (1 à 3 mots maximum)\n";
        $prompt .= "- Les tags doivent être pertinents pour le référencement SEO\n";
        $prompt .= "- Chaque tag doit être accompagné d'un score de pertinence de 0 à 100\n";
        
        if ( ! $options['include_brands'] ) {
            $prompt .= "- N'inclus PAS de noms de marques ou de produits\n";
        }
        
        if ( ! $options['include_regions'] ) {
            $prompt .= "- N'inclus PAS de noms de régions ou de lieux\n";
        }
        
        $prompt .= "\nFormate ta réponse comme suit:\n";
        $prompt .= "tag1 (score)\ntag2 (score)\ntag3 (score)\n...\n";
        $prompt .= "Exemple: marketing digital (95)\n";
        
        return $prompt;
    }

    /**
     * Traite la réponse de l'IA pour extraire les tags
     *
     * @param string $response Réponse de l'IA.
     * @param array  $options  Options de génération.
     * @return array           Tags générés avec leurs scores.
     */
    private function process_ai_response( $response, $options ) {
        $tags = array();
        
        // Diviser la réponse en lignes
        $lines = explode( "\n", $response );
        
        foreach ( $lines as $line ) {
            // Ignorer les lignes vides
            if ( empty( trim( $line ) ) ) {
                continue;
            }
            
            // Extraire le tag et le score
            if ( preg_match( '/^(.*?)\s*\((\d+)\)$/', trim( $line ), $matches ) ) {
                $tag = trim( $matches[1] );
                $score = intval( $matches[2] );
                
                // Vérifier que le tag n'est pas vide et que le score est valide
                if ( ! empty( $tag ) && $score >= 0 && $score <= 100 ) {
                    $tags[] = array(
                        'name' => $tag,
                        'score' => $score,
                    );
                }
            }
        }
        
        // Trier les tags par score décroissant
        usort( $tags, function( $a, $b ) {
            return $b['score'] - $a['score'];
        } );
        
        // Limiter le nombre de tags
        $tags = array_slice( $tags, 0, $options['max_tags'] );
        
        return $tags;
    }

    /**
     * Enrichit les tags avec des données externes
     *
     * @param array  $tags        Tags générés.
     * @param string $external_api API externe à utiliser.
     * @return array              Tags enrichis.
     */
    private function enrich_tags_with_external_data( $tags, $external_api ) {
        // Si aucune API externe n'est spécifiée, retourner les tags tels quels
        if ( empty( $external_api ) ) {
            return $tags;
        }
        
        // Récupérer les noms des tags
        $tag_names = array_column( $tags, 'name' );
        
        // Enrichir les tags en fonction de l'API externe
        switch ( $external_api ) {
            case 'google_trends':
                $enriched_tags = $this->enrich_with_google_trends( $tag_names );
                break;
                
            case 'semrush':
                $enriched_tags = $this->enrich_with_semrush( $tag_names );
                break;
                
            default:
                return $tags;
        }
        
        // Fusionner les données enrichies avec les tags existants
        foreach ( $tags as $key => $tag ) {
            if ( isset( $enriched_tags[ $tag['name'] ] ) ) {
                $tags[ $key ]['trend_score'] = $enriched_tags[ $tag['name'] ];
            }
        }
        
        return $tags;
    }

    /**
     * Enrichit les tags avec les données de Google Trends
     *
     * @param array $tag_names Noms des tags.
     * @return array           Scores de tendance.
     */
    private function enrich_with_google_trends( $tag_names ) {
        // Cette fonction est un placeholder pour l'intégration avec Google Trends
        // Dans une implémentation réelle, vous devriez utiliser l'API Google Trends
        
        $trend_scores = array();
        
        foreach ( $tag_names as $tag ) {
            // Simuler un score de tendance
            $trend_scores[ $tag ] = rand( 0, 100 );
        }
        
        return $trend_scores;
    }

    /**
     * Enrichit les tags avec les données de Semrush
     *
     * @param array $tag_names Noms des tags.
     * @return array           Scores de tendance.
     */
    private function enrich_with_semrush( $tag_names ) {
        // Cette fonction est un placeholder pour l'intégration avec Semrush
        // Dans une implémentation réelle, vous devriez utiliser l'API Semrush
        
        $trend_scores = array();
        
        foreach ( $tag_names as $tag ) {
            // Simuler un score de tendance
            $trend_scores[ $tag ] = rand( 0, 100 );
        }
        
        return $trend_scores;
    }

    /**
     * Applique les tags à un article
     *
     * @param int   $post_id ID de l'article.
     * @param array $tags    Tags à appliquer.
     * @return bool          Succès ou échec.
     */
    public function apply_tags( $post_id, $tags ) {
        // Vérifier que l'article existe
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            return false;
        }
        
        // Extraire les noms des tags
        $tag_names = array_column( $tags, 'name' );
        
        // Appliquer les tags à l'article
        $result = wp_set_post_tags( $post_id, $tag_names, true );
        
        return ! is_wp_error( $result );
    }

    /**
     * Suggère une combinaison optimale de tags
     *
     * @param array $tags Tags générés.
     * @return array      Combinaison optimale de tags.
     */
    public function suggest_optimal_combination( $tags ) {
        // Cette fonction est un placeholder pour la suggestion de combinaison optimale
        // Dans une implémentation réelle, vous devriez utiliser un algorithme plus sophistiqué
        
        // Trier les tags par score décroissant
        usort( $tags, function( $a, $b ) {
            return $b['score'] - $a['score'];
        } );
        
        // Sélectionner les 5 meilleurs tags
        $optimal_tags = array_slice( $tags, 0, 5 );
        
        return $optimal_tags;
    }
}
