/**
 * Module d'analyse de mots-clés
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire d'analyse de mots-clés
     */
    const KeywordAnalyzer = {
        /**
         * Initialise le gestionnaire d'analyse de mots-clés
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Analyse les mots-clés
         * @param {number} postId  ID de l'article
         * @param {string} content Contenu à analyser
         * @param {string} keyword Mot-clé principal
         */
        analyzeKeywords: function(postId, content, keyword) {
            // Afficher le chargement
            window.SmartSEOAI.UIManager.showLoading(smartseoAiWritingAssistant.i18n.analyzing, 'keyword');
            
            // Récupérer le contenu si non fourni
            if (!content) {
                if (window.SmartSEOAI.UIManager.isGutenbergEditor()) {
                    content = wp.data.select('core/editor').getEditedPostContent();
                } else {
                    if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                        content = tinyMCE.get('content').getContent();
                    } else {
                        content = $('#content').val();
                    }
                }
            }
            
            // Préparer les données
            const data = {
                action: 'smartseo_ai_analyze_keywords',
                nonce: smartseoAiWritingAssistant.nonce,
                post_id: postId,
                content: content,
                keyword: keyword
            };
            
            // Envoyer la requête AJAX
            $.ajax({
                url: smartseoAiWritingAssistant.ajaxUrl,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading('keyword');
                    
                    if (response.success) {
                        // Afficher les résultats
                        window.SmartSEOAI.UIManager.showKeywordResults(response.data);
                        
                        // Afficher un message de succès
                        window.SmartSEOAI.UIManager.showSuccess(smartseoAiWritingAssistant.i18n.success);
                    } else {
                        // Afficher un message d'erreur
                        window.SmartSEOAI.UIManager.showError(response.data.message || smartseoAiWritingAssistant.i18n.error);
                    }
                },
                error: function(xhr, status, error) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading('keyword');
                    
                    // Afficher un message d'erreur
                    window.SmartSEOAI.UIManager.showError(smartseoAiWritingAssistant.i18n.error + ': ' + error);
                }
            });
        },

        /**
         * Calcule la densité d'un mot-clé dans un contenu
         * @param {string} content Contenu à analyser
         * @param {string} keyword Mot-clé à rechercher
         * @return {Object} Résultats de l'analyse
         */
        calculateKeywordDensity: function(content, keyword) {
            // Nettoyer le contenu
            const cleanContent = this.stripTags(content).toLowerCase();
            
            // Compter le nombre total de mots
            const totalWords = cleanContent.split(/\s+/).filter(word => word.length > 0).length;
            
            // Compter les occurrences du mot-clé
            const keywordRegex = new RegExp('\\b' + keyword.toLowerCase() + '\\b', 'g');
            const keywordCount = (cleanContent.match(keywordRegex) || []).length;
            
            // Calculer la densité
            const density = totalWords > 0 ? (keywordCount / totalWords) * 100 : 0;
            
            // Déterminer le statut
            let status = 'poor';
            
            if (density >= 0.5 && density <= 2.5) {
                status = 'good';
            } else if (density > 0 && density < 0.5) {
                status = 'average';
            } else if (density > 2.5 && density <= 3.5) {
                status = 'average';
            }
            
            return {
                keyword: keyword,
                count: keywordCount,
                total_words: totalWords,
                density: density.toFixed(2),
                status: status
            };
        },

        /**
         * Analyse le placement d'un mot-clé dans un contenu
         * @param {string} content          Contenu à analyser
         * @param {string} title            Titre de l'article
         * @param {string} meta_description Meta description de l'article
         * @param {string} keyword          Mot-clé à rechercher
         * @return {Object} Résultats de l'analyse
         */
        analyzeKeywordPlacement: function(content, title, meta_description, keyword) {
            // Nettoyer le mot-clé
            const cleanKeyword = keyword.toLowerCase();
            
            // Vérifier si le mot-clé est dans le titre
            const inTitle = title.toLowerCase().indexOf(cleanKeyword) !== -1;
            
            // Vérifier si le mot-clé est dans la meta description
            const inMetaDescription = meta_description.toLowerCase().indexOf(cleanKeyword) !== -1;
            
            // Vérifier si le mot-clé est dans les titres (h1, h2, h3)
            let inHeadings = false;
            const headingRegex = /<h[1-3][^>]*>(.*?)<\/h[1-3]>/gi;
            let match;
            
            while ((match = headingRegex.exec(content)) !== null) {
                const headingText = this.stripTags(match[1]).toLowerCase();
                
                if (headingText.indexOf(cleanKeyword) !== -1) {
                    inHeadings = true;
                    break;
                }
            }
            
            // Vérifier si le mot-clé est dans le premier paragraphe
            let inFirstParagraph = false;
            const paragraphRegex = /<p[^>]*>(.*?)<\/p>/i;
            const paragraphMatch = paragraphRegex.exec(content);
            
            if (paragraphMatch) {
                const paragraphText = this.stripTags(paragraphMatch[1]).toLowerCase();
                inFirstParagraph = paragraphText.indexOf(cleanKeyword) !== -1;
            }
            
            // Déterminer le statut
            let status = 'poor';
            
            const placementCount = (inTitle ? 1 : 0) + (inMetaDescription ? 1 : 0) + (inHeadings ? 1 : 0) + (inFirstParagraph ? 1 : 0);
            
            if (placementCount >= 3) {
                status = 'good';
            } else if (placementCount >= 2) {
                status = 'average';
            }
            
            return {
                in_title: inTitle,
                in_meta_description: inMetaDescription,
                in_headings: inHeadings,
                in_first_paragraph: inFirstParagraph,
                status: status
            };
        },

        /**
         * Supprime les balises HTML d'une chaîne
         * @param {string} html Chaîne HTML
         * @return {string} Chaîne sans balises HTML
         */
        stripTags: function(html) {
            if (!html) {
                return '';
            }
            
            // Créer un élément temporaire
            const temp = document.createElement('div');
            temp.innerHTML = html;
            
            // Récupérer le texte
            return temp.textContent || temp.innerText || '';
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.KeywordAnalyzer = KeywordAnalyzer;

})(jQuery);
