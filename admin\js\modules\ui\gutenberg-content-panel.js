/**
 * Module de gestion du panneau de contenu pour Gutenberg
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire du panneau de contenu pour Gutenberg
     */
    const G<PERSON>nbergContentPanel = {
        /**
         * Initialise le gestionnaire du panneau de contenu
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Rend le panneau de contenu
         * @return {string} HTML du panneau
         */
        render: function() {
            const { __ } = wp.i18n;
            const { Button, TextControl, Spinner } = wp.components;
            const { Fragment } = wp.element;
            
            return (
                <Fragment>
                    <div className="smartseo-ai-panel-header">
                        <h3>{__('Génération de contenu', 'smartseo-ai')}</h3>
                        <p>{__('Générez du contenu optimisé pour le SEO en un clic.', 'smartseo-ai')}</p>
                    </div>
                    
                    <div className="smartseo-ai-panel-content">
                        <TextControl
                            label={__('Mot-clé principal', 'smartseo-ai')}
                            id="smartseo-ai-focus-keyword"
                            placeholder={__('Entrez votre mot-clé principal', 'smartseo-ai')}
                        />
                        
                        <div className="smartseo-ai-button-group">
                            <Button
                                isPrimary
                                className="smartseo-ai-generate-title"
                                data-type="title"
                            >
                                {__('Générer un titre', 'smartseo-ai')}
                            </Button>
                            
                            <Button
                                isPrimary
                                className="smartseo-ai-generate-meta"
                                data-type="meta_description"
                            >
                                {__('Générer une meta description', 'smartseo-ai')}
                            </Button>
                        </div>
                        
                        <div className="smartseo-ai-button-group">
                            <Button
                                isPrimary
                                className="smartseo-ai-generate-intro"
                                data-type="introduction"
                            >
                                {__('Générer une introduction', 'smartseo-ai')}
                            </Button>
                            
                            <Button
                                isPrimary
                                className="smartseo-ai-generate-conclusion"
                                data-type="conclusion"
                            >
                                {__('Générer une conclusion', 'smartseo-ai')}
                            </Button>
                        </div>
                        
                        <div className="smartseo-ai-button-group">
                            <Button
                                isPrimary
                                className="smartseo-ai-generate-headings"
                                data-type="h2_headings"
                            >
                                {__('Générer des titres H2', 'smartseo-ai')}
                            </Button>
                            
                            <Button
                                isPrimary
                                className="smartseo-ai-generate-paragraph"
                                data-type="paragraph"
                            >
                                {__('Générer un paragraphe', 'smartseo-ai')}
                            </Button>
                        </div>
                        
                        <div className="smartseo-ai-button-group">
                            <Button
                                isPrimary
                                className="smartseo-ai-generate-article"
                                data-type="full_article"
                            >
                                {__('Générer un article complet', 'smartseo-ai')}
                            </Button>
                        </div>
                    </div>
                    
                    <div className="smartseo-ai-results-container" id="smartseo-ai-content-results">
                        <div className="smartseo-ai-loading" style={{ display: 'none' }}>
                            <Spinner />
                            <p>{__('Génération en cours...', 'smartseo-ai')}</p>
                        </div>
                        
                        <div className="smartseo-ai-results" style={{ display: 'none' }}>
                            <h4>{__('Suggestions générées', 'smartseo-ai')}</h4>
                            <div className="smartseo-ai-results-content"></div>
                        </div>
                    </div>
                </Fragment>
            );
        },

        /**
         * Affiche les résultats de génération de contenu
         * @param {Object} results Résultats de génération
         * @param {string} type    Type de contenu
         */
        showResults: function(results, type) {
            const { __ } = wp.i18n;
            const $container = $('#smartseo-ai-content-results');
            const $loading = $container.find('.smartseo-ai-loading');
            const $results = $container.find('.smartseo-ai-results');
            const $content = $results.find('.smartseo-ai-results-content');
            
            // Masquer le chargement
            $loading.hide();
            
            // Vider le contenu précédent
            $content.empty();
            
            // Afficher les résultats
            $results.show();
            
            // Formater les résultats en fonction du type
            if (type === 'title' && results.titles) {
                const titles = results.titles;
                
                $content.append('<p>' + __('Voici quelques suggestions de titres :', 'smartseo-ai') + '</p>');
                
                const $list = $('<ul class="smartseo-ai-suggestions-list"></ul>');
                
                titles.forEach(title => {
                    const $item = $('<li class="smartseo-ai-suggestion-item"></li>');
                    $item.append('<div class="smartseo-ai-suggestion-content">' + title + '</div>');
                    $item.append('<div class="smartseo-ai-suggestion-actions">' +
                        '<button class="button smartseo-ai-apply-suggestion" data-content="' + title + '" data-target="title">' + __('Appliquer', 'smartseo-ai') + '</button>' +
                        '<button class="button smartseo-ai-copy-suggestion" data-content="' + title + '">' + __('Copier', 'smartseo-ai') + '</button>' +
                        '</div>');
                    $list.append($item);
                });
                
                $content.append($list);
            } else if (type === 'meta_description' && results.descriptions) {
                const descriptions = results.descriptions;
                
                $content.append('<p>' + __('Voici quelques suggestions de meta descriptions :', 'smartseo-ai') + '</p>');
                
                const $list = $('<ul class="smartseo-ai-suggestions-list"></ul>');
                
                descriptions.forEach(description => {
                    const $item = $('<li class="smartseo-ai-suggestion-item"></li>');
                    $item.append('<div class="smartseo-ai-suggestion-content">' + description + '</div>');
                    $item.append('<div class="smartseo-ai-suggestion-actions">' +
                        '<button class="button smartseo-ai-apply-suggestion" data-content="' + description + '" data-target="meta_description">' + __('Appliquer', 'smartseo-ai') + '</button>' +
                        '<button class="button smartseo-ai-copy-suggestion" data-content="' + description + '">' + __('Copier', 'smartseo-ai') + '</button>' +
                        '</div>');
                    $list.append($item);
                });
                
                $content.append($list);
            } else if (type === 'introduction' && results.introduction) {
                const introduction = results.introduction;
                
                $content.append('<p>' + __('Voici une suggestion d\'introduction :', 'smartseo-ai') + '</p>');
                
                const $item = $('<div class="smartseo-ai-suggestion-item"></div>');
                $item.append('<div class="smartseo-ai-suggestion-content">' + introduction + '</div>');
                $item.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + introduction + '" data-target="introduction">' + __('Appliquer', 'smartseo-ai') + '</button>' +
                    '<button class="button smartseo-ai-copy-suggestion" data-content="' + introduction + '">' + __('Copier', 'smartseo-ai') + '</button>' +
                    '</div>');
                
                $content.append($item);
            } else if (type === 'conclusion' && results.conclusion) {
                const conclusion = results.conclusion;
                
                $content.append('<p>' + __('Voici une suggestion de conclusion :', 'smartseo-ai') + '</p>');
                
                const $item = $('<div class="smartseo-ai-suggestion-item"></div>');
                $item.append('<div class="smartseo-ai-suggestion-content">' + conclusion + '</div>');
                $item.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + conclusion + '" data-target="conclusion">' + __('Appliquer', 'smartseo-ai') + '</button>' +
                    '<button class="button smartseo-ai-copy-suggestion" data-content="' + conclusion + '">' + __('Copier', 'smartseo-ai') + '</button>' +
                    '</div>');
                
                $content.append($item);
            } else if (type === 'h2_headings' && results.headings) {
                const headings = results.headings;
                
                $content.append('<p>' + __('Voici quelques suggestions de titres H2 :', 'smartseo-ai') + '</p>');
                
                const $list = $('<ul class="smartseo-ai-suggestions-list"></ul>');
                
                // Joindre les titres en une seule chaîne pour l'application en bloc
                const headingsText = headings.join('\n');
                
                // Ajouter un bouton pour appliquer tous les titres
                $content.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + headingsText + '" data-target="h2_headings">' + __('Appliquer tous les titres', 'smartseo-ai') + '</button>' +
                    '</div>');
                
                headings.forEach(heading => {
                    const $item = $('<li class="smartseo-ai-suggestion-item"></li>');
                    $item.append('<div class="smartseo-ai-suggestion-content">' + heading + '</div>');
                    $item.append('<div class="smartseo-ai-suggestion-actions">' +
                        '<button class="button smartseo-ai-apply-suggestion" data-content="' + heading + '" data-target="h2_headings">' + __('Appliquer', 'smartseo-ai') + '</button>' +
                        '<button class="button smartseo-ai-copy-suggestion" data-content="' + heading + '">' + __('Copier', 'smartseo-ai') + '</button>' +
                        '</div>');
                    $list.append($item);
                });
                
                $content.append($list);
            } else if (type === 'paragraph' && results.paragraph) {
                const paragraph = results.paragraph;
                
                $content.append('<p>' + __('Voici une suggestion de paragraphe :', 'smartseo-ai') + '</p>');
                
                const $item = $('<div class="smartseo-ai-suggestion-item"></div>');
                $item.append('<div class="smartseo-ai-suggestion-content">' + paragraph + '</div>');
                $item.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + paragraph + '" data-target="paragraph">' + __('Appliquer', 'smartseo-ai') + '</button>' +
                    '<button class="button smartseo-ai-copy-suggestion" data-content="' + paragraph + '">' + __('Copier', 'smartseo-ai') + '</button>' +
                    '</div>');
                
                $content.append($item);
            } else if (type === 'full_article' && results.article) {
                const article = results.article;
                
                $content.append('<p>' + __('Voici une suggestion d\'article complet :', 'smartseo-ai') + '</p>');
                
                const $item = $('<div class="smartseo-ai-suggestion-item"></div>');
                $item.append('<div class="smartseo-ai-suggestion-content">' + article + '</div>');
                $item.append('<div class="smartseo-ai-suggestion-actions">' +
                    '<button class="button smartseo-ai-apply-suggestion" data-content="' + article + '" data-target="full_article">' + __('Appliquer', 'smartseo-ai') + '</button>' +
                    '<button class="button smartseo-ai-copy-suggestion" data-content="' + article + '">' + __('Copier', 'smartseo-ai') + '</button>' +
                    '</div>');
                
                $content.append($item);
            } else {
                // Afficher un message d'erreur
                $content.append('<p>' + __('Aucun résultat disponible.', 'smartseo-ai') + '</p>');
            }
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.GutenbergContentPanel = GutenbergContentPanel;

})(jQuery);
