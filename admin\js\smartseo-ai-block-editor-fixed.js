/**
 * Script pour l'éditeur de blocs
 *
 * @package SmartSEO_AI
 */

(function() {
    'use strict';

    const { __ } = wp.i18n;
    const { registerPlugin } = wp.plugins;
    const { PluginDocumentSettingPanel } = wp.editPost;
    const { TextControl, TextareaControl } = wp.components;
    const { useSelect, useDispatch } = wp.data;
    const { useEffect } = wp.element;
    const { createElement } = wp.element;

    /**
     * Composant de métadonnées SEO
     */
    const SmartSEOAIMetaBox = function() {
        // Récupérer les métadonnées
        const metaTitle = useSelect(function(select) {
            return select('core/editor').getEditedPostAttribute('meta')?.smartseo_ai_meta_title || '';
        }, []);
        
        const metaDescription = useSelect(function(select) {
            return select('core/editor').getEditedPostAttribute('meta')?.smartseo_ai_meta_description || '';
        }, []);
        
        const focusKeyword = useSelect(function(select) {
            return select('core/editor').getEditedPostAttribute('meta')?.smartseo_ai_focus_keyword || '';
        }, []);
        
        // Récupérer le dispatcher
        const { editPost } = useDispatch('core/editor');
        
        // Mettre à jour les métadonnées
        const updateMetaTitle = function(value) {
            editPost({ meta: { smartseo_ai_meta_title: value } });
        };
        
        const updateMetaDescription = function(value) {
            editPost({ meta: { smartseo_ai_meta_description: value } });
        };
        
        const updateFocusKeyword = function(value) {
            editPost({ meta: { smartseo_ai_focus_keyword: value } });
        };
        
        // Analyser le contenu lorsque les métadonnées changent
        useEffect(function() {
            if (window.SmartSEOAI && window.SmartSEOAI.LiveAnalyzer && window.SmartSEOAI.LiveAnalyzer.analyzeContent) {
                const content = wp.data.select('core/editor').getEditedPostContent();
                const title = wp.data.select('core/editor').getEditedPostAttribute('title');
                const postId = wp.data.select('core/editor').getCurrentPostId();
                
                window.SmartSEOAI.LiveAnalyzer.analyzeContent(postId, content, title, focusKeyword);
            }
        }, [metaTitle, metaDescription, focusKeyword]);
        
        // Rendu du composant
        return createElement(
            'div',
            { className: 'smartseo-ai-metabox' },
            createElement(
                TextControl,
                {
                    label: __('Mot-clé principal', 'smartseo-ai'),
                    value: focusKeyword,
                    onChange: updateFocusKeyword,
                    className: 'smartseo-ai-focus-keyword'
                }
            ),
            createElement(
                TextControl,
                {
                    label: __('Titre SEO', 'smartseo-ai'),
                    value: metaTitle,
                    onChange: updateMetaTitle,
                    className: 'smartseo-ai-meta-title'
                }
            ),
            createElement(
                TextareaControl,
                {
                    label: __('Meta Description', 'smartseo-ai'),
                    value: metaDescription,
                    onChange: updateMetaDescription,
                    className: 'smartseo-ai-meta-description'
                }
            )
        );
    };

    // Enregistrer le plugin
    registerPlugin('smartseo-ai-metabox', {
        render: function() {
            return createElement(
                PluginDocumentSettingPanel,
                {
                    name: 'smartseo-ai-metabox',
                    title: __('SmartSEO AI', 'smartseo-ai'),
                    className: 'smartseo-ai-metabox-panel'
                },
                createElement(SmartSEOAIMetaBox)
            );
        },
        icon: 'search',
    });
})();
