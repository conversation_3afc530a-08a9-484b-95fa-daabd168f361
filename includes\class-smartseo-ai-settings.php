<?php
/**
 * Classe pour gérer les paramètres du plugin
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les paramètres du plugin
 */
class SmartSEO_AI_Settings {

    /**
     * Constructeur
     */
    public function __construct() {
        // Enregistrer les paramètres
        add_action( 'admin_init', array( $this, 'register_settings' ) );
    }

    /**
     * Enregistre les paramètres du plugin
     */
    public function register_settings() {
        // Enregistrer la section des paramètres généraux
        add_settings_section(
            'smartseo_ai_general_settings',
            __( 'Paramètres généraux', 'smartseo-ai' ),
            array( $this, 'render_general_settings_section' ),
            'smartseo-ai-settings'
        );

        // Enregistrer le champ du fournisseur d'IA
        add_settings_field(
            'smartseo_ai_provider',
            __( 'Fournisseur d\'IA', 'smartseo-ai' ),
            array( $this, 'render_provider_field' ),
            'smartseo-ai-settings',
            'smartseo_ai_general_settings'
        );

        // Enregistrer le champ de la clé API OpenAI
        add_settings_field(
            'smartseo_ai_openai_api_key',
            __( 'Clé API OpenAI', 'smartseo-ai' ),
            array( $this, 'render_openai_api_key_field' ),
            'smartseo-ai-settings',
            'smartseo_ai_general_settings'
        );

        // Enregistrer le champ du modèle OpenAI
        add_settings_field(
            'smartseo_ai_openai_model',
            __( 'Modèle OpenAI', 'smartseo-ai' ),
            array( $this, 'render_openai_model_field' ),
            'smartseo-ai-settings',
            'smartseo_ai_general_settings'
        );

        // Enregistrer le champ de la clé API Gemini
        add_settings_field(
            'smartseo_ai_gemini_api_key',
            __( 'Clé API Gemini (Google AI)', 'smartseo-ai' ),
            array( $this, 'render_gemini_api_key_field' ),
            'smartseo-ai-settings',
            'smartseo_ai_general_settings'
        );

        // Enregistrer le champ du modèle Gemini
        add_settings_field(
            'smartseo_ai_gemini_model',
            __( 'Modèle Gemini', 'smartseo-ai' ),
            array( $this, 'render_gemini_model_field' ),
            'smartseo-ai-settings',
            'smartseo_ai_general_settings'
        );

        // Enregistrer la section des fonctionnalités
        add_settings_section(
            'smartseo_ai_features_settings',
            __( 'Fonctionnalités', 'smartseo-ai' ),
            array( $this, 'render_features_settings_section' ),
            'smartseo-ai-settings'
        );

        // Enregistrer les champs des fonctionnalités
        $features = array(
            'enable_meta_description' => __( 'Activer la génération de meta description', 'smartseo-ai' ),
            'enable_keywords' => __( 'Activer la génération de mots-clés', 'smartseo-ai' ),
            'enable_seo_title' => __( 'Activer la génération de titre SEO', 'smartseo-ai' ),
            'enable_slug_optimization' => __( 'Activer l\'optimisation du slug', 'smartseo-ai' ),
            'enable_open_graph' => __( 'Activer la génération de données Open Graph', 'smartseo-ai' ),
            'enable_seo_advice' => __( 'Activer les conseils d\'optimisation SEO', 'smartseo-ai' ),
        );

        foreach ( $features as $feature_key => $feature_label ) {
            add_settings_field(
                'smartseo_ai_settings[' . $feature_key . ']',
                $feature_label,
                array( $this, 'render_feature_field' ),
                'smartseo-ai-settings',
                'smartseo_ai_features_settings',
                array(
                    'feature_key' => $feature_key,
                    'label_for' => 'smartseo_ai_settings_' . $feature_key,
                )
            );
        }

        // Enregistrer les options
        register_setting( 'smartseo-ai-settings', 'smartseo_ai_provider', array( $this, 'sanitize_provider' ) );
        register_setting( 'smartseo-ai-settings', 'smartseo_ai_openai_api_key', array( $this, 'sanitize_api_key' ) );
        register_setting( 'smartseo-ai-settings', 'smartseo_ai_openai_model', array( $this, 'sanitize_openai_model' ) );
        register_setting( 'smartseo-ai-settings', 'smartseo_ai_gemini_api_key', array( $this, 'sanitize_api_key' ) );
        register_setting( 'smartseo-ai-settings', 'smartseo_ai_gemini_model', array( $this, 'sanitize_gemini_model' ) );
        register_setting( 'smartseo-ai-settings', 'smartseo_ai_settings', array( $this, 'sanitize_settings' ) );
    }

    /**
     * Affiche la section des paramètres généraux
     */
    public function render_general_settings_section() {
        echo '<p>' . esc_html__( 'Configurez les paramètres généraux du plugin SmartSEO AI.', 'smartseo-ai' ) . '</p>';
    }

    /**
     * Affiche la section des fonctionnalités
     */
    public function render_features_settings_section() {
        echo '<p>' . esc_html__( 'Activez ou désactivez les fonctionnalités du plugin.', 'smartseo-ai' ) . '</p>';
    }

    /**
     * Affiche le champ du fournisseur d'IA
     */
    public function render_provider_field() {
        $provider = get_option( 'smartseo_ai_provider', 'openai' );
        $providers = array(
            'openai' => 'OpenAI (GPT)',
            'gemini' => 'Google Gemini AI',
        );

        echo '<select id="smartseo_ai_provider" name="smartseo_ai_provider">';
        foreach ( $providers as $provider_key => $provider_label ) {
            echo '<option value="' . esc_attr( $provider_key ) . '" ' . selected( $provider, $provider_key, false ) . '>' . esc_html( $provider_label ) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . esc_html__( 'Sélectionnez le fournisseur d\'IA à utiliser pour l\'optimisation SEO.', 'smartseo-ai' ) . '</p>';
        echo '<script>jQuery(document).ready(function($) {
            function toggleApiFields() {
                var provider = $("#smartseo_ai_provider").val();
                if (provider === "openai") {
                    $("#smartseo_ai_openai_api_key").closest("tr").show();
                    $("#smartseo_ai_openai_model").closest("tr").show();
                    $("#smartseo_ai_gemini_api_key").closest("tr").hide();
                    $("#smartseo_ai_gemini_model").closest("tr").hide();
                } else {
                    $("#smartseo_ai_openai_api_key").closest("tr").hide();
                    $("#smartseo_ai_openai_model").closest("tr").hide();
                    $("#smartseo_ai_gemini_api_key").closest("tr").show();
                    $("#smartseo_ai_gemini_model").closest("tr").show();
                }
            }

            toggleApiFields();
            $("#smartseo_ai_provider").on("change", toggleApiFields);
        });</script>';
    }

    /**
     * Affiche le champ de la clé API OpenAI
     */
    public function render_openai_api_key_field() {
        $api_key = get_option( 'smartseo_ai_openai_api_key', '' );

        echo '<input type="password" id="smartseo_ai_openai_api_key" name="smartseo_ai_openai_api_key" value="' . esc_attr( $api_key ) . '" class="regular-text" />';
        echo '<p class="description">' . esc_html__( 'Entrez votre clé API OpenAI. Vous pouvez l\'obtenir sur le site d\'OpenAI.', 'smartseo-ai' ) . '</p>';
    }

    /**
     * Affiche le champ du modèle OpenAI
     */
    public function render_openai_model_field() {
        $model = get_option( 'smartseo_ai_openai_model', 'gpt-4' );
        $models = array(
            'gpt-4' => 'GPT-4',
            'gpt-4-turbo' => 'GPT-4 Turbo',
            'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
        );

        echo '<select id="smartseo_ai_openai_model" name="smartseo_ai_openai_model">';
        foreach ( $models as $model_key => $model_label ) {
            echo '<option value="' . esc_attr( $model_key ) . '" ' . selected( $model, $model_key, false ) . '>' . esc_html( $model_label ) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . esc_html__( 'Sélectionnez le modèle OpenAI à utiliser pour l\'optimisation SEO.', 'smartseo-ai' ) . '</p>';
    }

    /**
     * Affiche le champ de la clé API Gemini
     */
    public function render_gemini_api_key_field() {
        $api_key = get_option( 'smartseo_ai_gemini_api_key', '' );

        echo '<input type="password" id="smartseo_ai_gemini_api_key" name="smartseo_ai_gemini_api_key" value="' . esc_attr( $api_key ) . '" class="regular-text" />';
        echo '<p class="description">' . esc_html__( 'Entrez votre clé API Google Gemini. Vous pouvez l\'obtenir sur Google AI Studio.', 'smartseo-ai' ) . '</p>';
    }

    /**
     * Affiche le champ du modèle Gemini
     */
    public function render_gemini_model_field() {
        $model = get_option( 'smartseo_ai_gemini_model', 'gemini-1.5-pro' );
        $models = array(
            'gemini-1.5-pro' => 'Gemini 1.5 Pro',
            'gemini-1.5-flash' => 'Gemini 1.5 Flash',
            'gemini-1.5-flash-8b' => 'Gemini 1.5 Flash 8B',
            'gemini-2.0-flash' => 'Gemini 2.0 Flash',
            'gemini-2.0-flash-lite' => 'Gemini 2.0 Flash Lite',
        );

        echo '<select id="smartseo_ai_gemini_model" name="smartseo_ai_gemini_model">';
        foreach ( $models as $model_key => $model_label ) {
            echo '<option value="' . esc_attr( $model_key ) . '" ' . selected( $model, $model_key, false ) . '>' . esc_html( $model_label ) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . esc_html__( 'Sélectionnez le modèle Gemini à utiliser pour l\'optimisation SEO.', 'smartseo-ai' ) . '</p>';
    }

    /**
     * Affiche un champ de fonctionnalité
     *
     * @param array $args Arguments du champ.
     */
    public function render_feature_field( $args ) {
        $feature_key = $args['feature_key'];
        $settings = get_option( 'smartseo_ai_settings', array() );
        $value = isset( $settings[$feature_key] ) ? $settings[$feature_key] : 'yes';

        echo '<label for="smartseo_ai_settings_' . esc_attr( $feature_key ) . '">';
        echo '<input type="checkbox" id="smartseo_ai_settings_' . esc_attr( $feature_key ) . '" name="smartseo_ai_settings[' . esc_attr( $feature_key ) . ']" value="yes" ' . checked( $value, 'yes', false ) . ' />';
        echo '</label>';
    }

    /**
     * Sanitize la clé API
     *
     * @param string $input Valeur saisie.
     * @return string Valeur sanitizée.
     */
    public function sanitize_api_key( $input ) {
        return sanitize_text_field( $input );
    }

    /**
     * Sanitize le fournisseur d'IA
     *
     * @param string $input Valeur saisie.
     * @return string Valeur sanitizée.
     */
    public function sanitize_provider( $input ) {
        $valid_providers = array( 'openai', 'gemini' );

        if ( in_array( $input, $valid_providers, true ) ) {
            return $input;
        }

        return 'openai';
    }

    /**
     * Sanitize le modèle OpenAI
     *
     * @param string $input Valeur saisie.
     * @return string Valeur sanitizée.
     */
    public function sanitize_openai_model( $input ) {
        $valid_models = array( 'gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo' );

        if ( in_array( $input, $valid_models, true ) ) {
            return $input;
        }

        return 'gpt-4';
    }

    /**
     * Sanitize le modèle Gemini
     *
     * @param string $input Valeur saisie.
     * @return string Valeur sanitizée.
     */
    public function sanitize_gemini_model( $input ) {
        $valid_models = array(
            'gemini-1.5-pro',
            'gemini-1.5-flash',
            'gemini-1.5-flash-8b',
            'gemini-2.0-flash',
            'gemini-2.0-flash-lite'
        );

        if ( in_array( $input, $valid_models, true ) ) {
            return $input;
        }

        return 'gemini-1.5-pro';
    }

    /**
     * Sanitize les paramètres
     *
     * @param array $input Valeurs saisies.
     * @return array Valeurs sanitizées.
     */
    public function sanitize_settings( $input ) {
        $sanitized_input = array();
        $features = array(
            'enable_meta_description',
            'enable_keywords',
            'enable_seo_title',
            'enable_slug_optimization',
            'enable_open_graph',
            'enable_seo_advice',
        );

        foreach ( $features as $feature ) {
            $sanitized_input[$feature] = isset( $input[$feature] ) ? 'yes' : 'no';
        }

        return $sanitized_input;
    }
}
