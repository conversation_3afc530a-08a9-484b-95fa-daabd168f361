/**
 * Script de correction des erreurs SVG
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Correcteur de SVG
     */
    const SVGFixer = {
        /**
         * Initialise le correcteur
         */
        init: function() {
            console.log('SVG Fixer: Initialisation');
            
            // Corriger les SVG au chargement de la page
            this.fixSVGs();
            
            // Corriger les SVG après un délai (pour les SVG chargés dynamiquement)
            setTimeout(() => {
                this.fixSVGs();
            }, 1000);
            
            console.log('SVG Fixer: Initialisation terminée');
        },
        
        /**
         * Corrige les SVG
         */
        fixSVGs: function() {
            console.log('SVG Fixer: Correction des SVG');
            
            // Trouver tous les SVG
            const $svgs = $('svg');
            
            console.log('SVG Fixer: ' + $svgs.length + ' SVG trouvés');
            
            // Parcourir les SVG
            $svgs.each(function() {
                const $svg = $(this);
                
                // Trouver tous les chemins
                const $paths = $svg.find('path');
                
                // Parcourir les chemins
                $paths.each(function() {
                    const $path = $(this);
                    
                    // Récupérer l'attribut d
                    const d = $path.attr('d');
                    
                    if (d) {
                        // Vérifier si l'attribut d contient des caractères invalides
                        if (d.includes('...') || d.includes('tc') || d.includes('undefined')) {
                            console.log('SVG Fixer: Chemin invalide trouvé', d);
                            
                            // Supprimer le chemin
                            $path.remove();
                            
                            console.log('SVG Fixer: Chemin supprimé');
                        }
                    }
                });
            });
            
            console.log('SVG Fixer: Correction des SVG terminée');
        }
    };
    
    // Initialiser le correcteur au chargement du document
    $(document).ready(function() {
        SVGFixer.init();
    });
    
})(jQuery);
