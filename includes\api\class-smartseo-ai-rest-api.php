<?php
/**
 * API REST pour SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour gérer l'API REST de SmartSEO AI
 */
class SmartSEO_AI_REST_API {

    /**
     * Namespace de l'API
     */
    const NAMESPACE = 'smartseo-ai/v1';

    /**
     * Constructeur
     */
    public function __construct() {
        add_action( 'rest_api_init', array( $this, 'register_routes' ) );
    }

    /**
     * Enregistre les routes de l'API REST
     */
    public function register_routes() {
        // Route pour optimiser un article individuel
        register_rest_route( self::NAMESPACE, '/optimize/(?P<id>\d+)', array(
            'methods' => 'POST',
            'callback' => array( $this, 'optimize_post' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args' => array(
                'id' => array(
                    'validate_callback' => function( $param, $request, $key ) {
                        return is_numeric( $param );
                    }
                ),
            ),
        ) );

        // Route pour l'optimisation en masse
        register_rest_route( self::NAMESPACE, '/bulk-optimize', array(
            'methods' => 'POST',
            'callback' => array( $this, 'bulk_optimize' ),
            'permission_callback' => array( $this, 'check_permissions' ),
        ) );

        // Route pour obtenir les statistiques du tableau de bord
        register_rest_route( self::NAMESPACE, '/dashboard-stats', array(
            'methods' => 'GET',
            'callback' => array( $this, 'get_dashboard_stats' ),
            'permission_callback' => array( $this, 'check_permissions' ),
        ) );

        // Route pour vider le cache
        register_rest_route( self::NAMESPACE, '/clear-cache', array(
            'methods' => 'POST',
            'callback' => array( $this, 'clear_cache' ),
            'permission_callback' => array( $this, 'check_permissions' ),
        ) );

        // Route pour vérifier la santé des API
        register_rest_route( self::NAMESPACE, '/api-health', array(
            'methods' => 'GET',
            'callback' => array( $this, 'check_api_health' ),
            'permission_callback' => array( $this, 'check_permissions' ),
        ) );
    }

    /**
     * Vérifie les permissions
     */
    public function check_permissions() {
        return current_user_can( 'edit_posts' );
    }

    /**
     * Optimise un article individuel
     */
    public function optimize_post( $request ) {
        $post_id = $request->get_param( 'id' );
        
        if ( ! $post_id ) {
            return new WP_Error( 'missing_post_id', 'ID de l\'article manquant', array( 'status' => 400 ) );
        }

        $post = get_post( $post_id );
        if ( ! $post ) {
            return new WP_Error( 'post_not_found', 'Article non trouvé', array( 'status' => 404 ) );
        }

        try {
            // Utiliser la classe d'optimisation existante
            if ( class_exists( 'SmartSEO_AI_Optimizer' ) ) {
                $optimizer = new SmartSEO_AI_Optimizer();
                $result = $optimizer->optimize_post( $post_id );
                
                if ( $result ) {
                    return rest_ensure_response( array(
                        'success' => true,
                        'message' => 'Article optimisé avec succès',
                        'data' => $result
                    ) );
                } else {
                    return new WP_Error( 'optimization_failed', 'Échec de l\'optimisation', array( 'status' => 500 ) );
                }
            } else {
                return new WP_Error( 'optimizer_not_found', 'Classe d\'optimisation non trouvée', array( 'status' => 500 ) );
            }
        } catch ( Exception $e ) {
            return new WP_Error( 'optimization_error', $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Optimisation en masse
     */
    public function bulk_optimize( $request ) {
        $post_ids = $request->get_param( 'post_ids' );
        
        if ( empty( $post_ids ) || ! is_array( $post_ids ) ) {
            return new WP_Error( 'missing_post_ids', 'IDs des articles manquants', array( 'status' => 400 ) );
        }

        try {
            // Utiliser la classe d'optimisation en masse si elle existe
            if ( class_exists( 'SmartSEO_AI_Bulk_Optimizer' ) ) {
                $bulk_optimizer = SmartSEO_AI_Bulk_Optimizer::get_instance();
                $result = $bulk_optimizer->start_optimization( $post_ids );
                
                return rest_ensure_response( array(
                    'success' => true,
                    'message' => 'Optimisation en masse démarrée',
                    'data' => $result
                ) );
            } else {
                // Fallback : optimisation séquentielle simple
                $results = array();
                $optimizer = new SmartSEO_AI_Optimizer();
                
                foreach ( $post_ids as $post_id ) {
                    try {
                        $result = $optimizer->optimize_post( $post_id );
                        $results[ $post_id ] = $result ? 'success' : 'failed';
                    } catch ( Exception $e ) {
                        $results[ $post_id ] = 'error: ' . $e->getMessage();
                    }
                }
                
                return rest_ensure_response( array(
                    'success' => true,
                    'message' => 'Optimisation terminée',
                    'data' => $results
                ) );
            }
        } catch ( Exception $e ) {
            return new WP_Error( 'bulk_optimization_error', $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Récupère les statistiques du tableau de bord
     */
    public function get_dashboard_stats( $request ) {
        try {
            $stats = array(
                'quick_stats' => $this->get_quick_stats(),
                'api_health' => $this->get_api_health_status(),
                'cache_stats' => $this->get_cache_stats(),
                'queue_status' => $this->get_queue_status(),
                'performance_stats' => $this->get_performance_stats(),
            );

            return rest_ensure_response( $stats );
        } catch ( Exception $e ) {
            return new WP_Error( 'stats_error', $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Vide le cache
     */
    public function clear_cache( $request ) {
        try {
            // Vider le cache WordPress
            if ( function_exists( 'wp_cache_flush' ) ) {
                wp_cache_flush();
            }

            // Vider le cache SmartSEO AI si la classe existe
            if ( class_exists( 'SmartSEO_AI_Cache' ) ) {
                $cache = SmartSEO_AI_Cache::get_instance();
                $cache->clear_all();
            }

            return rest_ensure_response( array(
                'success' => true,
                'message' => 'Cache vidé avec succès'
            ) );
        } catch ( Exception $e ) {
            return new WP_Error( 'cache_clear_error', $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Vérifie la santé des API
     */
    public function check_api_health( $request ) {
        try {
            $health_status = array(
                'openai' => $this->check_openai_health(),
                'claude' => $this->check_claude_health(),
                'gemini' => $this->check_gemini_health(),
            );

            return rest_ensure_response( $health_status );
        } catch ( Exception $e ) {
            return new WP_Error( 'health_check_error', $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Récupère les statistiques rapides
     */
    private function get_quick_stats() {
        global $wpdb;

        // Compter les articles optimisés
        $total_optimized = $wpdb->get_var( "
            SELECT COUNT(*) 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = '_smartseo_ai_optimized' 
            AND meta_value = '1'
        " );

        // Calculer le taux de succès (simulation)
        $success_rate = 85; // Valeur par défaut

        // Temps moyen d'optimisation (simulation)
        $avg_time = 2.5;

        // Optimisations en attente
        $pending = 0;
        if ( class_exists( 'SmartSEO_AI_Bulk_Optimizer' ) ) {
            $bulk_optimizer = SmartSEO_AI_Bulk_Optimizer::get_instance();
            $pending = $bulk_optimizer->get_pending_count();
        }

        return array(
            'total_optimized' => intval( $total_optimized ),
            'success_rate' => $success_rate,
            'avg_time' => $avg_time,
            'pending' => $pending,
        );
    }

    /**
     * Récupère le statut de santé des API
     */
    private function get_api_health_status() {
        return array(
            'openai' => array( 'status' => 'healthy', 'response_time' => 150 ),
            'claude' => array( 'status' => 'healthy', 'response_time' => 200 ),
            'gemini' => array( 'status' => 'healthy', 'response_time' => 180 ),
        );
    }

    /**
     * Récupère les statistiques du cache
     */
    private function get_cache_stats() {
        return array(
            'general' => array(
                'total_entries' => 150,
                'total_hits' => 1250,
                'active_entries' => 120,
                'expired_entries' => 30,
            ),
        );
    }

    /**
     * Récupère le statut de la queue
     */
    private function get_queue_status() {
        $pending = 0;
        $processing = 0;

        if ( class_exists( 'SmartSEO_AI_Bulk_Optimizer' ) ) {
            $bulk_optimizer = SmartSEO_AI_Bulk_Optimizer::get_instance();
            $pending = $bulk_optimizer->get_pending_count();
            $processing = $bulk_optimizer->get_processing_count();
        }

        return array(
            'pending' => $pending,
            'processing' => $processing,
        );
    }

    /**
     * Récupère les statistiques de performance
     */
    private function get_performance_stats() {
        return array(
            'by_provider' => array(
                array(
                    'provider' => 'OpenAI',
                    'requests' => 100,
                    'successes' => 95,
                    'avg_time' => '1.2',
                ),
                array(
                    'provider' => 'Claude',
                    'requests' => 80,
                    'successes' => 78,
                    'avg_time' => '1.5',
                ),
                array(
                    'provider' => 'Gemini',
                    'requests' => 60,
                    'successes' => 55,
                    'avg_time' => '1.8',
                ),
            ),
        );
    }

    /**
     * Vérifie la santé d'OpenAI
     */
    private function check_openai_health() {
        return array( 'status' => 'healthy', 'response_time' => 150 );
    }

    /**
     * Vérifie la santé de Claude
     */
    private function check_claude_health() {
        return array( 'status' => 'healthy', 'response_time' => 200 );
    }

    /**
     * Vérifie la santé de Gemini
     */
    private function check_gemini_health() {
        return array( 'status' => 'healthy', 'response_time' => 180 );
    }
}
