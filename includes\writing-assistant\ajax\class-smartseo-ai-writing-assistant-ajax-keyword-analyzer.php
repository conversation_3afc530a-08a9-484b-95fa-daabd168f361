<?php
/**
 * Classe de gestion des AJAX d'analyse de mots-clés pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les AJAX d'analyse de mots-clés de l'Assistant de Rédaction SEO
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Keyword_Analyzer {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Analyse les mots-clés
     */
    public function analyze_keywords() {
        // Vérifier le nonce
        if ( ! check_ajax_referer( 'smartseo_ai_writing_assistant_nonce', 'nonce', false ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page.', 'smartseo-ai' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les données
        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $content = isset( $_POST['content'] ) ? wp_kses_post( wp_unslash( $_POST['content'] ) ) : '';
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';

        // Vérifier si le mot-clé est fourni
        if ( empty( $keyword ) ) {
            wp_send_json_error( array( 'message' => __( 'Veuillez fournir un mot-clé principal.', 'smartseo-ai' ) ) );
        }

        // Analyser les mots-clés
        $results = $this->analyze( $keyword, $content );

        // Vérifier si l'analyse a réussi
        if ( is_wp_error( $results ) ) {
            wp_send_json_error( array( 'message' => $results->get_error_message() ) );
        }

        // Envoyer la réponse
        wp_send_json_success( $results );
    }

    /**
     * Recherche des sujets connexes
     */
    public function search_related_topics() {
        // Vérifier le nonce
        if ( ! check_ajax_referer( 'smartseo_ai_writing_assistant_nonce', 'nonce', false ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page.', 'smartseo-ai' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les données
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';

        // Vérifier si le mot-clé est fourni
        if ( empty( $keyword ) ) {
            wp_send_json_error( array( 'message' => __( 'Veuillez fournir un mot-clé.', 'smartseo-ai' ) ) );
        }

        // Rechercher des sujets connexes
        $results = $this->search_topics( $keyword );

        // Vérifier si la recherche a réussi
        if ( is_wp_error( $results ) ) {
            wp_send_json_error( array( 'message' => $results->get_error_message() ) );
        }

        // Envoyer la réponse
        wp_send_json_success( $results );
    }

    /**
     * Analyse les mots-clés
     *
     * @param string $keyword Mot-clé principal.
     * @param string $content Contenu à analyser.
     * @return array|WP_Error Résultats de l'analyse ou erreur.
     */
    private function analyze( $keyword, $content ) {
        // Construire le prompt
        $prompt = $this->build_analysis_prompt( $keyword, $content );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        return $this->process_analysis_response( $response, $keyword );
    }

    /**
     * Recherche des sujets connexes
     *
     * @param string $keyword Mot-clé principal.
     * @return array|WP_Error Résultats de la recherche ou erreur.
     */
    private function search_topics( $keyword ) {
        // Construire le prompt
        $prompt = $this->build_topics_prompt( $keyword );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        return $this->process_topics_response( $response );
    }

    /**
     * Construit le prompt pour l'analyse de mots-clés
     *
     * @param string $keyword Mot-clé principal.
     * @param string $content Contenu à analyser.
     * @return string Prompt pour l'IA.
     */
    private function build_analysis_prompt( $keyword, $content ) {
        $prompt = "Analyse le mot-clé principal \"{$keyword}\" et suggère des mots-clés secondaires et longue traîne pertinents pour le référencement SEO.";
        
        if ( ! empty( $content ) ) {
            // Extraire un résumé du contenu
            $summary = wp_strip_all_tags( $content );
            if ( strlen( $summary ) > 500 ) {
                $summary = substr( $summary, 0, 500 ) . '...';
            }
            
            $prompt .= " Voici un résumé du contenu existant : \"{$summary}\".";
        }
        
        $prompt .= " Pour chaque mot-clé, fournis les informations suivantes :
1. Le mot-clé
2. Le volume de recherche estimé (élevé, moyen, faible)
3. La difficulté de classement (élevée, moyenne, faible)
4. L'intention de recherche (informationnelle, transactionnelle, navigationnelle)

Réponds au format JSON avec la structure suivante :
{
  \"primary_keyword\": {
    \"keyword\": \"mot-clé principal\",
    \"volume\": \"volume de recherche\",
    \"difficulty\": \"difficulté\",
    \"intent\": \"intention\"
  },
  \"secondary_keywords\": [
    {
      \"keyword\": \"mot-clé secondaire 1\",
      \"volume\": \"volume de recherche\",
      \"difficulty\": \"difficulté\",
      \"intent\": \"intention\"
    },
    ...
  ],
  \"long_tail_keywords\": [
    {
      \"keyword\": \"mot-clé longue traîne 1\",
      \"volume\": \"volume de recherche\",
      \"difficulty\": \"difficulté\",
      \"intent\": \"intention\"
    },
    ...
  ]
}";

        return $prompt;
    }

    /**
     * Construit le prompt pour la recherche de sujets connexes
     *
     * @param string $keyword Mot-clé principal.
     * @return string Prompt pour l'IA.
     */
    private function build_topics_prompt( $keyword ) {
        $prompt = "Suggère 10 sujets connexes au mot-clé \"{$keyword}\" qui pourraient intéresser les lecteurs et être pertinents pour le référencement SEO.";
        
        $prompt .= " Pour chaque sujet, fournis les informations suivantes :
1. Le nom du sujet
2. Le volume de recherche estimé (élevé, moyen, faible) si possible

Réponds au format JSON avec la structure suivante :
{
  \"topics\": [
    {
      \"name\": \"nom du sujet 1\",
      \"volume\": \"volume de recherche\"
    },
    ...
  ]
}";

        return $prompt;
    }

    /**
     * Traite la réponse de l'analyse de mots-clés
     *
     * @param string $response Réponse de l'IA.
     * @param string $keyword  Mot-clé principal.
     * @return array Résultats de l'analyse.
     */
    private function process_analysis_response( $response, $keyword ) {
        // Extraire le JSON de la réponse
        $json_start = strpos( $response, '{' );
        $json_end = strrpos( $response, '}' );
        
        if ( $json_start === false || $json_end === false ) {
            // Créer une réponse par défaut
            return $this->create_default_analysis_response( $keyword );
        }
        
        $json = substr( $response, $json_start, $json_end - $json_start + 1 );
        
        // Décoder le JSON
        $data = json_decode( $json, true );
        
        if ( json_last_error() !== JSON_ERROR_NONE ) {
            // Créer une réponse par défaut
            return $this->create_default_analysis_response( $keyword );
        }
        
        // Vérifier si les données sont valides
        if ( ! isset( $data['primary_keyword'] ) && ! isset( $data['secondary_keywords'] ) && ! isset( $data['long_tail_keywords'] ) ) {
            // Créer une réponse par défaut
            return $this->create_default_analysis_response( $keyword );
        }
        
        return $data;
    }

    /**
     * Traite la réponse de la recherche de sujets connexes
     *
     * @param string $response Réponse de l'IA.
     * @return array Résultats de la recherche.
     */
    private function process_topics_response( $response ) {
        // Extraire le JSON de la réponse
        $json_start = strpos( $response, '{' );
        $json_end = strrpos( $response, '}' );
        
        if ( $json_start === false || $json_end === false ) {
            // Créer une réponse par défaut
            return array(
                'topics' => array(),
            );
        }
        
        $json = substr( $response, $json_start, $json_end - $json_start + 1 );
        
        // Décoder le JSON
        $data = json_decode( $json, true );
        
        if ( json_last_error() !== JSON_ERROR_NONE ) {
            // Créer une réponse par défaut
            return array(
                'topics' => array(),
            );
        }
        
        // Vérifier si les données sont valides
        if ( ! isset( $data['topics'] ) ) {
            // Créer une réponse par défaut
            return array(
                'topics' => array(),
            );
        }
        
        return $data;
    }

    /**
     * Crée une réponse par défaut pour l'analyse de mots-clés
     *
     * @param string $keyword Mot-clé principal.
     * @return array Réponse par défaut.
     */
    private function create_default_analysis_response( $keyword ) {
        return array(
            'primary_keyword' => array(
                'keyword' => $keyword,
                'volume' => __( 'moyen', 'smartseo-ai' ),
                'difficulty' => __( 'moyen', 'smartseo-ai' ),
                'intent' => __( 'informationnelle', 'smartseo-ai' ),
            ),
            'secondary_keywords' => array(
                array(
                    'keyword' => $keyword . ' ' . __( 'guide', 'smartseo-ai' ),
                    'volume' => __( 'moyen', 'smartseo-ai' ),
                    'difficulty' => __( 'moyen', 'smartseo-ai' ),
                    'intent' => __( 'informationnelle', 'smartseo-ai' ),
                ),
                array(
                    'keyword' => __( 'meilleur', 'smartseo-ai' ) . ' ' . $keyword,
                    'volume' => __( 'moyen', 'smartseo-ai' ),
                    'difficulty' => __( 'moyen', 'smartseo-ai' ),
                    'intent' => __( 'transactionnelle', 'smartseo-ai' ),
                ),
            ),
            'long_tail_keywords' => array(
                array(
                    'keyword' => __( 'comment', 'smartseo-ai' ) . ' ' . $keyword . ' ' . __( 'facilement', 'smartseo-ai' ),
                    'volume' => __( 'faible', 'smartseo-ai' ),
                    'difficulty' => __( 'faible', 'smartseo-ai' ),
                    'intent' => __( 'informationnelle', 'smartseo-ai' ),
                ),
                array(
                    'keyword' => $keyword . ' ' . __( 'pour débutants', 'smartseo-ai' ),
                    'volume' => __( 'faible', 'smartseo-ai' ),
                    'difficulty' => __( 'faible', 'smartseo-ai' ),
                    'intent' => __( 'informationnelle', 'smartseo-ai' ),
                ),
            ),
        );
    }
}
