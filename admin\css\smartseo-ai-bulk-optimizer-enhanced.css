/**
 * Styles pour l'optimisation en masse améliorée SmartSEO AI
 */

/* Variables CSS */
:root {
    --smartseo-primary: #0073aa;
    --smartseo-success: #46b450;
    --smartseo-warning: #ffb900;
    --smartseo-error: #dc3232;
    --smartseo-info: #00a0d2;
    --smartseo-gray-light: #f8f9fa;
    --smartseo-gray-medium: #e9ecef;
    --smartseo-gray-dark: #6c757d;
    --smartseo-border-radius: 8px;
    --smartseo-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --smartseo-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
    --smartseo-transition: all 0.3s ease;
}

/* Conteneur principal */
.smartseo-ai-bulk-optimizer-enhanced {
    max-width: 1400px;
    margin: 0 auto;
}

.smartseo-ai-bulk-optimizer-enhanced h1 {
    margin-bottom: 30px;
    color: #1d2327;
    font-size: 28px;
    font-weight: 600;
}

/* Statistiques globales */
.smartseo-stats-overview {
    background: #fff;
    border-radius: var(--smartseo-border-radius);
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: var(--smartseo-shadow);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: var(--smartseo-border-radius);
    border-left: 4px solid;
    transition: var(--smartseo-transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--smartseo-shadow-hover);
}

.stat-card.total {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-left-color: #667eea;
}

.stat-card.optimized {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border-left-color: var(--smartseo-success);
}

.stat-card.pending {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
    border-left-color: var(--smartseo-warning);
}

.stat-card.progress {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
    border-left-color: var(--smartseo-info);
}

.stat-icon {
    font-size: 32px;
    margin-right: 15px;
    opacity: 0.9;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    opacity: 0.9;
}

.stat-value {
    font-size: 28px;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    opacity: 0.8;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-fill {
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Statistiques par type */
.stats-by-type {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid var(--smartseo-gray-medium);
}

.stats-by-type h3 {
    margin: 0 0 20px 0;
    color: #1d2327;
    font-size: 18px;
    font-weight: 600;
}

.type-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.type-stat-item {
    background: var(--smartseo-gray-light);
    padding: 15px;
    border-radius: var(--smartseo-border-radius);
    border: 1px solid var(--smartseo-gray-medium);
}

.type-name {
    font-weight: 600;
    margin-bottom: 8px;
    color: #1d2327;
}

.type-numbers {
    font-size: 14px;
    margin-bottom: 8px;
    color: var(--smartseo-gray-dark);
}

.type-numbers .optimized {
    color: var(--smartseo-success);
    font-weight: 600;
}

.type-progress {
    display: flex;
    align-items: center;
    gap: 10px;
}

.type-progress-bar {
    flex: 1;
    height: 4px;
    background: var(--smartseo-gray-medium);
    border-radius: 2px;
    overflow: hidden;
}

.type-progress-fill {
    height: 100%;
    background: var(--smartseo-success);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.type-percentage {
    font-size: 12px;
    font-weight: 600;
    color: var(--smartseo-gray-dark);
    min-width: 35px;
}

/* Panel de filtres */
.smartseo-filters-panel {
    background: #fff;
    border-radius: var(--smartseo-border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--smartseo-shadow);
}

.smartseo-filters-panel h3 {
    margin: 0 0 20px 0;
    color: #1d2327;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filters-form {
    margin: 0;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #1d2327;
    font-size: 14px;
}

.filter-group select,
.filter-group input[type="text"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: var(--smartseo-transition);
}

.filter-group select:focus,
.filter-group input[type="text"]:focus {
    border-color: var(--smartseo-primary);
    box-shadow: 0 0 0 1px var(--smartseo-primary);
    outline: none;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    cursor: pointer;
    font-weight: normal !important;
}

.checkbox-item input[type="checkbox"] {
    margin: 0;
}

.checkbox-item .count {
    color: var(--smartseo-gray-dark);
    font-size: 12px;
    margin-left: auto;
}

.sort-controls {
    display: flex;
    gap: 10px;
}

.sort-controls select {
    flex: 1;
}

.filters-actions {
    display: flex;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid var(--smartseo-gray-medium);
}

.filters-actions .button {
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Panel d'actions en masse */
.smartseo-bulk-actions-panel {
    background: #fff;
    border-radius: var(--smartseo-border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--smartseo-shadow);
}

.bulk-actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.bulk-actions-header h3 {
    margin: 0;
    color: #1d2327;
    font-size: 18px;
    font-weight: 600;
}

.selection-info {
    color: var(--smartseo-gray-dark);
    font-size: 14px;
}

.selection-info #selected-count {
    font-weight: 600;
    color: var(--smartseo-primary);
}

.bulk-actions-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.bulk-actions-controls .button {
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Barre de progression d'optimisation */
.optimization-progress {
    margin-top: 25px;
    padding: 20px;
    background: var(--smartseo-gray-light);
    border-radius: var(--smartseo-border-radius);
    border: 1px solid var(--smartseo-gray-medium);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-header h4 {
    margin: 0;
    color: #1d2327;
    font-size: 16px;
}

.progress-stats {
    font-size: 14px;
    color: var(--smartseo-gray-dark);
    font-weight: 600;
}

.progress-bar-container {
    position: relative;
    margin-bottom: 15px;
}

.optimization-progress .progress-bar {
    width: 100%;
    height: 20px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid var(--smartseo-gray-medium);
}

.optimization-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--smartseo-primary), var(--smartseo-success));
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.optimization-progress .progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 600;
    font-size: 12px;
    color: #333;
    text-shadow: 0 1px 2px rgba(255,255,255,0.8);
}

.progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.progress-status {
    color: var(--smartseo-gray-dark);
    font-style: italic;
}

.progress-eta {
    color: var(--smartseo-gray-dark);
    font-weight: 600;
}

/* Panel de table des contenus */
.smartseo-content-table-panel {
    background: #fff;
    border-radius: var(--smartseo-border-radius);
    box-shadow: var(--smartseo-shadow);
    overflow: hidden;
}

.table-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--smartseo-gray-medium);
    background: var(--smartseo-gray-light);
}

.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.table-info {
    color: var(--smartseo-gray-dark);
    font-size: 14px;
    font-weight: 500;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.table-actions label {
    font-size: 14px;
    color: #1d2327;
    font-weight: 500;
}

.table-actions select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.table-actions .button {
    font-size: 13px;
    padding: 4px 8px;
    height: auto;
}

/* Table des contenus */
.table-container {
    overflow-x: auto;
}

.smartseo-content-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.smartseo-content-table th,
.smartseo-content-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--smartseo-gray-medium);
}

.smartseo-content-table th {
    background: var(--smartseo-gray-light);
    font-weight: 600;
    color: #1d2327;
    position: sticky;
    top: 0;
    z-index: 10;
}

.smartseo-content-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: var(--smartseo-transition);
}

.smartseo-content-table th.sortable:hover {
    background: var(--smartseo-gray-medium);
}

.sort-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
}

.sort-indicator.asc {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 6px solid #666;
}

.sort-indicator.desc {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #666;
}

/* Colonnes spécifiques */
.column-select {
    width: 40px;
    text-align: center;
}

.column-id {
    width: 60px;
    text-align: center;
}

.column-type {
    width: 100px;
}

.column-date {
    width: 140px;
}

.column-author {
    width: 120px;
}

.column-words {
    width: 80px;
    text-align: center;
}

.column-score {
    width: 120px;
    text-align: center;
}

.column-status {
    width: 130px;
    text-align: center;
}

.column-actions {
    width: 120px;
    text-align: center;
}

/* Lignes de contenu */
.content-row {
    transition: var(--smartseo-transition);
}

.content-row:hover {
    background: rgba(0, 115, 170, 0.05);
}

.content-row.optimized {
    background: rgba(70, 180, 80, 0.03);
}

.content-row.in-progress {
    background: rgba(255, 185, 0, 0.05);
    animation: pulse 2s infinite;
}

.content-row.error {
    background: rgba(220, 50, 50, 0.03);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Contenu des cellules */
.title-wrapper {
    max-width: 300px;
}

.post-title {
    display: block;
    font-weight: 600;
    color: var(--smartseo-primary);
    text-decoration: none;
    margin-bottom: 4px;
    line-height: 1.3;
}

.post-title:hover {
    color: #005a87;
}

.row-actions {
    font-size: 12px;
    color: var(--smartseo-gray-dark);
}

.row-actions a {
    color: var(--smartseo-gray-dark);
    text-decoration: none;
}

.row-actions a:hover {
    color: var(--smartseo-primary);
}

.post-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.post-type-post {
    background: #e3f2fd;
    color: #1976d2;
}

.post-type-page {
    background: #f3e5f5;
    color: #7b1fa2;
}

.post-type-product {
    background: #e8f5e9;
    color: #388e3c;
}

.date-info .published {
    font-weight: 500;
}

.date-info .modified {
    margin-top: 2px;
}

.date-info .modified small {
    color: var(--smartseo-gray-dark);
    font-style: italic;
}

.word-count {
    font-weight: 600;
    color: var(--smartseo-gray-dark);
}

/* Badges de score */
.score-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 10px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
    min-width: 70px;
    justify-content: center;
}

.score-badge.score-excellent {
    background: linear-gradient(135deg, #4caf50, #8bc34a);
    color: white;
}

.score-badge.score-good {
    background: linear-gradient(135deg, #2196f3, #03a9f4);
    color: white;
}

.score-badge.score-average {
    background: linear-gradient(135deg, #ff9800, #ffc107);
    color: white;
}

.score-badge.score-poor {
    background: linear-gradient(135deg, #f44336, #e91e63);
    color: white;
}

.score-badge.score-none {
    background: var(--smartseo-gray-medium);
    color: var(--smartseo-gray-dark);
}

.score-value {
    font-size: 14px;
    font-weight: bold;
}

.score-max {
    font-size: 10px;
    opacity: 0.8;
}

/* Badges de statut */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 10px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-optimized {
    background: #e8f5e9;
    color: #2e7d32;
}

.status-badge.status-not-optimized {
    background: var(--smartseo-gray-medium);
    color: var(--smartseo-gray-dark);
}

.status-badge.status-in-progress {
    background: #fff3e0;
    color: #f57c00;
}

.status-badge.status-error {
    background: #ffebee;
    color: #c62828;
}

/* Boutons d'action */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.optimize-single {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    padding: 4px 8px;
    height: auto;
}

.optimize-single.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.optimize-single.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    margin-left: -6px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s infinite linear;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Message aucun résultat */
.no-results {
    text-align: center;
}

.no-results-message {
    padding: 40px 20px;
    color: var(--smartseo-gray-dark);
}

.no-results-message .dashicons {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-results-message h3 {
    margin: 0 0 10px 0;
    color: #1d2327;
    font-size: 18px;
}

.no-results-message p {
    margin: 0;
    font-size: 14px;
}

/* Pagination */
.table-pagination {
    padding: 20px 25px;
    border-top: 1px solid var(--smartseo-gray-medium);
    background: var(--smartseo-gray-light);
}

.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-info {
    color: var(--smartseo-gray-dark);
    font-size: 14px;
    font-weight: 500;
}

.pagination-links {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.pagination-links a,
.pagination-links span {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: var(--smartseo-primary);
    background: #fff;
    font-size: 14px;
    transition: var(--smartseo-transition);
}

.pagination-links a:hover {
    background: var(--smartseo-gray-light);
    border-color: #999;
}

.pagination-links .current {
    background: var(--smartseo-primary);
    border-color: var(--smartseo-primary);
    color: #fff;
    font-weight: 600;
}

/* Responsive */
@media (max-width: 1200px) {
    .filters-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .smartseo-ai-bulk-optimizer-enhanced {
        padding: 0 10px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .table-actions {
        justify-content: space-between;
    }

    .bulk-actions-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .bulk-actions-controls {
        flex-direction: column;
    }

    .pagination-wrapper {
        flex-direction: column;
        text-align: center;
    }

    /* Table responsive */
    .smartseo-content-table {
        font-size: 12px;
    }

    .smartseo-content-table th,
    .smartseo-content-table td {
        padding: 8px 6px;
    }

    .column-author,
    .column-words {
        display: none;
    }

    .title-wrapper {
        max-width: 200px;
    }
}
