<?php
/**
 * Classe pour gérer l'optimisation en masse des articles
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'optimisation en masse des articles
 */
class SmartSEO_AI_Bulk_Optimizer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Ajouter la page d'administration
        add_action( 'admin_menu', array( $this, 'add_bulk_optimizer_page' ) );

        // Enregistrer les scripts et styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_assets' ) );
    }

    /**
     * Ajoute la page d'optimisation en masse dans le menu d'administration
     */
    public function add_bulk_optimizer_page() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Optimisation en masse', 'smartseo-ai' ),
            __( 'Optimisation en masse', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-bulk-optimizer',
            array( $this, 'render_bulk_optimizer_page' )
        );
    }

    /**
     * Enregistre les scripts et styles pour la page d'optimisation en masse
     *
     * @param string $hook Hook de la page actuelle.
     */
    public function enqueue_assets( $hook ) {
        // Vérifier si nous sommes sur la page d'optimisation en masse
        if ( 'smartseo-ai_page_smartseo-ai-bulk-optimizer' !== $hook ) {
            return;
        }

        // Enregistrer le script
        wp_enqueue_script(
            'smartseo-ai-bulk-optimizer',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-bulk-optimizer.js',
            array( 'jquery', 'wp-api' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Localiser le script avec les données nécessaires
        wp_localize_script(
            'smartseo-ai-bulk-optimizer',
            'smartseoAiBulkOptimizer',
            array(
                'restUrl' => rest_url( 'smartseo-ai/v1' ),
                'nonce' => wp_create_nonce( 'wp_rest' ),
                'i18n' => array(
                    'optimizing' => __( 'Optimisation en cours...', 'smartseo-ai' ),
                    'success' => __( 'Optimisation réussie !', 'smartseo-ai' ),
                    'error' => __( 'Erreur lors de l\'optimisation.', 'smartseo-ai' ),
                    'complete' => __( 'Optimisation terminée !', 'smartseo-ai' ),
                    'confirmOptimize' => __( 'Êtes-vous sûr de vouloir optimiser tous les articles ? Cette opération peut prendre du temps.', 'smartseo-ai' ),
                    'confirmStop' => __( 'Êtes-vous sûr de vouloir arrêter l\'optimisation en cours ?', 'smartseo-ai' ),
                ),
                'batchSize' => 1, // Nombre d'articles à optimiser en parallèle
                'delayBetweenBatches' => 5000, // Délai entre les lots (en millisecondes)
            )
        );
    }

    /**
     * Affiche la page d'optimisation en masse
     */
    public function render_bulk_optimizer_page() {
        // Récupérer le numéro de page actuel
        $current_page = isset( $_GET['paged'] ) ? absint( $_GET['paged'] ) : 1;

        // Récupérer le nombre d'articles par page
        $per_page = isset( $_GET['per_page'] ) ? absint( $_GET['per_page'] ) : 20;
        if ( $per_page < 10 || $per_page > 100 ) {
            $per_page = 20; // Valeur par défaut si hors limites
        }

        // Récupérer les articles pour la page actuelle
        $result = $this->get_posts_for_optimization( $current_page, $per_page );
        $posts = $result['posts'];
        $total_posts = $result['total_posts'];
        $total_pages = $result['total_pages'];

        // Récupérer les statistiques globales
        $stats = $this->get_optimization_stats();

        // Inclure le template
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/bulk-optimizer.php';
    }

    /**
     * Récupère les articles pour l'optimisation avec pagination
     *
     * @param int $page     Numéro de page actuel.
     * @param int $per_page Nombre d'articles par page.
     * @return array Tableau contenant les articles, le nombre total d'articles et le nombre total de pages.
     */
    private function get_posts_for_optimization( $page = 1, $per_page = 20 ) {
        $args = array(
            'post_type'      => array( 'post', 'page' ),
            'post_status'    => 'publish',
            'posts_per_page' => $per_page,
            'paged'          => $page,
            'orderby'        => 'date',
            'order'          => 'DESC',
        );

        $query = new WP_Query( $args );
        $posts = array();

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();

                $post_id = get_the_ID();
                $score = get_post_meta( $post_id, 'smartseo_ai_seo_score', true );

                $posts[] = array(
                    'id'         => $post_id,
                    'title'      => get_the_title(),
                    'permalink'  => get_permalink(),
                    'edit_link'  => get_edit_post_link(),
                    'post_type'  => get_post_type_object( get_post_type() )->labels->singular_name,
                    'date'       => get_the_date(),
                    'seo_score'  => ! empty( $score ) ? intval( $score ) : 0,
                    'has_score'  => ! empty( $score ),
                    'status'     => ! empty( $score ) ? 'optimized' : 'not-optimized',
                );
            }

            wp_reset_postdata();
        }

        return array(
            'posts'       => $posts,
            'total_posts' => $query->found_posts,
            'total_pages' => $query->max_num_pages,
        );
    }

    /**
     * Récupère les statistiques globales d'optimisation
     *
     * @return array Statistiques d'optimisation.
     */
    private function get_optimization_stats() {
        global $wpdb;

        // Compter le nombre total d'articles publiés
        $total_posts_query = $wpdb->prepare(
            "SELECT COUNT(ID) FROM {$wpdb->posts} WHERE post_type IN ('post', 'page') AND post_status = 'publish'"
        );
        $total_posts = $wpdb->get_var( $total_posts_query );

        // Compter le nombre d'articles optimisés (avec un score SEO)
        $optimized_posts_query = $wpdb->prepare(
            "SELECT COUNT(DISTINCT post_id) FROM {$wpdb->postmeta}
            WHERE meta_key = 'smartseo_ai_seo_score'
            AND post_id IN (SELECT ID FROM {$wpdb->posts} WHERE post_type IN ('post', 'page') AND post_status = 'publish')"
        );
        $optimized_posts = $wpdb->get_var( $optimized_posts_query );

        // Calculer le nombre d'articles non optimisés
        $not_optimized_posts = $total_posts - $optimized_posts;

        // Calculer le taux d'optimisation
        $optimization_rate = $total_posts > 0 ? round( ( $optimized_posts / $total_posts ) * 100 ) : 0;

        return array(
            'total_posts'        => $total_posts,
            'optimized_posts'    => $optimized_posts,
            'not_optimized_posts' => $not_optimized_posts,
            'optimization_rate'  => $optimization_rate,
        );
    }

    /**
     * Retourne la classe CSS en fonction du score SEO
     *
     * @param int $score Score SEO.
     * @return string Classe CSS.
     */
    public function get_score_class( $score ) {
        if ( $score >= 80 ) {
            return 'good';
        } elseif ( $score >= 50 ) {
            return 'average';
        } else {
            return 'poor';
        }
    }
}
