<?php
/**
 * Classe pour gérer l'optimisation en masse des articles
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'optimisation en masse des articles
 */
class SmartSEO_AI_Bulk_Optimizer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Ajouter la page d'administration
        add_action( 'admin_menu', array( $this, 'add_bulk_optimizer_page' ) );

        // Enregistrer les scripts et styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_assets' ) );
    }

    /**
     * Ajoute la page d'optimisation en masse dans le menu d'administration
     */
    public function add_bulk_optimizer_page() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Optimisation en masse', 'smartseo-ai' ),
            __( 'Optimisation en masse', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-bulk-optimizer',
            array( $this, 'render_bulk_optimizer_page' )
        );

        // Ajouter la page de paramètres de l'optimisation en masse
        add_submenu_page(
            'smartseo-ai',
            __( 'Paramètres d\'optimisation', 'smartseo-ai' ),
            __( 'Paramètres d\'optimisation', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-bulk-settings',
            array( $this, 'render_bulk_settings_page' )
        );
    }

    /**
     * Enregistre les scripts et styles pour la page d'optimisation en masse
     *
     * @param string $hook Hook de la page actuelle.
     */
    public function enqueue_assets( $hook ) {
        // Vérifier si nous sommes sur la page d'optimisation en masse ou de paramètres
        if ( ! in_array( $hook, array( 'smartseo-ai_page_smartseo-ai-bulk-optimizer', 'smartseo-ai_page_smartseo-ai-bulk-settings' ), true ) ) {
            return;
        }

        // Si on est sur la page de paramètres, charger seulement les styles de base
        if ( 'smartseo-ai_page_smartseo-ai-bulk-settings' === $hook ) {
            return; // Les styles sont inclus dans la vue
        }

        // Vérifier si on utilise l'interface améliorée
        $use_enhanced = get_option( 'smartseo_ai_enhanced_bulk_optimizer', true );

        if ( $use_enhanced ) {
            // Scripts et styles pour l'interface améliorée
            wp_enqueue_script(
                'smartseo-ai-bulk-optimizer-enhanced',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-bulk-optimizer-enhanced.js',
                array( 'jquery', 'wp-api' ),
                SMARTSEO_AI_VERSION,
                true
            );

            wp_enqueue_style(
                'smartseo-ai-bulk-optimizer-enhanced',
                SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-bulk-optimizer-enhanced.css',
                array(),
                SMARTSEO_AI_VERSION
            );

            // Localiser le script amélioré
            wp_localize_script(
                'smartseo-ai-bulk-optimizer-enhanced',
                'smartseoAiBulkOptimizer',
                array(
                    'restUrl' => rest_url( 'smartseo-ai/v1' ),
                    'nonce' => wp_create_nonce( 'wp_rest' ),
                    'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                    'i18n' => array(
                        'optimizing' => __( 'Optimisation en cours...', 'smartseo-ai' ),
                        'success' => __( 'Optimisation réussie !', 'smartseo-ai' ),
                        'error' => __( 'Erreur lors de l\'optimisation.', 'smartseo-ai' ),
                        'complete' => __( 'Optimisation terminée !', 'smartseo-ai' ),
                        'confirmOptimize' => __( 'Êtes-vous sûr de vouloir optimiser tous les articles ? Cette opération peut prendre du temps.', 'smartseo-ai' ),
                        'confirmStop' => __( 'Êtes-vous sûr de vouloir arrêter l\'optimisation en cours ?', 'smartseo-ai' ),
                        'selectAtLeastOne' => __( 'Veuillez sélectionner au moins un article.', 'smartseo-ai' ),
                        'optimizationStarted' => __( 'Optimisation démarrée', 'smartseo-ai' ),
                        'optimizationStopped' => __( 'Optimisation arrêtée', 'smartseo-ai' ),
                    ),
                    'settings' => array(
                        'batchSize' => get_option( 'smartseo_ai_bulk_batch_size', 3 ),
                        'delayBetweenBatches' => get_option( 'smartseo_ai_bulk_delay', 3000 ),
                        'maxConcurrent' => get_option( 'smartseo_ai_bulk_max_concurrent', 2 ),
                    ),
                )
            );
        } else {
            // Scripts et styles pour l'interface classique
            wp_enqueue_script(
                'smartseo-ai-bulk-optimizer',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-bulk-optimizer.js',
                array( 'jquery', 'wp-api' ),
                SMARTSEO_AI_VERSION,
                true
            );

            // Localiser le script classique
            wp_localize_script(
                'smartseo-ai-bulk-optimizer',
                'smartseoAiBulkOptimizer',
                array(
                    'restUrl' => rest_url( 'smartseo-ai/v1' ),
                    'nonce' => wp_create_nonce( 'wp_rest' ),
                    'i18n' => array(
                        'optimizing' => __( 'Optimisation en cours...', 'smartseo-ai' ),
                        'success' => __( 'Optimisation réussie !', 'smartseo-ai' ),
                        'error' => __( 'Erreur lors de l\'optimisation.', 'smartseo-ai' ),
                        'complete' => __( 'Optimisation terminée !', 'smartseo-ai' ),
                        'confirmOptimize' => __( 'Êtes-vous sûr de vouloir optimiser tous les articles ? Cette opération peut prendre du temps.', 'smartseo-ai' ),
                        'confirmStop' => __( 'Êtes-vous sûr de vouloir arrêter l\'optimisation en cours ?', 'smartseo-ai' ),
                    ),
                    'batchSize' => 1,
                    'delayBetweenBatches' => 5000,
                )
            );
        }
    }

    /**
     * Affiche la page d'optimisation en masse
     */
    public function render_bulk_optimizer_page() {
        // Vérifier si on doit utiliser l'interface améliorée
        $use_enhanced = get_option( 'smartseo_ai_enhanced_bulk_optimizer', true );

        // Récupérer le numéro de page actuel
        $current_page = isset( $_GET['paged'] ) ? absint( $_GET['paged'] ) : 1;

        // Récupérer le nombre d'articles par page
        $per_page = isset( $_GET['per_page'] ) ? absint( $_GET['per_page'] ) : 20;
        if ( $per_page < 10 || $per_page > 100 ) {
            $per_page = 20; // Valeur par défaut si hors limites
        }

        // Récupérer les articles pour la page actuelle avec filtres
        $result = $this->get_posts_for_optimization( $current_page, $per_page );
        $posts = $result['posts'];
        $total_posts = $result['total_posts'];
        $total_pages = $result['total_pages'];

        // Récupérer les statistiques globales
        $stats = $this->get_optimization_stats();

        // Inclure le template approprié
        if ( $use_enhanced ) {
            include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/bulk-optimizer-enhanced.php';
        } else {
            include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/bulk-optimizer.php';
        }
    }

    /**
     * Récupère les articles pour l'optimisation avec pagination et filtres
     *
     * @param int   $page     Numéro de page actuel.
     * @param int   $per_page Nombre d'articles par page.
     * @param array $filters  Filtres à appliquer.
     * @return array Tableau contenant les articles, le nombre total d'articles et le nombre total de pages.
     */
    private function get_posts_for_optimization( $page = 1, $per_page = 20, $filters = array() ) {
        // Récupérer les filtres depuis les paramètres GET
        $post_types = isset( $_GET['post_types'] ) ? (array) $_GET['post_types'] : array( 'post', 'page' );
        $optimization_status = isset( $_GET['optimization_status'] ) ? sanitize_text_field( $_GET['optimization_status'] ) : 'all';
        $score_range = isset( $_GET['score_range'] ) ? sanitize_text_field( $_GET['score_range'] ) : 'all';
        $date_range = isset( $_GET['date_range'] ) ? sanitize_text_field( $_GET['date_range'] ) : 'all';
        $search_term = isset( $_GET['search'] ) ? sanitize_text_field( $_GET['search'] ) : '';
        $orderby = isset( $_GET['orderby'] ) ? sanitize_text_field( $_GET['orderby'] ) : 'date';
        $order = isset( $_GET['order'] ) ? sanitize_text_field( $_GET['order'] ) : 'DESC';

        // Valider les types de posts
        $available_post_types = $this->get_available_post_types();
        $post_types = array_intersect( $post_types, array_keys( $available_post_types ) );
        if ( empty( $post_types ) ) {
            $post_types = array( 'post', 'page' );
        }

        $args = array(
            'post_type'      => $post_types,
            'post_status'    => 'publish',
            'posts_per_page' => $per_page,
            'paged'          => $page,
            'orderby'        => $orderby,
            'order'          => $order,
        );

        // Filtre par terme de recherche
        if ( ! empty( $search_term ) ) {
            $args['s'] = $search_term;
        }

        // Filtre par date
        if ( 'all' !== $date_range ) {
            $date_query = $this->get_date_query( $date_range );
            if ( $date_query ) {
                $args['date_query'] = $date_query;
            }
        }

        // Filtre par statut d'optimisation
        if ( 'optimized' === $optimization_status ) {
            $args['meta_query'] = array(
                array(
                    'key'     => 'smartseo_ai_seo_score',
                    'compare' => 'EXISTS',
                ),
            );
        } elseif ( 'not_optimized' === $optimization_status ) {
            $args['meta_query'] = array(
                array(
                    'key'     => 'smartseo_ai_seo_score',
                    'compare' => 'NOT EXISTS',
                ),
            );
        }

        $query = new WP_Query( $args );
        $posts = array();

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();

                $post_id = get_the_ID();
                $score = get_post_meta( $post_id, 'smartseo_ai_seo_score', true );
                $post_type_obj = get_post_type_object( get_post_type() );

                // Filtre par score si nécessaire
                if ( 'all' !== $score_range && ! empty( $score ) ) {
                    $score_int = intval( $score );
                    $include_post = false;

                    switch ( $score_range ) {
                        case 'excellent':
                            $include_post = $score_int >= 80;
                            break;
                        case 'good':
                            $include_post = $score_int >= 60 && $score_int < 80;
                            break;
                        case 'average':
                            $include_post = $score_int >= 40 && $score_int < 60;
                            break;
                        case 'poor':
                            $include_post = $score_int < 40;
                            break;
                    }

                    if ( ! $include_post ) {
                        continue;
                    }
                }

                $posts[] = array(
                    'id'         => $post_id,
                    'title'      => get_the_title(),
                    'permalink'  => get_permalink(),
                    'edit_link'  => get_edit_post_link(),
                    'post_type'  => $post_type_obj ? $post_type_obj->labels->singular_name : get_post_type(),
                    'post_type_key' => get_post_type(),
                    'date'       => get_the_date(),
                    'modified'   => get_the_modified_date(),
                    'author'     => get_the_author(),
                    'word_count' => str_word_count( wp_strip_all_tags( get_the_content() ) ),
                    'seo_score'  => ! empty( $score ) ? intval( $score ) : 0,
                    'has_score'  => ! empty( $score ),
                    'status'     => ! empty( $score ) ? 'optimized' : 'not-optimized',
                );
            }

            wp_reset_postdata();
        }

        return array(
            'posts'       => $posts,
            'total_posts' => $query->found_posts,
            'total_pages' => $query->max_num_pages,
            'filters'     => array(
                'post_types' => $post_types,
                'optimization_status' => $optimization_status,
                'score_range' => $score_range,
                'date_range' => $date_range,
                'search_term' => $search_term,
                'orderby' => $orderby,
                'order' => $order,
            ),
        );
    }

    /**
     * Récupère les statistiques globales d'optimisation
     *
     * @return array Statistiques d'optimisation.
     */
    private function get_optimization_stats() {
        global $wpdb;

        // Récupérer les types de posts disponibles
        $available_post_types = $this->get_available_post_types();
        $post_types_in = "'" . implode( "','", array_keys( $available_post_types ) ) . "'";

        // Compter le nombre total d'articles publiés
        $total_posts = $wpdb->get_var(
            "SELECT COUNT(ID) FROM {$wpdb->posts} WHERE post_type IN ($post_types_in) AND post_status = 'publish'"
        );

        // Compter le nombre d'articles optimisés (avec un score SEO)
        $optimized_posts = $wpdb->get_var(
            "SELECT COUNT(DISTINCT post_id) FROM {$wpdb->postmeta}
            WHERE meta_key = 'smartseo_ai_seo_score'
            AND post_id IN (SELECT ID FROM {$wpdb->posts} WHERE post_type IN ($post_types_in) AND post_status = 'publish')"
        );

        // Calculer le nombre d'articles non optimisés
        $not_optimized_posts = $total_posts - $optimized_posts;

        // Calculer le taux d'optimisation
        $optimization_rate = $total_posts > 0 ? round( ( $optimized_posts / $total_posts ) * 100 ) : 0;

        // Statistiques par type de post
        $stats_by_type = array();
        foreach ( $available_post_types as $post_type => $label ) {
            $type_total = $wpdb->get_var( $wpdb->prepare(
                "SELECT COUNT(ID) FROM {$wpdb->posts} WHERE post_type = %s AND post_status = 'publish'",
                $post_type
            ) );

            $type_optimized = $wpdb->get_var( $wpdb->prepare(
                "SELECT COUNT(DISTINCT post_id) FROM {$wpdb->postmeta}
                WHERE meta_key = 'smartseo_ai_seo_score'
                AND post_id IN (SELECT ID FROM {$wpdb->posts} WHERE post_type = %s AND post_status = 'publish')",
                $post_type
            ) );

            $stats_by_type[ $post_type ] = array(
                'label' => $label,
                'total' => $type_total,
                'optimized' => $type_optimized,
                'not_optimized' => $type_total - $type_optimized,
                'rate' => $type_total > 0 ? round( ( $type_optimized / $type_total ) * 100 ) : 0,
            );
        }

        // Statistiques par score
        $score_stats = $wpdb->get_results(
            "SELECT
                CASE
                    WHEN CAST(meta_value AS UNSIGNED) >= 80 THEN 'excellent'
                    WHEN CAST(meta_value AS UNSIGNED) >= 60 THEN 'good'
                    WHEN CAST(meta_value AS UNSIGNED) >= 40 THEN 'average'
                    ELSE 'poor'
                END as score_range,
                COUNT(*) as count
            FROM {$wpdb->postmeta}
            WHERE meta_key = 'smartseo_ai_seo_score'
            AND post_id IN (SELECT ID FROM {$wpdb->posts} WHERE post_type IN ($post_types_in) AND post_status = 'publish')
            GROUP BY score_range",
            ARRAY_A
        );

        $score_distribution = array(
            'excellent' => 0,
            'good' => 0,
            'average' => 0,
            'poor' => 0,
        );

        foreach ( $score_stats as $stat ) {
            $score_distribution[ $stat['score_range'] ] = intval( $stat['count'] );
        }

        return array(
            'total_posts'        => $total_posts,
            'optimized_posts'    => $optimized_posts,
            'not_optimized_posts' => $not_optimized_posts,
            'optimization_rate'  => $optimization_rate,
            'by_post_type'       => $stats_by_type,
            'score_distribution' => $score_distribution,
        );
    }

    /**
     * Retourne la classe CSS en fonction du score SEO
     *
     * @param int $score Score SEO.
     * @return string Classe CSS.
     */
    public function get_score_class( $score ) {
        if ( $score >= 80 ) {
            return 'excellent';
        } elseif ( $score >= 60 ) {
            return 'good';
        } elseif ( $score >= 40 ) {
            return 'average';
        } else {
            return 'poor';
        }
    }

    /**
     * Récupère les types de posts disponibles pour l'optimisation
     *
     * @return array Types de posts disponibles.
     */
    private function get_available_post_types() {
        $post_types = get_post_types( array( 'public' => true ), 'objects' );
        $available_types = array();

        foreach ( $post_types as $post_type ) {
            // Exclure les types de posts non pertinents
            if ( in_array( $post_type->name, array( 'attachment', 'revision', 'nav_menu_item' ), true ) ) {
                continue;
            }

            $available_types[ $post_type->name ] = $post_type->labels->name;
        }

        return $available_types;
    }

    /**
     * Génère une requête de date basée sur la plage sélectionnée
     *
     * @param string $date_range Plage de date.
     * @return array|null Requête de date pour WP_Query.
     */
    private function get_date_query( $date_range ) {
        $date_query = null;

        switch ( $date_range ) {
            case 'last_week':
                $date_query = array(
                    array(
                        'after' => '1 week ago',
                    ),
                );
                break;

            case 'last_month':
                $date_query = array(
                    array(
                        'after' => '1 month ago',
                    ),
                );
                break;

            case 'last_3_months':
                $date_query = array(
                    array(
                        'after' => '3 months ago',
                    ),
                );
                break;

            case 'last_6_months':
                $date_query = array(
                    array(
                        'after' => '6 months ago',
                    ),
                );
                break;

            case 'last_year':
                $date_query = array(
                    array(
                        'after' => '1 year ago',
                    ),
                );
                break;

            case 'older_than_year':
                $date_query = array(
                    array(
                        'before' => '1 year ago',
                    ),
                );
                break;
        }

        return $date_query;
    }

    /**
     * Génère l'URL avec les filtres appliqués
     *
     * @param array $new_params Nouveaux paramètres à ajouter.
     * @return string URL avec filtres.
     */
    public function get_filtered_url( $new_params = array() ) {
        $current_params = array(
            'page' => 'smartseo-ai-bulk-optimizer',
            'post_types' => isset( $_GET['post_types'] ) ? (array) $_GET['post_types'] : array(),
            'optimization_status' => isset( $_GET['optimization_status'] ) ? sanitize_text_field( $_GET['optimization_status'] ) : 'all',
            'score_range' => isset( $_GET['score_range'] ) ? sanitize_text_field( $_GET['score_range'] ) : 'all',
            'date_range' => isset( $_GET['date_range'] ) ? sanitize_text_field( $_GET['date_range'] ) : 'all',
            'search' => isset( $_GET['search'] ) ? sanitize_text_field( $_GET['search'] ) : '',
            'orderby' => isset( $_GET['orderby'] ) ? sanitize_text_field( $_GET['orderby'] ) : 'date',
            'order' => isset( $_GET['order'] ) ? sanitize_text_field( $_GET['order'] ) : 'DESC',
            'per_page' => isset( $_GET['per_page'] ) ? absint( $_GET['per_page'] ) : 20,
        );

        // Fusionner avec les nouveaux paramètres
        $params = array_merge( $current_params, $new_params );

        // Supprimer les paramètres vides
        $params = array_filter( $params, function( $value ) {
            return ! empty( $value ) || $value === '0';
        } );

        return add_query_arg( $params, admin_url( 'admin.php' ) );
    }

    /**
     * Récupère les options de tri disponibles
     *
     * @return array Options de tri.
     */
    public function get_sort_options() {
        return array(
            'date' => __( 'Date de publication', 'smartseo-ai' ),
            'modified' => __( 'Date de modification', 'smartseo-ai' ),
            'title' => __( 'Titre', 'smartseo-ai' ),
            'menu_order' => __( 'Ordre', 'smartseo-ai' ),
            'comment_count' => __( 'Nombre de commentaires', 'smartseo-ai' ),
        );
    }

    /**
     * Affiche la page de paramètres de l'optimisation en masse
     */
    public function render_bulk_settings_page() {
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/bulk-optimizer-settings.php';
    }
}
