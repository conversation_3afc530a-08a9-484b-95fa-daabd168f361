/**
 * Module de gestion de la barre latérale pour Gutenberg
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    // Sous-modules
    const ContentPanel = window.SmartSEOAI.GutenbergContentPanel;
    const KeywordPanel = window.SmartSEOAI.GutenbergKeywordPanel;
    const AnalysisPanel = window.SmartSEOAI.GutenbergAnalysisPanel;
    const TrendsPanel = window.SmartSEOAI.GutenbergTrendsPanel;

    /**
     * Gestionnaire de la barre latérale pour Gutenberg
     */
    const GutenbergSidebar = {
        /**
         * Initialise le gestionnaire de la barre latérale pour Gutenberg
         */
        init: function() {
            console.log('GutenbergSidebar: Initialisation');

            try {
                // Vérifier si les sous-modules sont disponibles
                if (ContentPanel && ContentPanel.init) ContentPanel.init();
                if (KeywordPanel && KeywordPanel.init) KeywordPanel.init();
                if (AnalysisPanel && AnalysisPanel.init) AnalysisPanel.init();
                if (TrendsPanel && TrendsPanel.init) TrendsPanel.init();

                // Ajouter les écouteurs d'événements
                this.bindEvents();

                // Ajouter la barre latérale
                this.addSidebar();

                console.log('GutenbergSidebar: Initialisation terminée avec succès');
            } catch (error) {
                console.error('GutenbergSidebar: Erreur lors de l\'initialisation', error);
            }
        },

        /**
         * Ajoute les écouteurs d'événements
         */
        bindEvents: function() {
            console.log('GutenbergSidebar: Ajout des écouteurs d\'événements');

            try {
                // Écouter les événements de l'éditeur Gutenberg
                if (typeof wp !== 'undefined' && wp.data && wp.data.subscribe) {
                    wp.data.subscribe(function() {
                        try {
                            const isEditorSidebarOpened = wp.data.select('core/edit-post').isEditorSidebarOpened();
                            const activeGeneralSidebar = wp.data.select('core/edit-post').getActiveGeneralSidebarName();

                            // Vérifier si notre barre latérale est active
                            if (isEditorSidebarOpened && activeGeneralSidebar === 'smartseo-ai/writing-assistant') {
                                // Mettre à jour l'analyse en temps réel
                                GutenbergSidebar.updateLiveAnalysis();
                            }
                        } catch (error) {
                            console.error('GutenbergSidebar: Erreur dans l\'écouteur d\'événements', error);
                        }
                    });

                    console.log('GutenbergSidebar: Écouteurs d\'événements ajoutés avec succès');
                } else {
                    console.error('GutenbergSidebar: L\'API wp.data n\'est pas disponible');
                }
            } catch (error) {
                console.error('GutenbergSidebar: Erreur lors de l\'ajout des écouteurs d\'événements', error);
            }
        },

        /**
         * Ajoute la barre latérale à l'éditeur Gutenberg
         */
        addSidebar: function() {
            console.log('GutenbergSidebar: Ajout de la barre latérale');

            try {
                // Vérifier si l'API Gutenberg est disponible
                if (typeof wp === 'undefined' || !wp.plugins || !wp.editPost) {
                    console.error('GutenbergSidebar: L\'API Gutenberg n\'est pas disponible');
                    return;
                }

                const registerPlugin = wp.plugins.registerPlugin;
                const PluginSidebar = wp.editPost.PluginSidebar;
                const PluginSidebarMoreMenuItem = wp.editPost.PluginSidebarMoreMenuItem;
                const PanelBody = wp.components.PanelBody;
                const TabPanel = wp.components.TabPanel;
                const Fragment = wp.element.Fragment;
                const createElement = wp.element.createElement;
                const __ = wp.i18n.__;

                // Créer le composant de la barre latérale
                const SmartSEOAIWritingAssistant = function() {
                    return createElement(
                        Fragment,
                        {},
                        createElement(
                            PluginSidebarMoreMenuItem,
                            {
                                target: 'smartseo-ai/writing-assistant',
                                icon: 'edit'
                            },
                            __('Assistant de Rédaction SEO', 'smartseo-ai')
                        ),
                        createElement(
                            PluginSidebar,
                            {
                                name: 'smartseo-ai/writing-assistant',
                                title: __('Assistant de Rédaction SEO', 'smartseo-ai'),
                                icon: 'edit'
                            },
                            createElement(
                                TabPanel,
                                {
                                    className: 'smartseo-ai-tabs',
                                    activeClass: 'is-active',
                                    tabs: [
                                        {
                                            name: 'content',
                                            title: __('Contenu', 'smartseo-ai'),
                                            className: 'smartseo-ai-tab-content',
                                        },
                                        {
                                            name: 'keywords',
                                            title: __('Mots-clés', 'smartseo-ai'),
                                            className: 'smartseo-ai-tab-keywords',
                                        },
                                        {
                                            name: 'analysis',
                                            title: __('Analyse', 'smartseo-ai'),
                                            className: 'smartseo-ai-tab-analysis',
                                        },
                                        {
                                            name: 'trends',
                                            title: __('Tendances', 'smartseo-ai'),
                                            className: 'smartseo-ai-tab-trends',
                                        },
                                    ]
                                },
                                function(tab) {
                                    if (tab.name === 'content') {
                                        return createElement(
                                            PanelBody,
                                            {},
                                            createElement(
                                                'div',
                                                { id: 'smartseo-ai-content-panel' },
                                                ContentPanel && ContentPanel.render ? ContentPanel.render() : 'Chargement...'
                                            )
                                        );
                                    } else if (tab.name === 'keywords') {
                                        return createElement(
                                            PanelBody,
                                            {},
                                            createElement(
                                                'div',
                                                { id: 'smartseo-ai-keyword-panel' },
                                                KeywordPanel && KeywordPanel.render ? KeywordPanel.render() : 'Chargement...'
                                            )
                                        );
                                    } else if (tab.name === 'analysis') {
                                        return createElement(
                                            PanelBody,
                                            {},
                                            createElement(
                                                'div',
                                                { id: 'smartseo-ai-analysis-panel' },
                                                AnalysisPanel && AnalysisPanel.render ? AnalysisPanel.render() : 'Chargement...'
                                            )
                                        );
                                    } else if (tab.name === 'trends') {
                                        return createElement(
                                            PanelBody,
                                            {},
                                            createElement(
                                                'div',
                                                { id: 'smartseo-ai-trends-panel' },
                                                TrendsPanel && TrendsPanel.render ? TrendsPanel.render() : 'Chargement...'
                                            )
                                        );
                                    }
                                }
                            )
                        )
                    );
                };

                // Enregistrer le plugin
                registerPlugin('smartseo-ai-writing-assistant', {
                    render: SmartSEOAIWritingAssistant,
                    icon: 'edit',
                });

                console.log('GutenbergSidebar: Barre latérale ajoutée avec succès');
            } catch (error) {
                console.error('GutenbergSidebar: Erreur lors de l\'ajout de la barre latérale', error);
            }
        },

        /**
         * Met à jour l'analyse en temps réel
         */
        updateLiveAnalysis: function() {
            console.log('GutenbergSidebar: Mise à jour de l\'analyse en temps réel');

            try {
                // Récupérer le contenu et le titre
                const content = wp.data.select('core/editor').getEditedPostContent();
                const title = wp.data.select('core/editor').getEditedPostAttribute('title');
                const postId = wp.data.select('core/editor').getCurrentPostId();

                // Récupérer le mot-clé principal
                const keyword = $('#smartseo-ai-focus-keyword').val();

                // Déclencher l'analyse en temps réel
                if (window.SmartSEOAI && window.SmartSEOAI.LiveAnalyzer && window.SmartSEOAI.LiveAnalyzer.analyzeContent) {
                    window.SmartSEOAI.LiveAnalyzer.analyzeContent(postId, content, title, keyword);
                    console.log('GutenbergSidebar: Analyse en temps réel déclenchée avec succès');
                } else {
                    console.error('GutenbergSidebar: LiveAnalyzer n\'est pas disponible');
                }
            } catch (error) {
                console.error('GutenbergSidebar: Erreur lors de la mise à jour de l\'analyse en temps réel', error);
            }
        },

        /**
         * Affiche les résultats de génération de contenu
         * @param {Object} results Résultats de génération
         * @param {string} type    Type de contenu
         */
        showContentResults: function(results, type) {
            console.log('GutenbergSidebar: Affichage des résultats de génération de contenu', type);

            try {
                if (ContentPanel && ContentPanel.showResults) {
                    ContentPanel.showResults(results, type);
                    console.log('GutenbergSidebar: Résultats de génération de contenu affichés avec succès');
                } else {
                    console.error('GutenbergSidebar: ContentPanel n\'est pas disponible');
                }
            } catch (error) {
                console.error('GutenbergSidebar: Erreur lors de l\'affichage des résultats de génération de contenu', error);
            }
        },

        /**
         * Affiche les résultats d'analyse des mots-clés
         * @param {Object} results Résultats d'analyse
         */
        showKeywordResults: function(results) {
            console.log('GutenbergSidebar: Affichage des résultats d\'analyse des mots-clés');

            try {
                if (KeywordPanel && KeywordPanel.showResults) {
                    KeywordPanel.showResults(results);
                    console.log('GutenbergSidebar: Résultats d\'analyse des mots-clés affichés avec succès');
                } else {
                    console.error('GutenbergSidebar: KeywordPanel n\'est pas disponible');
                }
            } catch (error) {
                console.error('GutenbergSidebar: Erreur lors de l\'affichage des résultats d\'analyse des mots-clés', error);
            }
        },

        /**
         * Affiche les résultats d'analyse en temps réel
         * @param {Object} results Résultats d'analyse
         */
        showLiveAnalysisResults: function(results) {
            console.log('GutenbergSidebar: Affichage des résultats d\'analyse en temps réel');

            try {
                if (AnalysisPanel && AnalysisPanel.showResults) {
                    AnalysisPanel.showResults(results);
                    console.log('GutenbergSidebar: Résultats d\'analyse en temps réel affichés avec succès');
                } else {
                    console.error('GutenbergSidebar: AnalysisPanel n\'est pas disponible');
                }
            } catch (error) {
                console.error('GutenbergSidebar: Erreur lors de l\'affichage des résultats d\'analyse en temps réel', error);
            }
        },

        /**
         * Affiche les résultats de tendances
         * @param {Object} results Résultats de tendances
         */
        showTrendsResults: function(results) {
            console.log('GutenbergSidebar: Affichage des résultats de tendances');

            try {
                if (TrendsPanel && TrendsPanel.showResults) {
                    TrendsPanel.showResults(results);
                    console.log('GutenbergSidebar: Résultats de tendances affichés avec succès');
                } else {
                    console.error('GutenbergSidebar: TrendsPanel n\'est pas disponible');
                }
            } catch (error) {
                console.error('GutenbergSidebar: Erreur lors de l\'affichage des résultats de tendances', error);
            }
        },

        /**
         * Applique une suggestion
         * @param {string} content Contenu à appliquer
         * @param {string} target  Cible de l'application
         */
        applySuggestion: function(content, target) {
            console.log('GutenbergSidebar: Application d\'une suggestion', target);

            try {
                // Utiliser l'API Gutenberg pour appliquer la suggestion
                const dispatch = wp.data.dispatch;
                const select = wp.data.select;

                if (target === 'title') {
                    // Appliquer au titre
                    dispatch('core/editor').editPost({ title: content });
                } else if (target === 'meta_description') {
                    // Appliquer à la meta description
                    dispatch('core/editor').editPost({ meta: { smartseo_ai_meta_description: content } });
                } else if (target === 'content' || target === 'full_article') {
                    // Remplacer tout le contenu
                    dispatch('core/editor').resetBlocks(wp.blocks.parse(content));
                } else if (target === 'introduction' || target === 'conclusion' || target === 'paragraph') {
                    // Insérer le contenu à la position du curseur
                    const selectedBlock = select('core/block-editor').getSelectedBlock();

                    if (selectedBlock) {
                        // Insérer après le bloc sélectionné
                        dispatch('core/block-editor').insertBlocks(
                            wp.blocks.parse('<!-- wp:paragraph --><p>' + content + '</p><!-- /wp:paragraph -->'),
                            select('core/block-editor').getBlockIndex(selectedBlock.clientId) + 1
                        );
                    } else {
                        // Ajouter à la fin
                        dispatch('core/block-editor').insertBlocks(
                            wp.blocks.parse('<!-- wp:paragraph --><p>' + content + '</p><!-- /wp:paragraph -->')
                        );
                    }
                } else if (target === 'h2_headings' || target === 'h3_headings') {
                    // Appliquer les titres
                    const headingLevel = target === 'h2_headings' ? 2 : 3;
                    const headings = content.split('\n');
                    const blocks = [];

                    headings.forEach(function(heading) {
                        if (heading.trim()) {
                            blocks.push(
                                wp.blocks.createBlock('core/heading', {
                                    content: heading.trim(),
                                    level: headingLevel
                                })
                            );
                        }
                    });

                    if (blocks.length > 0) {
                        const selectedBlock = select('core/block-editor').getSelectedBlock();

                        if (selectedBlock) {
                            // Insérer après le bloc sélectionné
                            dispatch('core/block-editor').insertBlocks(
                                blocks,
                                select('core/block-editor').getBlockIndex(selectedBlock.clientId) + 1
                            );
                        } else {
                            // Ajouter à la fin
                            dispatch('core/block-editor').insertBlocks(blocks);
                        }
                    }
                }

                console.log('GutenbergSidebar: Suggestion appliquée avec succès');
            } catch (error) {
                console.error('GutenbergSidebar: Erreur lors de l\'application de la suggestion', error);
            }
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.GutenbergSidebar = GutenbergSidebar;

})(jQuery);
