<?php
/**
 * Classe pour gérer les appels à l'API Gemini de Google
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les appels à l'API Gemini de Google
 */
class SmartSEO_AI_Gemini {

    /**
     * Clé API Gemini
     *
     * @var string
     */
    private $api_key;

    /**
     * URL de base de l'API Gemini
     *
     * @var string
     */
    private $api_url = 'https://generativelanguage.googleapis.com/v1/models/';

    /**
     * Modèle Gemini à utiliser
     *
     * @var string
     */
    private $model;

    /**
     * Constructeur
     */
    public function __construct() {
        $this->api_key = get_option( 'smartseo_ai_gemini_api_key', '' );
        $this->model = get_option( 'smartseo_ai_gemini_model', 'gemini-pro' );

        // Utiliser directement le nom du modèle tel qu'il est stocké
        // Les noms corrects sont 'gemini-1.5-pro', 'gemini-1.5-flash', etc.
    }

    /**
     * Teste la connexion à l'API Gemini
     *
     * @return bool|WP_Error True si la connexion est réussie, WP_Error sinon
     */
    public function test_api_connection() {
        // Vérifier si la clé API est définie
        if ( empty( $this->api_key ) ) {
            return new WP_Error( 'no_api_key', __( 'Clé API Gemini non définie. Veuillez la configurer dans les paramètres du plugin.', 'smartseo-ai' ) );
        }

        // Essayer un appel simple à l'API
        $test_prompt = "Dis bonjour en une phrase.";
        $response = $this->call_gemini_api( $test_prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        return true;
    }

    /**
     * Optimise le contenu d'un article avec l'IA
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu de l'article.
     * @param string $title   Titre de l'article.
     * @return array|WP_Error Résultats de l'optimisation ou erreur.
     */
    public function optimize_content( $post_id, $content, $title ) {
        // Vérifier si la clé API est définie
        if ( empty( $this->api_key ) ) {
            return new WP_Error( 'no_api_key', __( 'Clé API Gemini non définie. Veuillez la configurer dans les paramètres du plugin.', 'smartseo-ai' ) );
        }

        // Tester la connexion à l'API
        $test = $this->test_api_connection();
        if ( is_wp_error( $test ) ) {
            error_log('SmartSEO AI - Échec du test de connexion Gemini: ' . $test->get_error_message());
            return $test;
        }

        // Vérifier si le contenu est vide
        if ( empty( $content ) ) {
            return new WP_Error(
                'empty_content',
                __( 'Le contenu de l\'article est vide. Impossible d\'optimiser un article sans contenu.', 'smartseo-ai' )
            );
        }

        // Vérifier si le titre est vide
        if ( empty( $title ) ) {
            return new WP_Error(
                'empty_title',
                __( 'Le titre de l\'article est vide. Impossible d\'optimiser un article sans titre.', 'smartseo-ai' )
            );
        }

        // Construire le prompt pour l'IA
        $prompt = $this->build_prompt( $content, $title );

        // Appeler l'API Gemini
        $response = $this->call_gemini_api( $prompt );

        // Traiter la réponse
        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Analyser la réponse JSON
        $seo_data = $this->parse_ai_response( $response );

        // Sauvegarder les données dans les métadonnées de l'article
        $this->save_seo_data( $post_id, $seo_data );

        // Calculer un score SEO basé sur les données générées
        $seo_score = $this->calculate_seo_score( $seo_data, $content );
        update_post_meta( $post_id, 'smartseo_ai_seo_score', $seo_score );

        // Ajouter le score au tableau de résultats
        $seo_data['seo_score'] = $seo_score;

        return $seo_data;
    }

    /**
     * Construit le prompt pour l'API Gemini
     *
     * @param string $content Contenu de l'article.
     * @param string $title   Titre de l'article.
     * @return string Prompt formaté.
     */
    private function build_prompt( $content, $title ) {
        return "Tu es un expert en SEO. Analyse ce contenu d'article de blog WordPress et génère des recommandations SEO optimales. Réponds uniquement au format JSON.

Titre de l'article : {$title}

Contenu de l'article :
{$content}

Génère un JSON avec les éléments suivants :
1. meta_description : Une meta description optimisée de 150-160 caractères maximum
2. keywords : 3 à 8 mots-clés pertinents séparés par des virgules
3. seo_title : Un titre SEO optimisé de 50-60 caractères maximum
4. seo_slug : Un slug URL optimisé (sans espaces, uniquement des tirets)
5. og_title : Un titre Open Graph accrocheur
6. og_description : Une description Open Graph attrayante
7. seo_advice : 3-5 conseils d'optimisation SEO spécifiques à cet article

Format de réponse attendu :
{
  \"meta_description\": \"...\",
  \"keywords\": \"mot-clé1, mot-clé2, mot-clé3\",
  \"seo_title\": \"...\",
  \"seo_slug\": \"...\",
  \"og_title\": \"...\",
  \"og_description\": \"...\",
  \"seo_advice\": \"<ul><li>Conseil 1</li><li>Conseil 2</li><li>Conseil 3</li></ul>\"
}

Réponds uniquement avec le JSON, sans texte supplémentaire.";
    }

    /**
     * Appelle l'API Gemini
     *
     * @param string $prompt Prompt à envoyer à l'API.
     * @return string|WP_Error Réponse de l'API ou erreur.
     */
    private function call_gemini_api( $prompt ) {
        // Construire l'URL complète de l'API
        $api_url = $this->api_url . $this->model . ':generateContent?key=' . $this->api_key;

        // Journaliser l'URL pour le débogage
        error_log('SmartSEO AI - URL API Gemini: ' . $api_url);
        error_log('SmartSEO AI - Modèle Gemini utilisé: ' . $this->model);

        // Format de requête pour les modèles Gemini 1.5 et 2.0
        $request_data = array(
            'contents' => array(
                array(
                    'parts' => array(
                        array(
                            'text' => $prompt
                        )
                    )
                )
            ),
            'generationConfig' => array(
                'temperature' => 0.7,
                'maxOutputTokens' => 1024,
                'topP' => 0.95,
                'topK' => 40
            )
        );

        // Journaliser les données de la requête pour le débogage
        error_log('SmartSEO AI - Données de requête Gemini: ' . json_encode($request_data));

        // Préparer les arguments de la requête
        $args = array(
            'method'  => 'POST',
            'timeout' => 45,
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body'    => json_encode( $request_data ),
        );

        // Effectuer la requête
        $response = wp_remote_post( $api_url, $args );

        // Vérifier s'il y a une erreur
        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Vérifier le code de statut
        $status_code = wp_remote_retrieve_response_code( $response );
        if ( $status_code !== 200 ) {
            $body = wp_remote_retrieve_body( $response );
            $error_data = json_decode( $body, true );
            $error_message = isset( $error_data['error']['message'] ) ? $error_data['error']['message'] : __( 'Erreur inconnue de l\'API Gemini.', 'smartseo-ai' );

            // Ajouter des informations de débogage
            $debug_info = sprintf(
                'URL: %s, Modèle: %s, Statut: %s, Réponse: %s',
                $api_url,
                $this->model,
                $status_code,
                $body
            );
            error_log('SmartSEO AI - Erreur Gemini API: ' . $debug_info);

            return new WP_Error( 'api_error', $error_message, array( 'status' => $status_code, 'debug' => $debug_info ) );
        }

        // Récupérer le corps de la réponse
        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );

        // Journaliser la réponse complète pour le débogage
        error_log('SmartSEO AI - Réponse Gemini API: ' . $body);

        // Vérifier si la réponse est valide et extraire le texte selon le format de réponse
        if ( isset( $data['candidates'][0]['content']['parts'][0]['text'] ) ) {
            // Format standard pour Gemini 1.5/2.0
            return $data['candidates'][0]['content']['parts'][0]['text'];
        } else if ( isset( $data['candidates'][0]['text'] ) ) {
            // Format alternatif possible
            return $data['candidates'][0]['text'];
        } else if ( isset( $data['candidates'][0]['content']['text'] ) ) {
            // Autre format possible
            return $data['candidates'][0]['content']['text'];
        } else if ( isset( $data['text'] ) ) {
            // Format simple
            return $data['text'];
        } else {
            // Aucun format reconnu - afficher la structure de la réponse pour le débogage
            $structure = print_r($data, true);
            error_log('SmartSEO AI - Structure de réponse Gemini: ' . $structure);
            return new WP_Error( 'invalid_response', __( 'Réponse invalide de l\'API Gemini. Format non reconnu.', 'smartseo-ai' ), array('response' => $body, 'structure' => $structure) );
        }
    }

    /**
     * Analyse la réponse de l'API Gemini
     *
     * @param string $response Réponse de l'API.
     * @return array Données SEO analysées.
     */
    private function parse_ai_response( $response ) {
        // Essayer de décoder le JSON
        $data = json_decode( $response, true );

        // Vérifier si le décodage a réussi
        if ( json_last_error() !== JSON_ERROR_NONE ) {
            // Essayer d'extraire le JSON de la réponse (au cas où l'IA aurait ajouté du texte)
            preg_match( '/\{.*\}/s', $response, $matches );
            if ( ! empty( $matches[0] ) ) {
                $data = json_decode( $matches[0], true );
            }
        }

        // Si toujours pas de données valides, retourner un tableau vide
        if ( empty( $data ) || json_last_error() !== JSON_ERROR_NONE ) {
            return array();
        }

        // Valider et nettoyer les données
        $seo_data = array();

        // Meta description
        if ( isset( $data['meta_description'] ) ) {
            $seo_data['meta_description'] = sanitize_text_field( $data['meta_description'] );
        }

        // Keywords
        if ( isset( $data['keywords'] ) ) {
            $seo_data['keywords'] = sanitize_text_field( $data['keywords'] );
        }

        // SEO Title
        if ( isset( $data['seo_title'] ) ) {
            $seo_data['seo_title'] = sanitize_text_field( $data['seo_title'] );
        }

        // SEO Slug
        if ( isset( $data['seo_slug'] ) ) {
            $seo_data['seo_slug'] = sanitize_title( $data['seo_slug'] );
        }

        // OG Title
        if ( isset( $data['og_title'] ) ) {
            $seo_data['og_title'] = sanitize_text_field( $data['og_title'] );
        }

        // OG Description
        if ( isset( $data['og_description'] ) ) {
            $seo_data['og_description'] = sanitize_text_field( $data['og_description'] );
        }

        // SEO Advice
        if ( isset( $data['seo_advice'] ) ) {
            $seo_data['seo_advice'] = wp_kses_post( $data['seo_advice'] );
        }

        return $seo_data;
    }

    /**
     * Sauvegarde les données SEO dans les métadonnées de l'article
     *
     * @param int   $post_id  ID de l'article.
     * @param array $seo_data Données SEO à sauvegarder.
     */
    private function save_seo_data( $post_id, $seo_data ) {
        // Sauvegarder chaque élément dans les métadonnées
        foreach ( $seo_data as $key => $value ) {
            update_post_meta( $post_id, 'smartseo_ai_' . $key, $value );
        }
    }

    /**
     * Génère du contenu avec l'API Gemini
     *
     * @param string $prompt Prompt à envoyer à l'API.
     * @return string|WP_Error Réponse de l'API ou erreur.
     */
    public function generate_content( $prompt ) {
        // Vérifier si la clé API est définie
        if ( empty( $this->api_key ) ) {
            return new WP_Error( 'no_api_key', __( 'Clé API Gemini non définie. Veuillez la configurer dans les paramètres du plugin.', 'smartseo-ai' ) );
        }

        // Appeler l'API Gemini
        return $this->call_gemini_api( $prompt );
    }

    /**
     * Calcule un score SEO basé sur les données générées
     *
     * @param array  $seo_data Données SEO générées.
     * @param string $content  Contenu de l'article.
     * @return int Score SEO (0-100).
     */
    private function calculate_seo_score( $seo_data, $content ) {
        $score = 0;
        $total_points = 0;

        // Vérifier la meta description
        if ( ! empty( $seo_data['meta_description'] ) ) {
            $meta_length = strlen( $seo_data['meta_description'] );
            if ( $meta_length >= 120 && $meta_length <= 160 ) {
                $score += 15;
            } elseif ( $meta_length >= 100 ) {
                $score += 10;
            } else {
                $score += 5;
            }
            $total_points += 15;
        }

        // Vérifier les mots-clés
        if ( ! empty( $seo_data['keywords'] ) ) {
            $keywords = explode( ',', $seo_data['keywords'] );
            $keyword_count = count( $keywords );

            if ( $keyword_count >= 3 && $keyword_count <= 8 ) {
                $score += 15;
            } elseif ( $keyword_count > 0 ) {
                $score += 8;
            }
            $total_points += 15;

            // Vérifier si les mots-clés sont présents dans le contenu
            $keyword_presence = 0;
            foreach ( $keywords as $keyword ) {
                $keyword = trim( $keyword );
                if ( ! empty( $keyword ) && stripos( $content, $keyword ) !== false ) {
                    $keyword_presence++;
                }
            }

            if ( $keyword_count > 0 ) {
                $keyword_ratio = $keyword_presence / $keyword_count;
                $score += round( $keyword_ratio * 15 );
                $total_points += 15;
            }
        }

        // Vérifier le titre SEO
        if ( ! empty( $seo_data['seo_title'] ) ) {
            $title_length = strlen( $seo_data['seo_title'] );
            if ( $title_length >= 40 && $title_length <= 60 ) {
                $score += 15;
            } elseif ( $title_length >= 30 ) {
                $score += 10;
            } else {
                $score += 5;
            }
            $total_points += 15;
        }

        // Vérifier le slug SEO
        if ( ! empty( $seo_data['seo_slug'] ) ) {
            $slug_words = count( explode( '-', $seo_data['seo_slug'] ) );
            if ( $slug_words >= 3 && $slug_words <= 8 ) {
                $score += 10;
            } elseif ( $slug_words > 0 ) {
                $score += 5;
            }
            $total_points += 10;
        }

        // Vérifier les données Open Graph
        if ( ! empty( $seo_data['og_title'] ) && ! empty( $seo_data['og_description'] ) ) {
            $score += 15;
            $total_points += 15;
        } elseif ( ! empty( $seo_data['og_title'] ) || ! empty( $seo_data['og_description'] ) ) {
            $score += 8;
            $total_points += 15;
        }

        // Vérifier les conseils SEO
        if ( ! empty( $seo_data['seo_advice'] ) ) {
            $score += 15;
            $total_points += 15;
        }

        // Calculer le score final (sur 100)
        $final_score = ( $total_points > 0 ) ? round( ( $score / $total_points ) * 100 ) : 0;

        return $final_score;
    }
}
