<?php
/**
 * Testeur pour les améliorations du module d'optimisation en masse
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour tester les nouvelles fonctionnalités du module d'optimisation en masse
 */
class SmartSEO_AI_Bulk_Optimizer_Tester {

    /**
     * Instance singleton
     *
     * @var SmartSEO_AI_Bulk_Optimizer_Tester
     */
    private static $instance = null;

    /**
     * Résultats des tests
     *
     * @var array
     */
    private $test_results = array();

    /**
     * Constructeur
     */
    private function __construct() {
        // Hook pour ajouter une page de test dans l'admin (uniquement en mode debug)
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            add_action( 'admin_menu', array( $this, 'add_test_menu' ) );
            add_action( 'wp_ajax_smartseo_ai_test_bulk_optimizer', array( $this, 'ajax_run_tests' ) );
        }
    }

    /**
     * Récupère l'instance singleton
     *
     * @return SmartSEO_AI_Bulk_Optimizer_Tester
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Ajoute le menu de test
     */
    public function add_test_menu() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Tests Module Optimisation', 'smartseo-ai' ),
            __( 'Tests Module Optimisation', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-bulk-optimizer-tests',
            array( $this, 'render_test_page' )
        );
    }

    /**
     * Affiche la page de test
     */
    public function render_test_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'Tests Module d\'Optimisation en Masse - SmartSEO AI', 'smartseo-ai' ); ?></h1>
            
            <div class="notice notice-info">
                <p><?php _e( 'Cette page permet de tester les nouvelles fonctionnalités du module d\'optimisation en masse.', 'smartseo-ai' ); ?></p>
            </div>

            <div class="test-controls">
                <button type="button" class="button button-primary" id="run-bulk-tests">
                    <?php _e( 'Lancer tous les tests', 'smartseo-ai' ); ?>
                </button>
                
                <button type="button" class="button button-secondary" id="toggle-interface">
                    <?php _e( 'Basculer l\'interface', 'smartseo-ai' ); ?>
                </button>
                
                <button type="button" class="button button-secondary" id="reset-settings">
                    <?php _e( 'Réinitialiser les paramètres', 'smartseo-ai' ); ?>
                </button>
            </div>

            <div id="test-results" style="margin-top: 20px;"></div>
        </div>

        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('#run-bulk-tests').on('click', function() {
                var $button = $(this);
                var $results = $('#test-results');
                
                $button.prop('disabled', true).text('<?php _e( 'Tests en cours...', 'smartseo-ai' ); ?>');
                $results.html('<div class="notice notice-info"><p><?php _e( 'Exécution des tests...', 'smartseo-ai' ); ?></p></div>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'smartseo_ai_test_bulk_optimizer',
                        nonce: '<?php echo wp_create_nonce( 'smartseo_ai_bulk_tests' ); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $results.html(response.data.html);
                        } else {
                            $results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        $results.html('<div class="notice notice-error"><p><?php _e( 'Erreur lors de l\'exécution des tests.', 'smartseo-ai' ); ?></p></div>');
                    },
                    complete: function() {
                        $button.prop('disabled', false).text('<?php _e( 'Lancer tous les tests', 'smartseo-ai' ); ?>');
                    }
                });
            });

            $('#toggle-interface').on('click', function() {
                var current = <?php echo get_option( 'smartseo_ai_enhanced_bulk_optimizer', true ) ? 'true' : 'false'; ?>;
                var newValue = !current;
                
                $.post(ajaxurl, {
                    action: 'update_option',
                    option_name: 'smartseo_ai_enhanced_bulk_optimizer',
                    option_value: newValue ? '1' : '0',
                    _wpnonce: '<?php echo wp_create_nonce( 'update_option' ); ?>'
                }, function() {
                    alert('Interface basculée ! Rechargez la page d\'optimisation en masse pour voir les changements.');
                });
            });

            $('#reset-settings').on('click', function() {
                if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?')) {
                    $.post(ajaxurl, {
                        action: 'smartseo_ai_reset_bulk_settings',
                        _wpnonce: '<?php echo wp_create_nonce( 'reset_bulk_settings' ); ?>'
                    }, function() {
                        alert('Paramètres réinitialisés !');
                    });
                }
            });
        });
        </script>

        <style>
        .test-controls {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        </style>
        <?php
    }

    /**
     * AJAX : Lance tous les tests
     */
    public function ajax_run_tests() {
        check_ajax_referer( 'smartseo_ai_bulk_tests', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'smartseo-ai' ) );
        }

        $this->test_results = array();

        // Lancer tous les tests
        $this->test_bulk_optimizer_class();
        $this->test_enhanced_interface();
        $this->test_filter_functionality();
        $this->test_settings_page();
        $this->test_assets_loading();

        // Générer le HTML des résultats
        $html = $this->generate_results_html();

        wp_send_json_success( array( 'html' => $html ) );
    }

    /**
     * Teste la classe bulk optimizer
     */
    private function test_bulk_optimizer_class() {
        $this->add_test_result( 'Bulk Optimizer Class', 'Tests de la classe principale' );

        try {
            $bulk_optimizer = new SmartSEO_AI_Bulk_Optimizer();
            
            // Test 1: Vérification de l'instance
            $this->add_test_result( 'Class Instance', 
                $bulk_optimizer instanceof SmartSEO_AI_Bulk_Optimizer ? 'PASS' : 'FAIL',
                'Instance créée avec succès'
            );

            // Test 2: Méthodes disponibles
            $required_methods = array(
                'get_available_post_types',
                'get_filtered_url',
                'get_sort_options',
                'get_score_class',
            );

            foreach ( $required_methods as $method ) {
                $this->add_test_result( "Method $method", 
                    method_exists( $bulk_optimizer, $method ) ? 'PASS' : 'FAIL',
                    "Méthode $method disponible"
                );
            }

            // Test 3: Types de posts disponibles
            $post_types = $bulk_optimizer->get_available_post_types();
            $this->add_test_result( 'Available Post Types', 
                is_array( $post_types ) && ! empty( $post_types ) ? 'PASS' : 'FAIL',
                count( $post_types ) . ' types de posts trouvés'
            );

            // Test 4: Options de tri
            $sort_options = $bulk_optimizer->get_sort_options();
            $this->add_test_result( 'Sort Options', 
                is_array( $sort_options ) && ! empty( $sort_options ) ? 'PASS' : 'FAIL',
                count( $sort_options ) . ' options de tri disponibles'
            );

        } catch ( Exception $e ) {
            $this->add_test_result( 'Bulk Optimizer Exception', 'FAIL', $e->getMessage() );
        }
    }

    /**
     * Teste l'interface améliorée
     */
    private function test_enhanced_interface() {
        $this->add_test_result( 'Enhanced Interface', 'Tests de l\'interface améliorée' );

        // Test 1: Fichiers de vue
        $view_files = array(
            'bulk-optimizer-enhanced.php',
            'bulk-optimizer-settings.php',
        );

        foreach ( $view_files as $file ) {
            $file_path = SMARTSEO_AI_PLUGIN_DIR . 'admin/views/' . $file;
            $this->add_test_result( "View File $file", 
                file_exists( $file_path ) ? 'PASS' : 'FAIL',
                $file_exists ? "Fichier $file existe" : "Fichier $file manquant"
            );
        }

        // Test 2: Fichiers CSS/JS
        $asset_files = array(
            'admin/css/smartseo-ai-bulk-optimizer-enhanced.css',
            'admin/js/smartseo-ai-bulk-optimizer-enhanced.js',
        );

        foreach ( $asset_files as $file ) {
            $file_path = SMARTSEO_AI_PLUGIN_DIR . $file;
            $this->add_test_result( "Asset File " . basename( $file ), 
                file_exists( $file_path ) ? 'PASS' : 'FAIL',
                file_exists( $file_path ) ? "Fichier $file existe" : "Fichier $file manquant"
            );
        }

        // Test 3: Option d'interface améliorée
        $enhanced_option = get_option( 'smartseo_ai_enhanced_bulk_optimizer' );
        $this->add_test_result( 'Enhanced Interface Option', 
            ! is_null( $enhanced_option ) ? 'PASS' : 'FAIL',
            'Option d\'interface : ' . ( $enhanced_option ? 'Activée' : 'Désactivée' )
        );
    }

    /**
     * Teste les fonctionnalités de filtrage
     */
    private function test_filter_functionality() {
        $this->add_test_result( 'Filter Functionality', 'Tests des fonctionnalités de filtrage' );

        try {
            $bulk_optimizer = new SmartSEO_AI_Bulk_Optimizer();

            // Test 1: URL avec filtres
            $test_params = array( 'post_types' => array( 'post' ), 'score_range' => 'good' );
            $filtered_url = $bulk_optimizer->get_filtered_url( $test_params );
            
            $this->add_test_result( 'Filtered URL Generation', 
                ! empty( $filtered_url ) && strpos( $filtered_url, 'post_types' ) !== false ? 'PASS' : 'FAIL',
                'URL avec filtres générée'
            );

            // Test 2: Classes de score
            $score_classes = array(
                90 => 'excellent',
                70 => 'good', 
                50 => 'average',
                30 => 'poor',
            );

            foreach ( $score_classes as $score => $expected_class ) {
                $actual_class = $bulk_optimizer->get_score_class( $score );
                $this->add_test_result( "Score Class $score", 
                    $actual_class === $expected_class ? 'PASS' : 'FAIL',
                    "Score $score -> Classe $actual_class"
                );
            }

        } catch ( Exception $e ) {
            $this->add_test_result( 'Filter Functionality Exception', 'FAIL', $e->getMessage() );
        }
    }

    /**
     * Teste la page de paramètres
     */
    private function test_settings_page() {
        $this->add_test_result( 'Settings Page', 'Tests de la page de paramètres' );

        // Test 1: Options de paramètres
        $settings_options = array(
            'smartseo_ai_enhanced_bulk_optimizer',
            'smartseo_ai_bulk_batch_size',
            'smartseo_ai_bulk_delay',
            'smartseo_ai_bulk_max_concurrent',
            'smartseo_ai_bulk_auto_retry',
            'smartseo_ai_bulk_retry_attempts',
        );

        foreach ( $settings_options as $option ) {
            $value = get_option( $option );
            $this->add_test_result( "Option $option", 
                ! is_null( $value ) ? 'PASS' : 'INFO',
                "Valeur : " . ( is_bool( $value ) ? ( $value ? 'true' : 'false' ) : $value )
            );
        }
    }

    /**
     * Teste le chargement des assets
     */
    private function test_assets_loading() {
        $this->add_test_result( 'Assets Loading', 'Tests du chargement des assets' );

        // Test 1: Hooks WordPress
        $hooks_registered = array(
            'admin_menu' => has_action( 'admin_menu' ),
            'admin_enqueue_scripts' => has_action( 'admin_enqueue_scripts' ),
        );

        foreach ( $hooks_registered as $hook => $registered ) {
            $this->add_test_result( "Hook $hook", 
                $registered ? 'PASS' : 'FAIL',
                $registered ? "Hook $hook enregistré" : "Hook $hook manquant"
            );
        }
    }

    /**
     * Ajoute un résultat de test
     *
     * @param string $test_name Nom du test.
     * @param string $status Statut (PASS/FAIL/INFO).
     * @param string $message Message descriptif.
     */
    private function add_test_result( $test_name, $status, $message = '' ) {
        $this->test_results[] = array(
            'name' => $test_name,
            'status' => $status,
            'message' => $message,
            'timestamp' => current_time( 'mysql' ),
        );
    }

    /**
     * Génère le HTML des résultats de test
     *
     * @return string HTML des résultats.
     */
    private function generate_results_html() {
        $html = '<div class="test-results">';
        $html .= '<h2>' . __( 'Résultats des tests du module d\'optimisation en masse', 'smartseo-ai' ) . '</h2>';

        $pass_count = 0;
        $fail_count = 0;
        $info_count = 0;

        foreach ( $this->test_results as $result ) {
            $status_class = strtolower( $result['status'] );
            $status_color = '';
            
            switch ( $result['status'] ) {
                case 'PASS':
                    $status_color = 'color: #46b450; font-weight: bold;';
                    $pass_count++;
                    break;
                case 'FAIL':
                    $status_color = 'color: #dc3232; font-weight: bold;';
                    $fail_count++;
                    break;
                default:
                    $status_color = 'color: #0073aa; font-weight: bold;';
                    $info_count++;
                    break;
            }

            $html .= '<div style="padding: 10px; border-left: 4px solid ' . 
                     ( $result['status'] === 'PASS' ? '#46b450' : ( $result['status'] === 'FAIL' ? '#dc3232' : '#0073aa' ) ) . 
                     '; margin-bottom: 10px; background: #f9f9f9;">';
            $html .= '<strong>' . esc_html( $result['name'] ) . '</strong> ';
            $html .= '<span style="' . $status_color . '">[' . esc_html( $result['status'] ) . ']</span>';
            
            if ( ! empty( $result['message'] ) ) {
                $html .= '<br><small>' . esc_html( $result['message'] ) . '</small>';
            }
            
            $html .= '</div>';
        }

        // Résumé
        $html .= '<div style="margin-top: 20px; padding: 15px; background: #f0f0f1; border-radius: 4px;">';
        $html .= '<h3>' . __( 'Résumé', 'smartseo-ai' ) . '</h3>';
        $html .= '<p>';
        $html .= '<span style="color: #46b450; font-weight: bold;">' . $pass_count . ' tests réussis</span>, ';
        $html .= '<span style="color: #dc3232; font-weight: bold;">' . $fail_count . ' tests échoués</span>, ';
        $html .= '<span style="color: #0073aa; font-weight: bold;">' . $info_count . ' informations</span>';
        $html .= '</p>';
        
        if ( $fail_count === 0 ) {
            $html .= '<div class="notice notice-success inline"><p><strong>' . 
                     __( 'Tous les tests sont passés avec succès ! Le module d\'optimisation en masse est correctement implémenté.', 'smartseo-ai' ) . 
                     '</strong></p></div>';
        } else {
            $html .= '<div class="notice notice-warning inline"><p><strong>' . 
                     __( 'Certains tests ont échoué. Vérifiez la configuration et les fichiers.', 'smartseo-ai' ) . 
                     '</strong></p></div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }
}
