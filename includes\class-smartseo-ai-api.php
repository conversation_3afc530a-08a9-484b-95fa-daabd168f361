<?php
/**
 * Classe pour gérer les appels à l'API OpenAI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les appels à l'API OpenAI
 */
class SmartSEO_AI_API {

    /**
     * Clé API OpenAI
     *
     * @var string
     */
    private $api_key;

    /**
     * URL de base de l'API OpenAI
     *
     * @var string
     */
    private $api_url = 'https://api.openai.com/v1/chat/completions';

    /**
     * Constructeur
     */
    public function __construct() {
        $this->api_key = get_option( 'smartseo_ai_openai_api_key', '' );
    }

    /**
     * Génère du contenu avec l'API OpenAI
     *
     * @param string $prompt Prompt à envoyer à l'API.
     * @return string|WP_Error Réponse de l'API ou erreur.
     */
    public function generate_content( $prompt ) {
        // Vérifier si la clé API est définie
        if ( empty( $this->api_key ) ) {
            return new WP_Error( 'no_api_key', __( 'Clé API OpenAI non définie. Veuillez la configurer dans les paramètres du plugin.', 'smartseo-ai' ) );
        }

        // Appeler l'API OpenAI
        return $this->call_openai_api( $prompt );
    }

    /**
     * Optimise le contenu d'un article avec l'IA (méthode directe sans cache)
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu de l'article.
     * @param string $title   Titre de l'article.
     * @return array|WP_Error Résultats de l'optimisation ou erreur.
     */
    public function optimize_content_direct( $post_id, $content, $title ) {
        // Vérifier si la clé API est définie
        if ( empty( $this->api_key ) ) {
            return new WP_Error( 'no_api_key', __( 'Clé API OpenAI non définie. Veuillez la configurer dans les paramètres du plugin.', 'smartseo-ai' ) );
        }

        // Construire le prompt pour l'IA
        $prompt = $this->build_prompt( $content, $title );

        // Appeler l'API OpenAI
        $response = $this->call_openai_api( $prompt );

        // Traiter la réponse
        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Analyser la réponse JSON
        $seo_data = $this->parse_ai_response( $response );

        // Sauvegarder les données dans les métadonnées de l'article
        $this->save_seo_data( $post_id, $seo_data );

        // Calculer un score SEO basé sur les données générées
        $seo_score = $this->calculate_seo_score( $seo_data, $content );
        update_post_meta( $post_id, 'smartseo_ai_seo_score', $seo_score );

        // Ajouter le score au tableau de résultats
        $seo_data['seo_score'] = $seo_score;

        return $seo_data;
    }

    /**
     * Optimise le contenu d'un article avec l'IA (avec cache et performance)
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu de l'article.
     * @param string $title   Titre de l'article.
     * @return array|WP_Error Résultats de l'optimisation ou erreur.
     */
    public function optimize_content( $post_id, $content, $title ) {
        // Vérifier si la clé API est définie
        if ( empty( $this->api_key ) ) {
            return new WP_Error( 'no_api_key', __( 'Clé API OpenAI non définie. Veuillez la configurer dans les paramètres du plugin.', 'smartseo-ai' ) );
        }

        // Vérifier si le contenu est vide
        if ( empty( $content ) ) {
            return new WP_Error(
                'empty_content',
                __( 'Le contenu de l\'article est vide. Impossible d\'optimiser un article sans contenu.', 'smartseo-ai' )
            );
        }

        // Vérifier si le titre est vide
        if ( empty( $title ) ) {
            return new WP_Error(
                'empty_title',
                __( 'Le titre de l\'article est vide. Impossible d\'optimiser un article sans titre.', 'smartseo-ai' )
            );
        }

        // Utiliser le gestionnaire de performance pour l'optimisation avec cache
        if ( class_exists( 'SmartSEO_AI_Performance_Manager' ) ) {
            $performance_manager = SmartSEO_AI_Performance_Manager::get_instance();
            return $performance_manager->optimize_content_with_cache( $post_id, $content, $title );
        }

        // Fallback vers la méthode directe si le gestionnaire de performance n'est pas disponible
        return $this->optimize_content_direct( $post_id, $content, $title );
    }

    /**
     * Construit le prompt pour l'API OpenAI
     *
     * @param string $content Contenu de l'article.
     * @param string $title   Titre de l'article.
     * @return string Prompt formaté.
     */
    private function build_prompt( $content, $title ) {
        return "Tu es un expert en SEO. Analyse ce contenu d'article de blog WordPress et génère des recommandations SEO optimales. Réponds uniquement au format JSON.

Titre de l'article : {$title}

Contenu de l'article :
{$content}

Génère un JSON avec les éléments suivants :
1. meta_description : Une meta description optimisée de 150-160 caractères maximum
2. keywords : 3 à 8 mots-clés pertinents séparés par des virgules
3. seo_title : Un titre SEO optimisé de 50-60 caractères maximum
4. seo_slug : Un slug URL optimisé (sans espaces, uniquement des tirets)
5. og_title : Un titre Open Graph accrocheur
6. og_description : Une description Open Graph attrayante
7. seo_advice : 3-5 conseils d'optimisation SEO spécifiques à cet article

Format de réponse attendu :
{
  \"meta_description\": \"...\",
  \"keywords\": \"mot-clé1, mot-clé2, mot-clé3\",
  \"seo_title\": \"...\",
  \"seo_slug\": \"...\",
  \"og_title\": \"...\",
  \"og_description\": \"...\",
  \"seo_advice\": \"<ul><li>Conseil 1</li><li>Conseil 2</li><li>Conseil 3</li></ul>\"
}

Réponds uniquement avec le JSON, sans texte supplémentaire.";
    }

    /**
     * Appelle l'API OpenAI
     *
     * @param string $prompt Prompt à envoyer à l'API.
     * @return string|WP_Error Réponse de l'API ou erreur.
     */
    private function call_openai_api( $prompt ) {
        // Préparer les arguments de la requête
        $args = array(
            'method'  => 'POST',
            'timeout' => 45,
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type'  => 'application/json',
            ),
            'body'    => json_encode( array(
                'model'       => 'gpt-4',
                'messages'    => array(
                    array(
                        'role'    => 'system',
                        'content' => 'Tu es un expert en SEO qui génère des recommandations SEO pour des articles de blog WordPress. Réponds uniquement au format JSON demandé.',
                    ),
                    array(
                        'role'    => 'user',
                        'content' => $prompt,
                    ),
                ),
                'temperature' => 0.7,
                'max_tokens'  => 1000,
            ) ),
        );

        // Effectuer la requête
        $response = wp_remote_post( $this->api_url, $args );

        // Vérifier s'il y a une erreur
        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Vérifier le code de statut
        $status_code = wp_remote_retrieve_response_code( $response );
        if ( $status_code !== 200 ) {
            $body = wp_remote_retrieve_body( $response );
            $error_data = json_decode( $body, true );
            $error_message = isset( $error_data['error']['message'] ) ? $error_data['error']['message'] : __( 'Erreur inconnue de l\'API OpenAI.', 'smartseo-ai' );
            return new WP_Error( 'api_error', $error_message, array( 'status' => $status_code ) );
        }

        // Récupérer le corps de la réponse
        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );

        // Journaliser la réponse complète pour le débogage
        error_log('SmartSEO AI - Réponse OpenAI API: ' . $body);

        // Vérifier si la réponse est valide
        if ( ! isset( $data['choices'][0]['message']['content'] ) ) {
            // Journaliser la structure de la réponse pour le débogage
            $structure = print_r($data, true);
            error_log('SmartSEO AI - Structure de réponse OpenAI invalide: ' . $structure);

            return new WP_Error(
                'invalid_response',
                __( 'Réponse invalide de l\'API OpenAI. Vérifiez votre clé API et les paramètres.', 'smartseo-ai' ),
                array('response' => $body)
            );
        }

        return $data['choices'][0]['message']['content'];
    }

    /**
     * Analyse la réponse de l'API OpenAI
     *
     * @param string $response Réponse de l'API.
     * @return array Données SEO analysées.
     */
    private function parse_ai_response( $response ) {
        // Essayer de décoder le JSON
        $data = json_decode( $response, true );

        // Vérifier si le décodage a réussi
        if ( json_last_error() !== JSON_ERROR_NONE ) {
            // Essayer d'extraire le JSON de la réponse (au cas où l'IA aurait ajouté du texte)
            preg_match( '/\{.*\}/s', $response, $matches );
            if ( ! empty( $matches[0] ) ) {
                $data = json_decode( $matches[0], true );
            }
        }

        // Si toujours pas de données valides, journaliser l'erreur et retourner un tableau vide
        if ( empty( $data ) || json_last_error() !== JSON_ERROR_NONE ) {
            error_log('SmartSEO AI - Erreur de décodage JSON: ' . json_last_error_msg() . ' - Réponse: ' . $response);
            return array();
        }

        // Valider et nettoyer les données
        $seo_data = array();

        // Meta description
        if ( isset( $data['meta_description'] ) ) {
            $seo_data['meta_description'] = sanitize_text_field( $data['meta_description'] );
        }

        // Keywords
        if ( isset( $data['keywords'] ) ) {
            $seo_data['keywords'] = sanitize_text_field( $data['keywords'] );
        }

        // SEO Title
        if ( isset( $data['seo_title'] ) ) {
            $seo_data['seo_title'] = sanitize_text_field( $data['seo_title'] );
        }

        // SEO Slug
        if ( isset( $data['seo_slug'] ) ) {
            $seo_data['seo_slug'] = sanitize_title( $data['seo_slug'] );
        }

        // OG Title
        if ( isset( $data['og_title'] ) ) {
            $seo_data['og_title'] = sanitize_text_field( $data['og_title'] );
        }

        // OG Description
        if ( isset( $data['og_description'] ) ) {
            $seo_data['og_description'] = sanitize_text_field( $data['og_description'] );
        }

        // SEO Advice
        if ( isset( $data['seo_advice'] ) ) {
            $seo_data['seo_advice'] = wp_kses_post( $data['seo_advice'] );
        }

        return $seo_data;
    }

    /**
     * Sauvegarde les données SEO dans les métadonnées de l'article
     *
     * @param int   $post_id  ID de l'article.
     * @param array $seo_data Données SEO à sauvegarder.
     */
    private function save_seo_data( $post_id, $seo_data ) {
        // Sauvegarder chaque élément dans les métadonnées
        foreach ( $seo_data as $key => $value ) {
            update_post_meta( $post_id, 'smartseo_ai_' . $key, $value );
        }
    }

    /**
     * Calcule un score SEO basé sur les données générées
     *
     * @param array  $seo_data Données SEO générées.
     * @param string $content  Contenu de l'article.
     * @return int Score SEO (0-100).
     */
    private function calculate_seo_score( $seo_data, $content ) {
        $score = 0;
        $total_points = 0;

        // Vérifier la meta description
        if ( ! empty( $seo_data['meta_description'] ) ) {
            $meta_length = strlen( $seo_data['meta_description'] );
            if ( $meta_length >= 120 && $meta_length <= 160 ) {
                $score += 15;
            } elseif ( $meta_length >= 100 ) {
                $score += 10;
            } else {
                $score += 5;
            }
            $total_points += 15;
        }

        // Vérifier les mots-clés
        if ( ! empty( $seo_data['keywords'] ) ) {
            $keywords = explode( ',', $seo_data['keywords'] );
            $keyword_count = count( $keywords );

            if ( $keyword_count >= 3 && $keyword_count <= 8 ) {
                $score += 15;
            } elseif ( $keyword_count > 0 ) {
                $score += 8;
            }
            $total_points += 15;

            // Vérifier si les mots-clés sont présents dans le contenu
            $keyword_presence = 0;
            foreach ( $keywords as $keyword ) {
                $keyword = trim( $keyword );
                if ( ! empty( $keyword ) && stripos( $content, $keyword ) !== false ) {
                    $keyword_presence++;
                }
            }

            if ( $keyword_count > 0 ) {
                $keyword_ratio = $keyword_presence / $keyword_count;
                $score += round( $keyword_ratio * 15 );
                $total_points += 15;
            }
        }

        // Vérifier le titre SEO
        if ( ! empty( $seo_data['seo_title'] ) ) {
            $title_length = strlen( $seo_data['seo_title'] );
            if ( $title_length >= 40 && $title_length <= 60 ) {
                $score += 15;
            } elseif ( $title_length >= 30 ) {
                $score += 10;
            } else {
                $score += 5;
            }
            $total_points += 15;
        }

        // Vérifier le slug SEO
        if ( ! empty( $seo_data['seo_slug'] ) ) {
            $slug_words = count( explode( '-', $seo_data['seo_slug'] ) );
            if ( $slug_words >= 3 && $slug_words <= 8 ) {
                $score += 10;
            } elseif ( $slug_words > 0 ) {
                $score += 5;
            }
            $total_points += 10;
        }

        // Vérifier les données Open Graph
        if ( ! empty( $seo_data['og_title'] ) && ! empty( $seo_data['og_description'] ) ) {
            $score += 15;
            $total_points += 15;
        } elseif ( ! empty( $seo_data['og_title'] ) || ! empty( $seo_data['og_description'] ) ) {
            $score += 8;
            $total_points += 15;
        }

        // Vérifier les conseils SEO
        if ( ! empty( $seo_data['seo_advice'] ) ) {
            $score += 15;
            $total_points += 15;
        }

        // Calculer le score final (sur 100)
        $final_score = ( $total_points > 0 ) ? round( ( $score / $total_points ) * 100 ) : 0;

        return $final_score;
    }

    /**
     * Teste la connexion à l'API OpenAI
     *
     * @return bool|WP_Error True si la connexion fonctionne, WP_Error sinon.
     */
    public function test_api_connection() {
        // Vérifier si la clé API est définie
        if ( empty( $this->api_key ) ) {
            return new WP_Error( 'no_api_key', __( 'Clé API OpenAI non définie.', 'smartseo-ai' ) );
        }

        // Test simple avec un prompt minimal
        $test_prompt = "Réponds simplement 'OK' pour confirmer que l'API fonctionne.";

        $args = array(
            'method'  => 'POST',
            'timeout' => 15,
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type'  => 'application/json',
            ),
            'body'    => json_encode( array(
                'model'       => 'gpt-3.5-turbo', // Modèle moins cher pour les tests
                'messages'    => array(
                    array(
                        'role'    => 'user',
                        'content' => $test_prompt,
                    ),
                ),
                'max_tokens'  => 10,
            ) ),
        );

        $response = wp_remote_post( $this->api_url, $args );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $status_code = wp_remote_retrieve_response_code( $response );
        if ( $status_code !== 200 ) {
            $body = wp_remote_retrieve_body( $response );
            $error_data = json_decode( $body, true );
            $error_message = isset( $error_data['error']['message'] ) ? $error_data['error']['message'] : __( 'Erreur de connexion à l\'API OpenAI.', 'smartseo-ai' );
            return new WP_Error( 'api_connection_error', $error_message, array( 'status' => $status_code ) );
        }

        return true;
    }
}
