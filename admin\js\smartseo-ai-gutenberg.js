/**
 * Script JavaScript pour l'intégration avec <PERSON>
 */
(function(wp) {
    'use strict';

    // Extraire les composants nécessaires
    const { __ } = wp.i18n;
    const { registerPlugin } = wp.plugins;
    const { PluginSidebar, PluginSidebarMoreMenuItem } = wp.editPost;
    const { PanelBody, TextControl, TextareaControl, Button, Spinner, Notice } = wp.components;
    const { Fragment, useState, useEffect } = wp.element;
    const { useSelect, useDispatch } = wp.data;
    const { MediaUpload } = wp.blockEditor;
    const apiFetch = wp.apiFetch;

    /**
     * Composant principal du panneau latéral
     */
    const SmartSEOAISidebar = () => {
        // États
        const [isOptimizing, setIsOptimizing] = useState(false);
        const [notice, setNotice] = useState(null);
        const [seoData, setSeoData] = useState({
            meta_description: '',
            keywords: '',
            seo_title: '',
            seo_slug: '',
            og_title: '',
            og_description: '',
            og_image: '',
            seo_advice: '',
            seo_score: 0
        });

        // Récupérer l'ID de l'article et le contenu
        const { postId, postContent, postTitle, postSlug } = useSelect(select => {
            const { getCurrentPostId, getEditedPostContent, getEditedPostAttribute } = select('core/editor');
            return {
                postId: getCurrentPostId(),
                postContent: getEditedPostContent(),
                postTitle: getEditedPostAttribute('title'),
                postSlug: getEditedPostAttribute('slug')
            };
        }, []);

        // Dispatcher pour mettre à jour les attributs de l'article
        const { editPost } = useDispatch('core/editor');

        // Charger les données SEO au chargement du composant
        useEffect(() => {
            loadSeoData();
        }, [postId]);

        /**
         * Charge les données SEO de l'article
         */
        const loadSeoData = () => {
            apiFetch({
                path: `smartseo-ai/v1/get-seo-data/${postId}`,
                method: 'GET'
            }).then(response => {
                setSeoData(response);
            }).catch(error => {
                console.error('Erreur lors du chargement des données SEO:', error);
            });
        };

        /**
         * Optimise l'article avec l'IA
         */
        const optimizePost = () => {
            setIsOptimizing(true);
            setNotice(null);

            apiFetch({
                path: 'smartseo-ai/v1/optimize',
                method: 'POST',
                data: {
                    post_id: postId
                }
            }).then(response => {
                // Mettre à jour les données SEO
                setSeoData(response);
                
                // Afficher une notification de succès
                setNotice({
                    type: 'success',
                    message: smartseoAiGutenberg.i18n.success
                });
                
                // Mettre à jour le slug si nécessaire
                if (response.seo_slug && response.seo_slug !== postSlug) {
                    // Ne pas mettre à jour automatiquement, laisser l'utilisateur le faire manuellement
                }
            }).catch(error => {
                // Afficher une notification d'erreur
                setNotice({
                    type: 'error',
                    message: error.message || smartseoAiGutenberg.i18n.error
                });
            }).finally(() => {
                setIsOptimizing(false);
            });
        };

        /**
         * Met à jour une donnée SEO
         *
         * @param {string} key   Clé de la donnée
         * @param {string} value Valeur de la donnée
         */
        const updateSeoData = (key, value) => {
            setSeoData({
                ...seoData,
                [key]: value
            });
            
            // Mettre à jour la métadonnée dans l'article
            const meta = {};
            meta[`smartseo_ai_${key}`] = value;
            editPost({ meta });
        };

        /**
         * Applique le slug optimisé
         */
        const applySlug = () => {
            if (seoData.seo_slug) {
                editPost({ slug: seoData.seo_slug });
            }
        };

        /**
         * Retourne la classe CSS en fonction du score SEO
         *
         * @param {number} score Score SEO
         * @return {string} Classe CSS
         */
        const getScoreClass = (score) => {
            if (score >= 80) {
                return 'good';
            } else if (score >= 50) {
                return 'average';
            } else {
                return 'poor';
            }
        };

        return (
            <Fragment>
                <PluginSidebarMoreMenuItem
                    target="smartseo-ai-sidebar"
                >
                    {smartseoAiGutenberg.i18n.panelTitle}
                </PluginSidebarMoreMenuItem>
                <PluginSidebar
                    name="smartseo-ai-sidebar"
                    title={smartseoAiGutenberg.i18n.panelTitle}
                >
                    <div className="smartseo-ai-sidebar">
                        <Button
                            isPrimary
                            isBusy={isOptimizing}
                            disabled={isOptimizing}
                            onClick={optimizePost}
                            className="smartseo-ai-sidebar-button"
                        >
                            {isOptimizing ? (
                                <Fragment>
                                    <Spinner />
                                    {smartseoAiGutenberg.i18n.optimizing}
                                </Fragment>
                            ) : (
                                <Fragment>
                                    <span className="dashicons dashicons-superhero"></span>
                                    {smartseoAiGutenberg.i18n.optimizeButton}
                                </Fragment>
                            )}
                        </Button>

                        {notice && (
                            <Notice
                                status={notice.type}
                                isDismissible={true}
                                onRemove={() => setNotice(null)}
                                className="smartseo-ai-sidebar-notice"
                            >
                                {notice.message}
                            </Notice>
                        )}

                        {seoData.seo_score > 0 && (
                            <div className="smartseo-ai-sidebar-score">
                                <div className={`smartseo-ai-sidebar-score-value smartseo-ai-sidebar-score-${getScoreClass(seoData.seo_score)}`}>
                                    {seoData.seo_score}/100
                                </div>
                                <div className="smartseo-ai-sidebar-score-label">
                                    {smartseoAiGutenberg.i18n.seoScore}
                                </div>
                            </div>
                        )}

                        <PanelBody title={smartseoAiGutenberg.i18n.seoTitle} initialOpen={true}>
                            <TextControl
                                label={
                                    <Fragment>
                                        {smartseoAiGutenberg.i18n.seoTitle}
                                        <span className="smartseo-ai-sidebar-char-count">
                                            {seoData.seo_title.length}/60
                                        </span>
                                    </Fragment>
                                }
                                value={seoData.seo_title}
                                onChange={(value) => updateSeoData('seo_title', value)}
                                maxLength={60}
                            />

                            <TextareaControl
                                label={
                                    <Fragment>
                                        {smartseoAiGutenberg.i18n.metaDescription}
                                        <span className="smartseo-ai-sidebar-char-count">
                                            {seoData.meta_description.length}/160
                                        </span>
                                    </Fragment>
                                }
                                value={seoData.meta_description}
                                onChange={(value) => updateSeoData('meta_description', value)}
                                maxLength={160}
                            />

                            <TextControl
                                label={smartseoAiGutenberg.i18n.keywords}
                                value={seoData.keywords}
                                onChange={(value) => updateSeoData('keywords', value)}
                                help={__('Séparez les mots-clés par des virgules.', 'smartseo-ai')}
                            />

                            <div className="smartseo-ai-sidebar-field">
                                <label className="smartseo-ai-sidebar-field-label">
                                    {smartseoAiGutenberg.i18n.seoSlug}
                                </label>
                                <div style={{ display: 'flex' }}>
                                    <TextControl
                                        value={seoData.seo_slug}
                                        onChange={(value) => updateSeoData('seo_slug', value)}
                                        style={{ flexGrow: 1, marginRight: '8px' }}
                                    />
                                    <Button
                                        isSecondary
                                        onClick={applySlug}
                                    >
                                        {smartseoAiGutenberg.i18n.applySlug}
                                    </Button>
                                </div>
                            </div>
                        </PanelBody>

                        <PanelBody title={smartseoAiGutenberg.i18n.openGraph} initialOpen={false}>
                            <TextControl
                                label={smartseoAiGutenberg.i18n.ogTitle}
                                value={seoData.og_title}
                                onChange={(value) => updateSeoData('og_title', value)}
                            />

                            <TextareaControl
                                label={smartseoAiGutenberg.i18n.ogDescription}
                                value={seoData.og_description}
                                onChange={(value) => updateSeoData('og_description', value)}
                            />

                            <div className="smartseo-ai-sidebar-field">
                                <label className="smartseo-ai-sidebar-field-label">
                                    {smartseoAiGutenberg.i18n.ogImage}
                                </label>
                                <div style={{ display: 'flex' }}>
                                    <TextControl
                                        value={seoData.og_image}
                                        onChange={(value) => updateSeoData('og_image', value)}
                                        style={{ flexGrow: 1, marginRight: '8px' }}
                                    />
                                    <MediaUpload
                                        onSelect={(media) => updateSeoData('og_image', media.url)}
                                        allowedTypes={['image']}
                                        render={({ open }) => (
                                            <Button
                                                isSecondary
                                                onClick={open}
                                            >
                                                {smartseoAiGutenberg.i18n.selectImage}
                                            </Button>
                                        )}
                                    />
                                </div>
                            </div>
                        </PanelBody>

                        {seoData.seo_advice && (
                            <PanelBody title={smartseoAiGutenberg.i18n.seoAdvice} initialOpen={false}>
                                <div
                                    className="smartseo-ai-sidebar-advice"
                                    dangerouslySetInnerHTML={{ __html: seoData.seo_advice }}
                                />
                            </PanelBody>
                        )}
                    </div>
                </PluginSidebar>
            </Fragment>
        );
    };

    // Enregistrer le plugin
    registerPlugin('smartseo-ai', {
        render: SmartSEOAISidebar,
        icon: 'superhero'
    });

})(window.wp);
