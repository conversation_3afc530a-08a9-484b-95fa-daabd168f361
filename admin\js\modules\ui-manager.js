/**
 * Module de gestion de l'interface utilisateur
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    // Sous-modules
    const SidebarUI = window.SmartSEOAI.SidebarUI;
    const ModalUI = window.SmartSEOAI.ModalUI;
    const NotificationUI = window.SmartSEOAI.NotificationUI;
    const AnimationUI = window.SmartSEOAI.AnimationUI;

    /**
     * Gestionnaire de l'interface utilisateur
     */
    const UIManager = {
        /**
         * Initialise le gestionnaire de l'interface utilisateur
         */
        init: function() {
            console.log('UIManager: Initialisation du gestionnaire de l\'interface utilisateur');

            // Initialiser les sous-modules
            try {
                NotificationUI.init();
                AnimationUI.init();
                ModalUI.init();
                SidebarUI.init();

                // Ajouter l'interface de l'Assistant de Rédaction SEO
                this.addWritingAssistantInterface();

                console.log('UIManager: Initialisation terminée avec succès');
            } catch (error) {
                console.error('UIManager: Erreur lors de l\'initialisation', error);
            }
        },

        /**
         * Initialise la barre latérale pour Gutenberg
         */
        initGutenbergSidebar: function() {
            console.log('UIManager: Initialisation de la barre latérale pour Gutenberg');

            try {
                // Vérifier si SidebarUI est disponible
                if (SidebarUI && SidebarUI.addGutenbergSidebar) {
                    SidebarUI.addGutenbergSidebar();
                    console.log('UIManager: Barre latérale Gutenberg initialisée avec succès');
                } else {
                    console.error('UIManager: SidebarUI ou sa méthode addGutenbergSidebar n\'est pas disponible');
                }
            } catch (error) {
                console.error('UIManager: Erreur lors de l\'initialisation de la barre latérale Gutenberg', error);
            }
        },

        /**
         * Initialise la barre latérale pour l'éditeur classique
         */
        initClassicEditorSidebar: function() {
            console.log('UIManager: Initialisation de la barre latérale pour l\'éditeur classique');

            try {
                // Vérifier si SidebarUI est disponible
                if (SidebarUI && SidebarUI.addClassicEditorSidebar) {
                    SidebarUI.addClassicEditorSidebar();
                    console.log('UIManager: Barre latérale de l\'éditeur classique initialisée avec succès');
                } else {
                    console.error('UIManager: SidebarUI ou sa méthode addClassicEditorSidebar n\'est pas disponible');
                }
            } catch (error) {
                console.error('UIManager: Erreur lors de l\'initialisation de la barre latérale de l\'éditeur classique', error);
            }
        },

        /**
         * Ajoute l'interface de l'Assistant de Rédaction SEO
         */
        addWritingAssistantInterface: function() {
            // Déterminer le type d'éditeur (Gutenberg ou Classique)
            if (this.isGutenbergEditor()) {
                // Ajouter l'interface pour Gutenberg
                SidebarUI.addGutenbergSidebar();
            } else {
                // Ajouter l'interface pour l'éditeur classique
                SidebarUI.addClassicEditorSidebar();
            }
        },

        /**
         * Vérifie si l'éditeur Gutenberg est actif
         * @return {boolean} Vrai si Gutenberg est actif
         */
        isGutenbergEditor: function() {
            return typeof wp !== 'undefined' && wp.data && wp.data.select('core/editor');
        },

        /**
         * Récupère l'ID de l'article en cours d'édition
         * @return {number} ID de l'article
         */
        getPostId: function() {
            if (this.isGutenbergEditor()) {
                return wp.data.select('core/editor').getCurrentPostId();
            } else {
                return $('#post_ID').val();
            }
        },

        /**
         * Affiche un message de chargement
         * @param {string} message Message à afficher
         * @param {string} target  Cible du message
         */
        showLoading: function(message, target) {
            AnimationUI.showLoading(message, target);
        },

        /**
         * Masque le message de chargement
         * @param {string} target Cible du message
         */
        hideLoading: function(target) {
            AnimationUI.hideLoading(target);
        },

        /**
         * Affiche un message de succès
         * @param {string} message Message à afficher
         * @param {string} target  Cible du message
         */
        showSuccess: function(message, target) {
            NotificationUI.showSuccess(message, target);
        },

        /**
         * Affiche un message d'erreur
         * @param {string} message Message à afficher
         * @param {string} target  Cible du message
         */
        showError: function(message, target) {
            NotificationUI.showError(message, target);
        },

        /**
         * Affiche un message d'information
         * @param {string} message Message à afficher
         * @param {string} target  Cible du message
         */
        showInfo: function(message, target) {
            NotificationUI.showInfo(message, target);
        },

        /**
         * Affiche une modale
         * @param {string} title   Titre de la modale
         * @param {string} content Contenu de la modale
         * @param {Object} options Options de la modale
         */
        showModal: function(title, content, options) {
            ModalUI.showModal(title, content, options);
        },

        /**
         * Masque la modale
         */
        hideModal: function() {
            ModalUI.hideModal();
        },

        /**
         * Affiche les résultats de génération de contenu
         * @param {Object} results Résultats de génération
         * @param {string} type    Type de contenu
         */
        showContentResults: function(results, type) {
            // Déterminer le type d'éditeur
            if (this.isGutenbergEditor()) {
                // Afficher les résultats dans Gutenberg
                SidebarUI.showGutenbergContentResults(results, type);
            } else {
                // Afficher les résultats dans l'éditeur classique
                SidebarUI.showClassicEditorContentResults(results, type);
            }
        },

        /**
         * Affiche les résultats d'analyse des mots-clés
         * @param {Object} results Résultats d'analyse
         */
        showKeywordResults: function(results) {
            // Déterminer le type d'éditeur
            if (this.isGutenbergEditor()) {
                // Afficher les résultats dans Gutenberg
                SidebarUI.showGutenbergKeywordResults(results);
            } else {
                // Afficher les résultats dans l'éditeur classique
                SidebarUI.showClassicEditorKeywordResults(results);
            }
        },

        /**
         * Affiche les résultats d'analyse en temps réel
         * @param {Object} results Résultats d'analyse
         */
        showLiveAnalysisResults: function(results) {
            // Déterminer le type d'éditeur
            if (this.isGutenbergEditor()) {
                // Afficher les résultats dans Gutenberg
                SidebarUI.showGutenbergLiveAnalysisResults(results);
            } else {
                // Afficher les résultats dans l'éditeur classique
                SidebarUI.showClassicEditorLiveAnalysisResults(results);
            }
        },

        /**
         * Affiche les résultats de tendances
         * @param {Object} results Résultats de tendances
         */
        showTrendsResults: function(results) {
            // Déterminer le type d'éditeur
            if (this.isGutenbergEditor()) {
                // Afficher les résultats dans Gutenberg
                SidebarUI.showGutenbergTrendsResults(results);
            } else {
                // Afficher les résultats dans l'éditeur classique
                SidebarUI.showClassicEditorTrendsResults(results);
            }
        },

        /**
         * Applique une suggestion
         * @param {string} content Contenu à appliquer
         * @param {string} target  Cible de l'application
         */
        applySuggestion: function(content, target) {
            // Déterminer le type d'éditeur
            if (this.isGutenbergEditor()) {
                // Appliquer la suggestion dans Gutenberg
                this.applyGutenbergSuggestion(content, target);
            } else {
                // Appliquer la suggestion dans l'éditeur classique
                this.applyClassicEditorSuggestion(content, target);
            }
        },

        /**
         * Applique une suggestion dans Gutenberg
         * @param {string} content Contenu à appliquer
         * @param {string} target  Cible de l'application
         */
        applyGutenbergSuggestion: function(content, target) {
            // Utiliser l'API Gutenberg pour appliquer la suggestion
            const { dispatch, select } = wp.data;

            if (target === 'title') {
                // Appliquer au titre
                dispatch('core/editor').editPost({ title: content });
            } else if (target === 'meta_description') {
                // Appliquer à la meta description
                dispatch('core/editor').editPost({ meta: { smartseo_ai_meta_description: content } });
            } else if (target === 'content' || target === 'introduction' || target === 'conclusion' || target === 'paragraph') {
                // Appliquer au contenu
                const currentContent = select('core/editor').getEditedPostContent();

                if (target === 'content') {
                    // Remplacer tout le contenu
                    dispatch('core/editor').resetBlocks(wp.blocks.parse(content));
                } else {
                    // Insérer le contenu à la position du curseur
                    const selectedBlock = select('core/block-editor').getSelectedBlock();

                    if (selectedBlock) {
                        // Insérer après le bloc sélectionné
                        dispatch('core/block-editor').insertBlocks(
                            wp.blocks.parse(`<!-- wp:paragraph --><p>${content}</p><!-- /wp:paragraph -->`),
                            select('core/block-editor').getBlockIndex(selectedBlock.clientId) + 1
                        );
                    } else {
                        // Ajouter à la fin
                        dispatch('core/block-editor').insertBlocks(
                            wp.blocks.parse(`<!-- wp:paragraph --><p>${content}</p><!-- /wp:paragraph -->`)
                        );
                    }
                }
            } else if (target === 'h2_headings' || target === 'h3_headings') {
                // Appliquer les titres
                const headingLevel = target === 'h2_headings' ? 2 : 3;
                const headings = content.split('\n');
                const blocks = [];

                headings.forEach(heading => {
                    if (heading.trim()) {
                        blocks.push(
                            wp.blocks.createBlock('core/heading', {
                                content: heading.trim(),
                                level: headingLevel
                            })
                        );
                    }
                });

                if (blocks.length > 0) {
                    const selectedBlock = select('core/block-editor').getSelectedBlock();

                    if (selectedBlock) {
                        // Insérer après le bloc sélectionné
                        dispatch('core/block-editor').insertBlocks(
                            blocks,
                            select('core/block-editor').getBlockIndex(selectedBlock.clientId) + 1
                        );
                    } else {
                        // Ajouter à la fin
                        dispatch('core/block-editor').insertBlocks(blocks);
                    }
                }
            }

            // Afficher un message de succès
            this.showSuccess(smartseoAiWritingAssistant.i18n.success, target);
        },

        /**
         * Applique une suggestion dans l'éditeur classique
         * @param {string} content Contenu à appliquer
         * @param {string} target  Cible de l'application
         */
        applyClassicEditorSuggestion: function(content, target) {
            if (target === 'title') {
                // Appliquer au titre
                $('#title').val(content);
            } else if (target === 'meta_description') {
                // Appliquer à la meta description
                $('#smartseo_ai_meta_description').val(content);
            } else if (target === 'content') {
                // Remplacer tout le contenu
                if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                    tinyMCE.get('content').setContent(content);
                } else {
                    $('#content').val(content);
                }
            } else if (target === 'introduction' || target === 'conclusion' || target === 'paragraph') {
                // Insérer le contenu
                if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                    tinyMCE.get('content').execCommand('mceInsertContent', false, `<p>${content}</p>`);
                } else {
                    const textarea = $('#content');
                    const currentContent = textarea.val();
                    const cursorPos = textarea.prop('selectionStart');

                    // Insérer le contenu à la position du curseur
                    const newContent = currentContent.substring(0, cursorPos) +
                                      `<p>${content}</p>` +
                                      currentContent.substring(cursorPos);

                    textarea.val(newContent);
                }
            } else if (target === 'h2_headings' || target === 'h3_headings') {
                // Appliquer les titres
                const headingLevel = target === 'h2_headings' ? 2 : 3;
                const headings = content.split('\n');
                let headingsHtml = '';

                headings.forEach(heading => {
                    if (heading.trim()) {
                        headingsHtml += `<h${headingLevel}>${heading.trim()}</h${headingLevel}>`;
                    }
                });

                if (headingsHtml) {
                    if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                        tinyMCE.get('content').execCommand('mceInsertContent', false, headingsHtml);
                    } else {
                        const textarea = $('#content');
                        const currentContent = textarea.val();
                        const cursorPos = textarea.prop('selectionStart');

                        // Insérer les titres à la position du curseur
                        const newContent = currentContent.substring(0, cursorPos) +
                                          headingsHtml +
                                          currentContent.substring(cursorPos);

                        textarea.val(newContent);
                    }
                }
            }

            // Afficher un message de succès
            this.showSuccess(smartseoAiWritingAssistant.i18n.success, target);
        },

        /**
         * Copie une suggestion dans le presse-papier
         * @param {string} content Contenu à copier
         */
        copySuggestion: function(content) {
            // Créer un élément temporaire
            const tempElement = document.createElement('textarea');
            tempElement.value = content;
            document.body.appendChild(tempElement);

            // Sélectionner et copier le contenu
            tempElement.select();
            document.execCommand('copy');

            // Supprimer l'élément temporaire
            document.body.removeChild(tempElement);

            // Afficher un message de succès
            this.showSuccess(smartseoAiWritingAssistant.i18n.copied);
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.UIManager = UIManager;

})(jQuery);
