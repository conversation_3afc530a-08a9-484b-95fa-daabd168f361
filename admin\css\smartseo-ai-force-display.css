/**
 * Styles pour forcer l'affichage de l'interface
 *
 * @package SmartSEO_AI
 */

/* Styles pour forcer l'affichage dans l'éditeur classique */
#smartseo-ai-classic-sidebar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-top: 20px !important;
    border: 1px solid #ddd !important;
    background-color: #fff !important;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04) !important;
}

.smartseo-ai-classic-sidebar-header {
    padding: 8px 12px !important;
    border-bottom: 1px solid #ddd !important;
    background-color: #f5f5f5 !important;
}

.smartseo-ai-classic-sidebar-header h2 {
    margin: 0 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
}

.smartseo-ai-classic-sidebar-content {
    padding: 12px !important;
}

/* Styles pour forcer l'affichage dans l'éditeur Gutenberg */
.edit-post-sidebar .smartseo-ai-panel {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.edit-post-sidebar .smartseo-ai-tabs {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.edit-post-sidebar .smartseo-ai-tabs-nav {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.edit-post-sidebar .smartseo-ai-tab-nav {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.edit-post-sidebar .smartseo-ai-tab-panel.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Styles pour les onglets */
.smartseo-ai-tabs {
    margin-bottom: 20px !important;
}

.smartseo-ai-tabs-nav {
    display: flex !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
    border-bottom: 1px solid #ccc !important;
}

.smartseo-ai-tab-nav {
    padding: 8px 12px !important;
    margin-right: 5px !important;
    margin-bottom: -1px !important;
    cursor: pointer !important;
    border: 1px solid transparent !important;
    border-top-left-radius: 3px !important;
    border-top-right-radius: 3px !important;
    font-weight: 500 !important;
    font-size: 13px !important;
}

.smartseo-ai-tab-nav:hover {
    background-color: #f0f0f0 !important;
}

.smartseo-ai-tab-nav.active {
    border-color: #ccc !important;
    border-bottom-color: #fff !important;
    background-color: #fff !important;
}

.smartseo-ai-tab-panel {
    display: none !important;
    padding: 15px 0 !important;
}

.smartseo-ai-tab-panel.active {
    display: block !important;
}

/* Styles pour les boutons */
.smartseo-ai-button-group {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
    margin-bottom: 15px !important;
}

.smartseo-ai-button-group button {
    flex: 1 !important;
    min-width: 120px !important;
}

/* Styles pour les résultats */
.smartseo-ai-results-container {
    margin-top: 20px !important;
}

.smartseo-ai-loading {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
    text-align: center !important;
}

.smartseo-ai-results h4 {
    margin-top: 0 !important;
    margin-bottom: 15px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
}

/* Styles pour les notifications */
#smartseo-ai-notifications {
    position: fixed !important;
    top: 32px !important;
    right: 20px !important;
    z-index: 9999 !important;
    width: 300px !important;
}

.smartseo-ai-notification-container {
    margin-bottom: 10px !important;
}

.smartseo-ai-notification {
    display: flex !important;
    align-items: flex-start !important;
    margin-bottom: 10px !important;
    padding: 10px !important;
    border-radius: 3px !important;
    background-color: #fff !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.smartseo-ai-notification-success {
    border-left: 4px solid #46b450 !important;
}

.smartseo-ai-notification-error {
    border-left: 4px solid #dc3232 !important;
}

.smartseo-ai-notification-info {
    border-left: 4px solid #00a0d2 !important;
}

.smartseo-ai-notification-warning {
    border-left: 4px solid #ffb900 !important;
}

.smartseo-ai-notification-icon {
    margin-right: 10px !important;
}

.smartseo-ai-notification-message {
    flex: 1 !important;
    font-size: 13px !important;
}

.smartseo-ai-notification-close {
    cursor: pointer !important;
    font-size: 16px !important;
    line-height: 1 !important;
    color: #666 !important;
}

/* Styles pour les modales */
.smartseo-ai-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 9999 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.smartseo-ai-modal {
    position: relative !important;
    max-width: 90% !important;
    max-height: 90% !important;
    overflow: auto !important;
    background-color: #fff !important;
    border-radius: 3px !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3) !important;
}

.smartseo-ai-modal-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 15px !important;
    border-bottom: 1px solid #ddd !important;
}

.smartseo-ai-modal-title {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
}

.smartseo-ai-modal-close {
    cursor: pointer !important;
    font-size: 20px !important;
    line-height: 1 !important;
    color: #666 !important;
    background: none !important;
    border: none !important;
    padding: 0 !important;
}

.smartseo-ai-modal-body {
    padding: 15px !important;
    max-height: 500px !important;
    overflow-y: auto !important;
}

.smartseo-ai-modal-footer {
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
    padding: 15px !important;
    border-top: 1px solid #ddd !important;
}
