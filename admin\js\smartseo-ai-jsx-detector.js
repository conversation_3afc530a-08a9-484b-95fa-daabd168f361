/**
 * Script de détection de JSX pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Détecteur de JSX pour l'Assistant de Rédaction SEO
     */
    const SmartSEOAIJSXDetector = {
        /**
         * Initialise le détecteur
         */
        init: function() {
            console.log('SmartSEO AI JSX Detector: Initialisation');
            
            // Ajouter le bouton de détection
            this.addDetectorButton();
            
            // Vérifier les erreurs de script
            this.checkScriptErrors();
            
            console.log('SmartSEO AI JSX Detector: Initialisation terminée');
        },
        
        /**
         * Ajoute le bouton de détection
         */
        addDetectorButton: function() {
            console.log('SmartSEO AI JSX Detector: Ajout du bouton de détection');
            
            // Créer le bouton
            const $button = $('<div id="smartseo-ai-jsx-detector-button" style="position: fixed; bottom: 60px; right: 20px; background-color: #ff6b6b; color: white; padding: 10px; border-radius: 5px; cursor: pointer; z-index: 9999;">Détecter JSX</div>');
            
            // Ajouter le bouton au corps de la page
            $('body').append($button);
            
            // Ajouter l'écouteur d'événement
            $button.on('click', function() {
                SmartSEOAIJSXDetector.detectJSX();
            });
            
            console.log('SmartSEO AI JSX Detector: Bouton de détection ajouté');
        },
        
        /**
         * Vérifie les erreurs de script
         */
        checkScriptErrors: function() {
            console.log('SmartSEO AI JSX Detector: Vérification des erreurs de script');
            
            // Écouter les erreurs de script
            window.addEventListener('error', function(event) {
                console.error('SmartSEO AI JSX Detector: Erreur de script détectée', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    error: event.error
                });
                
                // Vérifier si l'erreur est liée à JSX
                if (event.message.includes('Unexpected token') || event.message.includes('JSX')) {
                    console.error('SmartSEO AI JSX Detector: Erreur JSX détectée dans ' + event.filename);
                    
                    // Ajouter l'erreur à la liste
                    SmartSEOAIJSXDetector.addErrorToList(event.filename, event.message, event.lineno, event.colno);
                }
            });
            
            console.log('SmartSEO AI JSX Detector: Vérification des erreurs de script terminée');
        },
        
        /**
         * Ajoute une erreur à la liste
         * @param {string} filename Nom du fichier
         * @param {string} message  Message d'erreur
         * @param {number} lineno   Numéro de ligne
         * @param {number} colno    Numéro de colonne
         */
        addErrorToList: function(filename, message, lineno, colno) {
            console.log('SmartSEO AI JSX Detector: Ajout d\'une erreur à la liste', filename);
            
            // Créer la liste si elle n'existe pas
            if (!this.errorList) {
                this.errorList = [];
            }
            
            // Ajouter l'erreur à la liste
            this.errorList.push({
                filename: filename,
                message: message,
                lineno: lineno,
                colno: colno
            });
            
            console.log('SmartSEO AI JSX Detector: Erreur ajoutée à la liste');
        },
        
        /**
         * Détecte les erreurs JSX
         */
        detectJSX: function() {
            console.log('SmartSEO AI JSX Detector: Détection des erreurs JSX');
            
            // Vérifier si la liste d'erreurs existe
            if (!this.errorList) {
                this.errorList = [];
            }
            
            // Afficher les erreurs
            if (this.errorList.length > 0) {
                console.log('SmartSEO AI JSX Detector: Erreurs JSX détectées', this.errorList);
                
                // Créer le message
                let message = 'Erreurs JSX détectées :\\n\\n';
                
                this.errorList.forEach(function(error) {
                    message += 'Fichier: ' + error.filename + '\\n';
                    message += 'Ligne: ' + error.lineno + ', Colonne: ' + error.colno + '\\n';
                    message += 'Message: ' + error.message + '\\n\\n';
                });
                
                // Afficher le message
                alert(message);
            } else {
                console.log('SmartSEO AI JSX Detector: Aucune erreur JSX détectée');
                
                // Afficher le message
                alert('Aucune erreur JSX détectée.');
            }
            
            console.log('SmartSEO AI JSX Detector: Détection des erreurs JSX terminée');
        },
        
        /**
         * Corrige les erreurs JSX
         */
        fixJSXErrors: function() {
            console.log('SmartSEO AI JSX Detector: Correction des erreurs JSX');
            
            // Afficher un message
            alert('Pour corriger les erreurs JSX, vous devez remplacer la syntaxe JSX par des appels à createElement().');
            
            console.log('SmartSEO AI JSX Detector: Correction des erreurs JSX terminée');
        }
    };
    
    // Initialiser le détecteur au chargement du document
    $(document).ready(function() {
        SmartSEOAIJSXDetector.init();
    });
    
    // Exposer le détecteur pour pouvoir le forcer
    window.SmartSEOAIJSXDetector = SmartSEOAIJSXDetector;

})(jQuery);
