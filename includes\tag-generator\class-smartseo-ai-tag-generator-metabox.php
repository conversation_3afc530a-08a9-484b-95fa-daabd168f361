<?php
/**
 * Classe pour la métabox du Générateur de Tags IA
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la métabox du Générateur de Tags IA
 */
class SmartSEO_AI_Tag_Generator_Metabox {

    /**
     * Initialise la métabox
     */
    public function init() {
        // Ajouter les hooks
        add_action( 'add_meta_boxes', array( $this, 'add_meta_box' ) );
        
        // Ajouter les hooks pour Gutenberg
        add_action( 'enqueue_block_editor_assets', array( $this, 'enqueue_block_editor_assets' ) );
    }

    /**
     * Ajoute la métabox
     */
    public function add_meta_box() {
        // Récupérer les types de contenu supportés
        $post_types = $this->get_supported_post_types();
        
        // Ajouter la métabox pour chaque type de contenu
        foreach ( $post_types as $post_type ) {
            add_meta_box(
                'smartseo_ai_tag_generator',
                __( 'Générateur de Tags IA', 'smartseo-ai' ),
                array( $this, 'render_meta_box' ),
                $post_type,
                'side',
                'default'
            );
        }
    }

    /**
     * Récupère les types de contenu supportés
     *
     * @return array Types de contenu supportés.
     */
    private function get_supported_post_types() {
        // Types de contenu par défaut
        $default_post_types = array( 'post', 'page' );
        
        // Ajouter WooCommerce si disponible
        if ( class_exists( 'WooCommerce' ) ) {
            $default_post_types[] = 'product';
        }
        
        // Filtrer les types de contenu
        $post_types = apply_filters( 'smartseo_ai_tag_generator_post_types', $default_post_types );
        
        return $post_types;
    }

    /**
     * Affiche la métabox
     *
     * @param WP_Post $post Article courant.
     */
    public function render_meta_box( $post ) {
        // Récupérer les tags existants
        $existing_tags = wp_get_post_tags( $post->ID, array( 'fields' => 'names' ) );
        
        // Afficher le template
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/partials/tag-generator/metabox.php';
    }

    /**
     * Enqueue les assets pour l'éditeur de blocs
     */
    public function enqueue_block_editor_assets() {
        // Récupérer les types de contenu supportés
        $post_types = $this->get_supported_post_types();
        
        // Récupérer le type de contenu courant
        $current_screen = get_current_screen();
        
        // Vérifier si nous sommes sur un type de contenu supporté
        if ( ! $current_screen || ! in_array( $current_screen->post_type, $post_types, true ) ) {
            return;
        }
        
        // Enregistrer le script
        wp_enqueue_script(
            'smartseo-ai-tag-generator-gutenberg',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/tag-generator/smartseo-ai-tag-generator-gutenberg.js',
            array( 'wp-plugins', 'wp-edit-post', 'wp-element', 'wp-components', 'wp-data', 'wp-i18n', 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );
        
        // Localiser le script
        wp_localize_script(
            'smartseo-ai-tag-generator-gutenberg',
            'smartseoAiTagGenerator',
            array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'smartseo_ai_tag_generator_nonce' ),
                'i18n' => array(
                    'title' => __( 'Générateur de Tags IA', 'smartseo-ai' ),
                    'generate' => __( 'Générer les Tags', 'smartseo-ai' ),
                    'apply' => __( 'Appliquer automatiquement', 'smartseo-ai' ),
                    'regenerate' => __( 'Régénérer les suggestions', 'smartseo-ai' ),
                    'loading' => __( 'Génération en cours...', 'smartseo-ai' ),
                    'error' => __( 'Une erreur s\'est produite lors de la génération des tags.', 'smartseo-ai' ),
                    'no_tags' => __( 'Aucun tag généré.', 'smartseo-ai' ),
                    'include_brands' => __( 'Inclure les marques et produits', 'smartseo-ai' ),
                    'include_regions' => __( 'Inclure les régions et lieux', 'smartseo-ai' ),
                    'optimal_combination' => __( 'Combinaison optimale', 'smartseo-ai' ),
                    'existing_tags' => __( 'Tags existants', 'smartseo-ai' ),
                    'generated_tags' => __( 'Tags générés', 'smartseo-ai' ),
                    'score' => __( 'Score', 'smartseo-ai' ),
                    'apply_success' => __( 'Tags appliqués avec succès.', 'smartseo-ai' ),
                    'apply_error' => __( 'Une erreur s\'est produite lors de l\'application des tags.', 'smartseo-ai' ),
                ),
            )
        );
        
        // Enregistrer le style
        wp_enqueue_style(
            'smartseo-ai-tag-generator-gutenberg',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/tag-generator/smartseo-ai-tag-generator-gutenberg.css',
            array(),
            SMARTSEO_AI_VERSION
        );
    }
}
