<?php
/**
 * Classe AJAX du Sitemap XML
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les requêtes AJAX du Sitemap XML
 */
class SmartSEO_AI_Sitemap_Ajax {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_Sitemap_Ajax
     */
    private static $instance = null;

    /**
     * Options du sitemap
     *
     * @var array
     */
    private $options;

    /**
     * Constructeur
     */
    private function __construct() {
        // Charger les options
        $this->options = get_option( 'smartseo_ai_sitemap_options', array() );

        // Ajouter les hooks
        $this->add_hooks();
    }

    /**
     * Récupère l'instance de la classe
     *
     * @return SmartSEO_AI_Sitemap_Ajax Instance de la classe.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Ajoute les hooks
     */
    private function add_hooks() {
        // Ajouter les actions AJAX - DÉSACTIVÉ (onglet sitemap retiré)
        // add_action( 'wp_ajax_smartseo_ai_regenerate_sitemap', array( $this, 'ajax_regenerate_sitemap' ) );
        // add_action( 'wp_ajax_smartseo_ai_validate_sitemap', array( $this, 'ajax_validate_sitemap' ) );
        // add_action( 'wp_ajax_smartseo_ai_analyze_sitemap', array( $this, 'ajax_analyze_sitemap' ) );

        // Ajouter les scripts - DÉSACTIVÉ (onglet sitemap retiré)
        // add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
    }

    /**
     * Enqueue les scripts
     *
     * @param string $hook Hook de la page.
     */
    public function enqueue_scripts( $hook ) {
        // Vérifier si nous sommes sur la page des paramètres
        if ( 'toplevel_page_smartseo-ai-settings' !== $hook && 'settings_page_smartseo-ai-settings' !== $hook ) {
            return;
        }

        // Enregistrer le script
        wp_enqueue_script(
            'smartseo-ai-sitemap-ajax',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/sitemap/smartseo-ai-sitemap-ajax.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Localiser le script
        wp_localize_script(
            'smartseo-ai-sitemap-ajax',
            'smartseoAiSitemap',
            array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'smartseo_ai_sitemap_nonce' ),
                'i18n'     => array(
                    'regenerating'      => __( 'Régénération du sitemap en cours...', 'smartseo-ai' ),
                    'regenerate_success' => __( 'Sitemap régénéré avec succès.', 'smartseo-ai' ),
                    'regenerate_error'   => __( 'Erreur lors de la régénération du sitemap.', 'smartseo-ai' ),
                    'validating'        => __( 'Validation du sitemap en cours...', 'smartseo-ai' ),
                    'validate_success'   => __( 'Sitemap valide.', 'smartseo-ai' ),
                    'validate_error'     => __( 'Erreur lors de la validation du sitemap.', 'smartseo-ai' ),
                    'analyzing'         => __( 'Analyse du sitemap en cours...', 'smartseo-ai' ),
                    'analyze_success'    => __( 'Analyse terminée.', 'smartseo-ai' ),
                    'analyze_error'      => __( 'Erreur lors de l\'analyse du sitemap.', 'smartseo-ai' ),
                ),
            )
        );
    }

    /**
     * Régénère le sitemap via AJAX
     */
    public function ajax_regenerate_sitemap() {
        // Vérifier le nonce
        check_ajax_referer( 'smartseo_ai_sitemap_nonce', 'nonce' );

        // Vérifier les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Instancier le générateur de sitemap
        $generator = new SmartSEO_AI_Sitemap_Generator( $this->options );

        // Générer le sitemap d'index
        $index_xml = $generator->generate_index_sitemap();

        // Générer les sitemaps spécifiques
        $post_xml = $generator->generate_sitemap( 'post' );
        $page_xml = $generator->generate_sitemap( 'page' );

        // Vérifier si les sitemaps ont été générés
        if ( empty( $index_xml ) || empty( $post_xml ) || empty( $page_xml ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur lors de la génération du sitemap.', 'smartseo-ai' ) ) );
        }

        // Vider le cache de réécriture
        flush_rewrite_rules();

        // Envoyer la réponse
        wp_send_json_success( array(
            'message' => __( 'Sitemap régénéré avec succès.', 'smartseo-ai' ),
            'url'     => home_url( 'sitemap.xml' ),
        ) );
    }

    /**
     * Valide le sitemap via AJAX
     */
    public function ajax_validate_sitemap() {
        // Vérifier le nonce
        check_ajax_referer( 'smartseo_ai_sitemap_nonce', 'nonce' );

        // Vérifier les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Instancier la classe du sitemap
        $sitemap = SmartSEO_AI_Sitemap::get_instance();

        // Valider le sitemap
        $result = $sitemap->validate_sitemap();

        // Vérifier le résultat
        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        // Envoyer la réponse
        wp_send_json_success( array(
            'message' => __( 'Sitemap valide.', 'smartseo-ai' ),
            'url'     => home_url( 'sitemap.xml' ),
        ) );
    }

    /**
     * Analyse le sitemap via AJAX
     */
    public function ajax_analyze_sitemap() {
        // Vérifier le nonce
        check_ajax_referer( 'smartseo_ai_sitemap_nonce', 'nonce' );

        // Vérifier les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Instancier la classe du sitemap
        $sitemap = SmartSEO_AI_Sitemap::get_instance();

        // Analyser le sitemap
        $suggestions = $sitemap->suggest_content();

        // Vérifier si des suggestions ont été trouvées
        if ( empty( $suggestions ) ) {
            wp_send_json_error( array( 'message' => __( 'Aucune suggestion trouvée.', 'smartseo-ai' ) ) );
        }

        // Envoyer la réponse
        wp_send_json_success( array(
            'message'     => __( 'Analyse terminée.', 'smartseo-ai' ),
            'suggestions' => $suggestions,
        ) );
    }
}
