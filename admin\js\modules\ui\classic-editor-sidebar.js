/**
 * Module de gestion de la barre latérale pour l'éditeur classique
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    // Sous-modules
    const ClassicContentPanel = window.SmartSEOAI.ClassicContentPanel;
    const ClassicKeywordPanel = window.SmartSEOAI.ClassicKeywordPanel;
    const ClassicAnalysisPanel = window.SmartSEOAI.ClassicAnalysisPanel;
    const ClassicTrendsPanel = window.SmartSEOAI.ClassicTrendsPanel;

    /**
     * Gestionnaire de la barre latérale pour l'éditeur classique
     */
    const ClassicEditorSidebar = {
        /**
         * Initialise le gestionnaire de la barre latérale pour l'éditeur classique
         */
        init: function() {
            // Initialiser les sous-modules
            ClassicContentPanel.init();
            ClassicKeywordPanel.init();
            ClassicAnalysisPanel.init();
            ClassicTrendsPanel.init();
            
            // Ajouter les écouteurs d'événements
            this.bindEvents();
        },

        /**
         * Ajoute les écouteurs d'événements
         */
        bindEvents: function() {
            // Écouter les changements dans l'éditeur classique
            $('#title, #content').on('input', this.debounce(function() {
                // Mettre à jour l'analyse en temps réel
                window.SmartSEOAI.LiveAnalyzer.scheduleAnalysis();
            }, 1000));
            
            // Écouter les changements dans l'éditeur TinyMCE
            if (typeof tinyMCE !== 'undefined') {
                $(document).on('tinymce-editor-init', function(event, editor) {
                    if (editor.id === 'content') {
                        editor.on('input change', this.debounce(function() {
                            // Mettre à jour l'analyse en temps réel
                            window.SmartSEOAI.LiveAnalyzer.scheduleAnalysis();
                        }, 1000));
                    }
                }.bind(this));
            }
        },

        /**
         * Crée une fonction debounce
         * @param {Function} func    Fonction à exécuter
         * @param {number}   wait    Délai d'attente en ms
         * @param {boolean}  immediate Exécuter immédiatement
         * @return {Function} Fonction debounce
         */
        debounce: function(func, wait, immediate) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },

        /**
         * Ajoute la barre latérale à l'éditeur classique
         */
        addSidebar: function() {
            // Vérifier si nous sommes sur une page d'édition
            if ($('#post').length === 0) {
                return;
            }
            
            // Créer la structure de la barre latérale
            const $sidebar = $('<div id="smartseo-ai-classic-sidebar" class="postbox"></div>');
            
            // Ajouter l'en-tête
            $sidebar.append('<div class="postbox-header">' +
                '<h2 class="hndle ui-sortable-handle">' +
                '<span>Assistant de Rédaction SEO</span>' +
                '</h2>' +
                '<div class="handle-actions hide-if-no-js">' +
                '<button type="button" class="handlediv" aria-expanded="true">' +
                '<span class="screen-reader-text">Afficher/Masquer le panneau</span>' +
                '<span class="toggle-indicator" aria-hidden="true"></span>' +
                '</button>' +
                '</div>' +
                '</div>');
            
            // Ajouter le contenu
            const $content = $('<div class="inside"></div>');
            
            // Ajouter les onglets
            const $tabs = $('<div class="smartseo-ai-tabs"></div>');
            $tabs.append('<ul class="smartseo-ai-tabs-nav">' +
                '<li class="smartseo-ai-tab-nav active" data-tab="content">Contenu</li>' +
                '<li class="smartseo-ai-tab-nav" data-tab="keywords">Mots-clés</li>' +
                '<li class="smartseo-ai-tab-nav" data-tab="analysis">Analyse</li>' +
                '<li class="smartseo-ai-tab-nav" data-tab="trends">Tendances</li>' +
                '</ul>');
            
            // Ajouter les panneaux
            const $panels = $('<div class="smartseo-ai-tabs-content"></div>');
            
            // Panneau de contenu
            const $contentPanel = $('<div id="smartseo-ai-tab-content" class="smartseo-ai-tab-panel active"></div>');
            $contentPanel.append(ClassicContentPanel.render());
            $panels.append($contentPanel);
            
            // Panneau de mots-clés
            const $keywordsPanel = $('<div id="smartseo-ai-tab-keywords" class="smartseo-ai-tab-panel"></div>');
            $keywordsPanel.append(ClassicKeywordPanel.render());
            $panels.append($keywordsPanel);
            
            // Panneau d'analyse
            const $analysisPanel = $('<div id="smartseo-ai-tab-analysis" class="smartseo-ai-tab-panel"></div>');
            $analysisPanel.append(ClassicAnalysisPanel.render());
            $panels.append($analysisPanel);
            
            // Panneau de tendances
            const $trendsPanel = $('<div id="smartseo-ai-tab-trends" class="smartseo-ai-tab-panel"></div>');
            $trendsPanel.append(ClassicTrendsPanel.render());
            $panels.append($trendsPanel);
            
            // Ajouter les onglets et les panneaux au contenu
            $content.append($tabs);
            $content.append($panels);
            
            // Ajouter le contenu à la barre latérale
            $sidebar.append($content);
            
            // Ajouter la barre latérale à la page
            $('#side-sortables').append($sidebar);
            
            // Ajouter les écouteurs d'événements pour les onglets
            $('.smartseo-ai-tab-nav').on('click', function() {
                const tab = $(this).data('tab');
                
                // Activer l'onglet
                $('.smartseo-ai-tab-nav').removeClass('active');
                $(this).addClass('active');
                
                // Afficher le panneau correspondant
                $('.smartseo-ai-tab-panel').removeClass('active');
                $('#smartseo-ai-tab-' + tab).addClass('active');
            });
            
            // Ajouter les écouteurs d'événements pour le bouton de bascule
            $('.handlediv').on('click', function() {
                const $inside = $(this).closest('.postbox').find('.inside');
                $inside.toggle();
                
                // Mettre à jour l'attribut aria-expanded
                const isExpanded = $inside.is(':visible');
                $(this).attr('aria-expanded', isExpanded);
            });
        },

        /**
         * Affiche les résultats de génération de contenu
         * @param {Object} results Résultats de génération
         * @param {string} type    Type de contenu
         */
        showContentResults: function(results, type) {
            ClassicContentPanel.showResults(results, type);
        },

        /**
         * Affiche les résultats d'analyse des mots-clés
         * @param {Object} results Résultats d'analyse
         */
        showKeywordResults: function(results) {
            ClassicKeywordPanel.showResults(results);
        },

        /**
         * Affiche les résultats d'analyse en temps réel
         * @param {Object} results Résultats d'analyse
         */
        showLiveAnalysisResults: function(results) {
            ClassicAnalysisPanel.showResults(results);
        },

        /**
         * Affiche les résultats de tendances
         * @param {Object} results Résultats de tendances
         */
        showTrendsResults: function(results) {
            ClassicTrendsPanel.showResults(results);
        },

        /**
         * Applique une suggestion
         * @param {string} content Contenu à appliquer
         * @param {string} target  Cible de l'application
         */
        applySuggestion: function(content, target) {
            if (target === 'title') {
                // Appliquer au titre
                $('#title').val(content);
            } else if (target === 'meta_description') {
                // Appliquer à la meta description
                $('#smartseo_ai_meta_description').val(content);
            } else if (target === 'content' || target === 'full_article') {
                // Remplacer tout le contenu
                if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                    tinyMCE.get('content').setContent(content);
                } else {
                    $('#content').val(content);
                }
            } else if (target === 'introduction' || target === 'conclusion' || target === 'paragraph') {
                // Insérer le contenu
                if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                    tinyMCE.get('content').execCommand('mceInsertContent', false, `<p>${content}</p>`);
                } else {
                    const textarea = $('#content');
                    const currentContent = textarea.val();
                    const cursorPos = textarea.prop('selectionStart');
                    
                    // Insérer le contenu à la position du curseur
                    const newContent = currentContent.substring(0, cursorPos) + 
                                      `<p>${content}</p>` + 
                                      currentContent.substring(cursorPos);
                    
                    textarea.val(newContent);
                }
            } else if (target === 'h2_headings' || target === 'h3_headings') {
                // Appliquer les titres
                const headingLevel = target === 'h2_headings' ? 2 : 3;
                const headings = content.split('\n');
                let headingsHtml = '';
                
                headings.forEach(heading => {
                    if (heading.trim()) {
                        headingsHtml += `<h${headingLevel}>${heading.trim()}</h${headingLevel}>`;
                    }
                });
                
                if (headingsHtml) {
                    if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                        tinyMCE.get('content').execCommand('mceInsertContent', false, headingsHtml);
                    } else {
                        const textarea = $('#content');
                        const currentContent = textarea.val();
                        const cursorPos = textarea.prop('selectionStart');
                        
                        // Insérer les titres à la position du curseur
                        const newContent = currentContent.substring(0, cursorPos) + 
                                          headingsHtml + 
                                          currentContent.substring(cursorPos);
                        
                        textarea.val(newContent);
                    }
                }
            }
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.ClassicEditorSidebar = ClassicEditorSidebar;

})(jQuery);
