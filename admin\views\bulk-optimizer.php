<?php
/**
 * Vue de l'optimisation en masse
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Utiliser les statistiques globales fournies par le contrôleur
$total_posts = $stats['total_posts'];
$optimized_posts = $stats['optimized_posts'];
$not_optimized_posts = $stats['not_optimized_posts'];
$optimization_rate = $stats['optimization_rate'];

// Paramètres de pagination
$current_page = isset( $_GET['paged'] ) ? absint( $_GET['paged'] ) : 1;
$per_page = isset( $_GET['per_page'] ) ? absint( $_GET['per_page'] ) : 20;

// Options de nombre d'articles par page
$per_page_options = array( 10, 20, 50, 100 );
?>

<div class="wrap">
    <h1><?php _e( 'SmartSEO AI - Optimisation en masse', 'smartseo-ai' ); ?></h1>

    <div class="smartseo-ai-container">
        <div class="smartseo-ai-header">
            <h1><?php _e( 'Optimisation en masse des articles', 'smartseo-ai' ); ?></h1>
            <span class="smartseo-ai-version">v<?php echo SMARTSEO_AI_VERSION; ?></span>
        </div>

        <div class="smartseo-ai-content">
            <div class="smartseo-ai-dashboard-stats">
                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Articles totaux', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo $total_posts; ?></div>
                </div>

                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Articles optimisés', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo $optimized_posts; ?></div>
                    <div class="stat-description"><?php echo $optimization_rate; ?>% <?php _e( 'du total', 'smartseo-ai' ); ?></div>
                </div>

                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Articles non optimisés', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo $not_optimized_posts; ?></div>
                </div>
            </div>

            <div class="smartseo-ai-bulk-actions">
                <button id="smartseo-ai-optimize-all" class="button button-primary button-hero">
                    <span class="dashicons dashicons-superhero"></span>
                    <?php _e( 'Optimiser tous les articles', 'smartseo-ai' ); ?>
                </button>

                <button id="smartseo-ai-stop-optimization" class="button button-secondary button-hero" style="display: none;">
                    <span class="dashicons dashicons-no-alt"></span>
                    <?php _e( 'Arrêter l\'optimisation', 'smartseo-ai' ); ?>
                </button>
            </div>

            <div id="smartseo-ai-progress-container" style="display: none;">
                <h3><?php _e( 'Progression de l\'optimisation', 'smartseo-ai' ); ?></h3>
                <div class="smartseo-ai-progress-bar-container">
                    <div id="smartseo-ai-progress-bar" class="smartseo-ai-progress-bar"></div>
                </div>
                <div id="smartseo-ai-progress-text" class="smartseo-ai-progress-text">0%</div>
                <div id="smartseo-ai-progress-status" class="smartseo-ai-progress-status"></div>
            </div>

            <h2><?php _e( 'Liste des articles', 'smartseo-ai' ); ?></h2>

            <div class="smartseo-ai-table-controls">
                <div class="smartseo-ai-pagination-info">
                    <?php
                    printf(
                        __( 'Affichage de %1$s à %2$s sur %3$s articles', 'smartseo-ai' ),
                        ( $current_page - 1 ) * $per_page + 1,
                        min( $current_page * $per_page, $total_posts ),
                        $total_posts
                    );
                    ?>
                </div>

                <div class="smartseo-ai-per-page-selector">
                    <label for="smartseo-ai-per-page"><?php _e( 'Articles par page:', 'smartseo-ai' ); ?></label>
                    <select id="smartseo-ai-per-page" name="per_page" onchange="window.location.href='<?php echo esc_url( add_query_arg( array( 'paged' => 1 ), remove_query_arg( 'per_page' ) ) ); ?>&per_page=' + this.value">
                        <?php foreach ( $per_page_options as $option ) : ?>
                            <option value="<?php echo esc_attr( $option ); ?>" <?php selected( $per_page, $option ); ?>>
                                <?php echo esc_html( $option ); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <!-- Loader animé -->
            <div id="smartseo-ai-table-loader" class="smartseo-ai-table-loader" style="display: none;">
                <div class="smartseo-ai-loader-spinner">
                    <div class="smartseo-ai-loader-bounce1"></div>
                    <div class="smartseo-ai-loader-bounce2"></div>
                    <div class="smartseo-ai-loader-bounce3"></div>
                </div>
                <div class="smartseo-ai-loader-text"><?php _e( 'Chargement des articles...', 'smartseo-ai' ); ?></div>
            </div>

            <table id="smartseo-ai-posts-table" class="smartseo-ai-posts-table">
                <thead>
                    <tr>
                        <th class="column-id"><?php _e( 'ID', 'smartseo-ai' ); ?></th>
                        <th><?php _e( 'Titre', 'smartseo-ai' ); ?></th>
                        <th><?php _e( 'Type', 'smartseo-ai' ); ?></th>
                        <th><?php _e( 'Date', 'smartseo-ai' ); ?></th>
                        <th class="column-score"><?php _e( 'Score SEO', 'smartseo-ai' ); ?></th>
                        <th class="column-status"><?php _e( 'Statut', 'smartseo-ai' ); ?></th>
                        <th class="column-actions"><?php _e( 'Actions', 'smartseo-ai' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( empty( $posts ) ) : ?>
                        <tr>
                            <td colspan="7"><?php _e( 'Aucun article trouvé.', 'smartseo-ai' ); ?></td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ( $posts as $post ) : ?>
                            <tr id="post-row-<?php echo esc_attr( $post['id'] ); ?>" data-post-id="<?php echo esc_attr( $post['id'] ); ?>" class="post-row <?php echo esc_attr( $post['status'] ); ?>">
                                <td class="column-id"><?php echo esc_html( $post['id'] ); ?></td>
                                <td>
                                    <a href="<?php echo esc_url( $post['permalink'] ); ?>" target="_blank">
                                        <?php echo esc_html( $post['title'] ); ?>
                                    </a>
                                </td>
                                <td><?php echo esc_html( $post['post_type'] ); ?></td>
                                <td><?php echo esc_html( $post['date'] ); ?></td>
                                <td class="column-score">
                                    <?php if ( $post['has_score'] ) : ?>
                                        <div class="smartseo-ai-score-indicator smartseo-ai-score-<?php echo esc_attr( $this->get_score_class( $post['seo_score'] ) ); ?>">
                                            <?php echo esc_html( $post['seo_score'] ); ?>/100
                                        </div>
                                    <?php else : ?>
                                        <div class="smartseo-ai-score-indicator smartseo-ai-score-none">
                                            <?php _e( 'Non optimisé', 'smartseo-ai' ); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="column-status">
                                    <div class="smartseo-ai-status-indicator smartseo-ai-status-<?php echo esc_attr( $post['status'] ); ?>">
                                        <?php
                                        if ( $post['status'] === 'optimized' ) {
                                            _e( 'Optimisé', 'smartseo-ai' );
                                        } elseif ( $post['status'] === 'in-progress' ) {
                                            _e( 'En cours...', 'smartseo-ai' );
                                        } elseif ( $post['status'] === 'error' ) {
                                            _e( 'Erreur', 'smartseo-ai' );
                                        } else {
                                            _e( 'Non optimisé', 'smartseo-ai' );
                                        }
                                        ?>
                                    </div>
                                </td>
                                <td class="column-actions">
                                    <button type="button" class="button button-small optimize-single-post" data-post-id="<?php echo esc_attr( $post['id'] ); ?>">
                                        <span class="dashicons dashicons-superhero" style="font-size: 16px; vertical-align: text-bottom;"></span>
                                        <?php _e( 'Optimiser', 'smartseo-ai' ); ?>
                                    </button>
                                    <a href="<?php echo esc_url( $post['edit_link'] ); ?>" class="button button-small">
                                        <span class="dashicons dashicons-edit" style="font-size: 16px; vertical-align: text-bottom;"></span>
                                        <?php _e( 'Éditer', 'smartseo-ai' ); ?>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>

            <!-- Pagination -->
            <?php if ( $total_pages > 1 ) : ?>
                <div class="smartseo-ai-pagination">
                    <?php
                    // Construire les liens de pagination
                    $pagination_links = paginate_links( array(
                        'base'      => add_query_arg( 'paged', '%#%' ),
                        'format'    => '',
                        'prev_text' => __( '&laquo; Précédent', 'smartseo-ai' ),
                        'next_text' => __( 'Suivant &raquo;', 'smartseo-ai' ),
                        'total'     => $total_pages,
                        'current'   => $current_page,
                        'type'      => 'array',
                    ) );

                    if ( ! empty( $pagination_links ) ) :
                    ?>
                        <ul class="smartseo-ai-pagination-links">
                            <?php foreach ( $pagination_links as $link ) : ?>
                                <li><?php echo $link; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Styles spécifiques à la page d'optimisation en masse */
.smartseo-ai-bulk-actions {
    margin: 20px 0;
    display: flex;
    gap: 10px;
}

/* Styles pour les contrôles de table */
.smartseo-ai-table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.smartseo-ai-pagination-info {
    font-style: italic;
    color: #666;
}

.smartseo-ai-per-page-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.smartseo-ai-per-page-selector select {
    min-width: 70px;
}

.smartseo-ai-progress-bar-container {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    margin: 10px 0;
    overflow: hidden;
}

.smartseo-ai-progress-bar {
    height: 100%;
    width: 0;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.smartseo-ai-progress-text {
    text-align: center;
    font-weight: bold;
    margin-bottom: 10px;
}

.smartseo-ai-progress-status {
    text-align: center;
    font-style: italic;
    margin-bottom: 20px;
}

.smartseo-ai-status-indicator {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
    text-align: center;
    min-width: 100px;
}

.smartseo-ai-status-optimized {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.smartseo-ai-status-not-optimized {
    background-color: #f5f5f5;
    color: #757575;
}

.smartseo-ai-status-in-progress {
    background-color: #e3f2fd;
    color: #1976d2;
    animation: pulse 1.5s infinite;
}

.smartseo-ai-status-error {
    background-color: #ffebee;
    color: #c62828;
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.post-row.in-progress {
    background-color: rgba(33, 150, 243, 0.1);
}

.post-row.optimized {
    background-color: rgba(76, 175, 80, 0.05);
}

.post-row.error {
    background-color: rgba(244, 67, 54, 0.05);
}

.column-id {
    width: 60px;
}

.column-score {
    width: 120px;
}

.column-status {
    width: 120px;
}

.column-actions {
    width: 180px;
}

/* Animation de chargement */
.optimize-single-post.loading,
#smartseo-ai-optimize-all.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.optimize-single-post.loading::after,
#smartseo-ai-optimize-all.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    margin-left: -8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: smartseo-ai-spin 1s infinite linear;
}

@keyframes smartseo-ai-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles pour le loader animé */
.smartseo-ai-table-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 5px;
    margin-bottom: 20px;
}

.smartseo-ai-loader-spinner {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.smartseo-ai-loader-bounce1,
.smartseo-ai-loader-bounce2,
.smartseo-ai-loader-bounce3 {
    width: 12px;
    height: 12px;
    background-color: #6a11cb;
    border-radius: 100%;
    display: inline-block;
    margin: 0 3px;
    animation: smartseo-ai-bounce 1.4s infinite ease-in-out both;
}

.smartseo-ai-loader-bounce1 {
    animation-delay: -0.32s;
}

.smartseo-ai-loader-bounce2 {
    animation-delay: -0.16s;
}

@keyframes smartseo-ai-bounce {
    0%, 80%, 100% {
        transform: scale(0);
    } 40% {
        transform: scale(1.0);
    }
}

.smartseo-ai-loader-text {
    font-style: italic;
    color: #666;
}

/* Styles pour la pagination */
.smartseo-ai-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.smartseo-ai-pagination-links {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 5px;
}

.smartseo-ai-pagination-links li {
    margin: 0;
}

.smartseo-ai-pagination-links a,
.smartseo-ai-pagination-links span {
    display: inline-block;
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    text-decoration: none;
    color: #2271b1;
    background: #f7f7f7;
    cursor: pointer;
    transition: all 0.2s ease;
}

.smartseo-ai-pagination-links a:hover {
    background: #e5e5e5;
    border-color: #999;
}

.smartseo-ai-pagination-links .current {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
    font-weight: bold;
}
</style>
