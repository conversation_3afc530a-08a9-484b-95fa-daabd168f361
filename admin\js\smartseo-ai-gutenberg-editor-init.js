/**
 * Script d'initialisation pour l'éditeur Gutenberg
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Initialisation de l'Assistant de Rédaction SEO pour l'éditeur Gutenberg
     */
    const SmartSEOAIGutenbergEditorInit = {
        /**
         * Initialise l'Assistant de Rédaction SEO
         */
        init: function() {
            console.log('SmartSEO AI Gutenberg Editor Init: Initialisation');
            
            // Vérifier si nous sommes dans l'éditeur Gutenberg
            if (!this.isGutenbergEditor()) {
                console.log('SmartSEO AI Gutenberg Editor Init: Pas dans l\'éditeur Gutenberg');
                return;
            }
            
            // Attendre que l'éditeur Gutenberg soit prêt
            this.waitForGutenberg();
            
            console.log('SmartSEO AI Gutenberg Editor Init: Initialisation terminée');
        },
        
        /**
         * Vérifie si nous sommes dans l'éditeur Gutenberg
         * @return {boolean} Vrai si nous sommes dans l'éditeur Gutenberg
         */
        isGutenbergEditor: function() {
            return typeof wp !== 'undefined' && wp.data && wp.data.select('core/editor');
        },
        
        /**
         * Attend que l'éditeur Gutenberg soit prêt
         */
        waitForGutenberg: function() {
            console.log('SmartSEO AI Gutenberg Editor Init: Attente de l\'éditeur Gutenberg');
            
            // Vérifier si l'éditeur est prêt
            if (wp.data && wp.data.select('core/editor') && wp.data.select('core/editor').getCurrentPostId()) {
                // L'éditeur est prêt, initialiser la barre latérale
                this.initSidebar();
            } else {
                // L'éditeur n'est pas prêt, attendre et réessayer
                setTimeout(function() {
                    SmartSEOAIGutenbergEditorInit.waitForGutenberg();
                }, 500);
            }
        },
        
        /**
         * Initialise la barre latérale
         */
        initSidebar: function() {
            console.log('SmartSEO AI Gutenberg Editor Init: Initialisation de la barre latérale');
            
            // Vérifier si le namespace global est disponible
            if (!window.SmartSEOAI || !window.SmartSEOAI.UIManager) {
                console.error('SmartSEO AI Gutenberg Editor Init: Le namespace global n\'est pas disponible');
                return;
            }
            
            // Initialiser la barre latérale
            if (window.SmartSEOAI.UIManager.initGutenbergSidebar) {
                window.SmartSEOAI.UIManager.initGutenbergSidebar();
                console.log('SmartSEO AI Gutenberg Editor Init: Barre latérale initialisée avec succès');
            } else {
                console.error('SmartSEO AI Gutenberg Editor Init: La méthode initGutenbergSidebar n\'est pas disponible');
            }
        }
    };
    
    // Initialiser l'Assistant de Rédaction SEO au chargement du document
    $(document).ready(function() {
        SmartSEOAIGutenbergEditorInit.init();
    });

})(jQuery);
