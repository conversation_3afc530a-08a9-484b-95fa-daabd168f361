/**
 * Script de test pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Test de l'Assistant de Rédaction SEO
     */
    const SmartSEOAITest = {
        /**
         * Initialise les tests
         */
        init: function() {
            console.log('SmartSEO AI Test: Initialisation des tests');
            
            // Ajouter le bouton de test
            this.addTestButton();
            
            console.log('SmartSEO AI Test: Initialisation terminée');
        },
        
        /**
         * Ajoute le bouton de test
         */
        addTestButton: function() {
            console.log('SmartSEO AI Test: Ajout du bouton de test');
            
            // Créer le bouton
            const $button = $('<div id="smartseo-ai-test-button" style="position: fixed; bottom: 20px; left: 20px; background-color: #0073aa; color: white; padding: 10px; border-radius: 5px; cursor: pointer; z-index: 9999;">SmartSEO AI Test</div>');
            
            // Ajouter le bouton au corps de la page
            $('body').append($button);
            
            // Ajouter l'écouteur d'événement
            $button.on('click', function() {
                SmartSEOAITest.runTests();
            });
            
            console.log('SmartSEO AI Test: Bouton de test ajouté');
        },
        
        /**
         * Exécute les tests
         */
        runTests: function() {
            console.log('SmartSEO AI Test: Exécution des tests');
            
            // Tester le namespace global
            this.testNamespace();
            
            // Tester les modules
            this.testModules();
            
            // Tester l'interface
            this.testInterface();
            
            // Tester les fonctionnalités
            this.testFunctionalities();
            
            // Afficher un message
            alert('Tests terminés. Consultez la console pour les résultats.');
        },
        
        /**
         * Teste le namespace global
         */
        testNamespace: function() {
            console.log('SmartSEO AI Test: Test du namespace global');
            
            // Vérifier si le namespace global est disponible
            if (window.SmartSEOAI) {
                console.log('SmartSEO AI Test: Le namespace global est disponible');
                
                // Vérifier les modules
                console.log('SmartSEO AI Test: Modules disponibles', Object.keys(window.SmartSEOAI));
            } else {
                console.error('SmartSEO AI Test: Le namespace global n\'est pas disponible');
            }
        },
        
        /**
         * Teste les modules
         */
        testModules: function() {
            console.log('SmartSEO AI Test: Test des modules');
            
            // Vérifier si le namespace global est disponible
            if (window.SmartSEOAI) {
                // Tester UIManager
                this.testModule('UIManager');
                
                // Tester ContentGenerator
                this.testModule('ContentGenerator');
                
                // Tester KeywordAnalyzer
                this.testModule('KeywordAnalyzer');
                
                // Tester LiveAnalyzer
                this.testModule('LiveAnalyzer');
                
                // Tester TrendsManager
                this.testModule('TrendsManager');
            } else {
                console.error('SmartSEO AI Test: Le namespace global n\'est pas disponible');
            }
        },
        
        /**
         * Teste un module
         * @param {string} moduleName Nom du module
         */
        testModule: function(moduleName) {
            console.log('SmartSEO AI Test: Test du module ' + moduleName);
            
            // Vérifier si le module est disponible
            if (window.SmartSEOAI && window.SmartSEOAI[moduleName]) {
                console.log('SmartSEO AI Test: Le module ' + moduleName + ' est disponible');
                
                // Vérifier si le module a une méthode init
                if (window.SmartSEOAI[moduleName].init) {
                    console.log('SmartSEO AI Test: Le module ' + moduleName + ' a une méthode init');
                } else {
                    console.warn('SmartSEO AI Test: Le module ' + moduleName + ' n\'a pas de méthode init');
                }
                
                // Vérifier les méthodes du module
                console.log('SmartSEO AI Test: Méthodes du module ' + moduleName, Object.keys(window.SmartSEOAI[moduleName]));
            } else {
                console.error('SmartSEO AI Test: Le module ' + moduleName + ' n\'est pas disponible');
            }
        },
        
        /**
         * Teste l'interface
         */
        testInterface: function() {
            console.log('SmartSEO AI Test: Test de l\'interface');
            
            // Vérifier si nous sommes dans l'éditeur Gutenberg
            if (window.SmartSEOAI && window.SmartSEOAI.UIManager && window.SmartSEOAI.UIManager.isGutenbergEditor) {
                const isGutenberg = window.SmartSEOAI.UIManager.isGutenbergEditor();
                console.log('SmartSEO AI Test: Éditeur Gutenberg détecté: ' + isGutenberg);
                
                // Vérifier les éléments de l'interface Gutenberg
                if (isGutenberg) {
                    console.log('SmartSEO AI Test: Barre latérale Gutenberg', $('.edit-post-sidebar').length);
                    console.log('SmartSEO AI Test: Panneau de contenu Gutenberg', $('.smartseo-ai-panel').length);
                }
            } else {
                console.log('SmartSEO AI Test: Éditeur classique détecté');
                
                // Vérifier les éléments de l'interface classique
                console.log('SmartSEO AI Test: Barre latérale classique', $('#smartseo-ai-classic-sidebar').length);
                console.log('SmartSEO AI Test: Métabox', $('#smartseo-ai-writing-assistant').length);
            }
        },
        
        /**
         * Teste les fonctionnalités
         */
        testFunctionalities: function() {
            console.log('SmartSEO AI Test: Test des fonctionnalités');
            
            // Tester les notifications
            this.testNotifications();
            
            // Tester les modales
            this.testModals();
            
            // Tester les animations
            this.testAnimations();
        },
        
        /**
         * Teste les notifications
         */
        testNotifications: function() {
            console.log('SmartSEO AI Test: Test des notifications');
            
            // Vérifier si le module NotificationUI est disponible
            if (window.SmartSEOAI && window.SmartSEOAI.NotificationUI) {
                console.log('SmartSEO AI Test: Le module NotificationUI est disponible');
                
                // Afficher une notification de succès
                window.SmartSEOAI.NotificationUI.showSuccess('Test de notification de succès');
                
                // Afficher une notification d'erreur
                setTimeout(function() {
                    window.SmartSEOAI.NotificationUI.showError('Test de notification d\'erreur');
                }, 1000);
                
                // Afficher une notification d'information
                setTimeout(function() {
                    window.SmartSEOAI.NotificationUI.showInfo('Test de notification d\'information');
                }, 2000);
                
                // Afficher une notification d'avertissement
                setTimeout(function() {
                    window.SmartSEOAI.NotificationUI.showWarning('Test de notification d\'avertissement');
                }, 3000);
            } else {
                console.error('SmartSEO AI Test: Le module NotificationUI n\'est pas disponible');
            }
        },
        
        /**
         * Teste les modales
         */
        testModals: function() {
            console.log('SmartSEO AI Test: Test des modales');
            
            // Vérifier si le module ModalUI est disponible
            if (window.SmartSEOAI && window.SmartSEOAI.ModalUI) {
                console.log('SmartSEO AI Test: Le module ModalUI est disponible');
                
                // Afficher une modale
                setTimeout(function() {
                    window.SmartSEOAI.ModalUI.showModal(
                        'Test de modale',
                        '<p>Ceci est un test de modale.</p>',
                        {
                            buttons: [
                                {
                                    text: 'Fermer',
                                    isPrimary: true,
                                    onClick: function() {
                                        window.SmartSEOAI.ModalUI.closeModal();
                                    }
                                }
                            ]
                        }
                    );
                }, 4000);
            } else {
                console.error('SmartSEO AI Test: Le module ModalUI n\'est pas disponible');
            }
        },
        
        /**
         * Teste les animations
         */
        testAnimations: function() {
            console.log('SmartSEO AI Test: Test des animations');
            
            // Vérifier si le module AnimationUI est disponible
            if (window.SmartSEOAI && window.SmartSEOAI.AnimationUI) {
                console.log('SmartSEO AI Test: Le module AnimationUI est disponible');
                
                // Afficher un chargement
                setTimeout(function() {
                    window.SmartSEOAI.AnimationUI.showLoading('Test de chargement');
                    
                    // Masquer le chargement après 2 secondes
                    setTimeout(function() {
                        window.SmartSEOAI.AnimationUI.hideLoading();
                    }, 2000);
                }, 6000);
            } else {
                console.error('SmartSEO AI Test: Le module AnimationUI n\'est pas disponible');
            }
        }
    };
    
    // Initialiser les tests au chargement du document
    $(document).ready(function() {
        SmartSEOAITest.init();
    });
    
    // Exposer les tests pour pouvoir les forcer
    window.SmartSEOAITest = SmartSEOAITest;

})(jQuery);
