<?php
/**
 * Classe pour l'analyse des mots-clés
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'analyse des mots-clés
 */
class SmartSEO_AI_Keyword_Analyzer {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     *
     * @param SmartSEO_AI_API|SmartSEO_AI_Gemini $ai_api Instance de l'API IA.
     */
    public function __construct( $ai_api ) {
        $this->ai_api = $ai_api;
    }

    /**
     * Analyse les mots-clés d'un contenu
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @param string $keyword Mot-clé principal.
     * @return array|WP_Error Résultats de l'analyse ou erreur.
     */
    public function analyze( $post_id, $content, $keyword = '' ) {
        // Si le post_id est fourni, récupérer les informations de l'article
        if ( $post_id > 0 ) {
            $post = get_post( $post_id );
            if ( $post ) {
                $title = $post->post_title;
                if ( empty( $content ) ) {
                    $content = $post->post_content;
                }
                $excerpt = $post->post_excerpt;
            }
        }

        // Si le mot-clé n'est pas fourni, essayer de le récupérer des métadonnées
        if ( empty( $keyword ) && $post_id > 0 ) {
            $keyword = get_post_meta( $post_id, 'smartseo_ai_focus_keyword', true );
            if ( empty( $keyword ) ) {
                $keyword = get_post_meta( $post_id, '_yoast_wpseo_focuskw', true );
            }
        }

        // Limiter la taille du contenu pour l'API
        $content = substr( strip_tags( $content ), 0, 5000 );

        // Générer le prompt pour l'IA
        $prompt = $this->build_prompt(
            "Analyse ce contenu et suggère des mots-clés stratégiques pour le SEO. " .
            ( ! empty( $keyword ) ? "Le mot-clé principal actuel est : $keyword. " : "" ) .
            "Identifie le mot-clé principal le plus pertinent et des mots-clés secondaires associés. " .
            "Pour chaque mot-clé, fournis une estimation du volume de recherche (élevé, moyen, faible), " .
            "une estimation de la difficulté SEO (facile, moyenne, difficile), " .
            "et l'intention de recherche (informationnelle, transactionnelle, navigationnelle). " .
            "Réponds au format JSON avec les champs suivants : " .
            "'primary_keyword' (objet avec 'keyword', 'volume', 'difficulty', 'intent', 'relevance'), " .
            "'secondary_keywords' (tableau d'objets avec 'keyword', 'volume', 'difficulty', 'intent', 'relevance'), " .
            "'long_tail_keywords' (tableau d'objets avec 'keyword', 'volume', 'difficulty', 'intent', 'relevance').",
            array(
                'title' => isset( $title ) ? $title : '',
                'content' => $content,
                'excerpt' => isset( $excerpt ) ? $excerpt : '',
            )
        );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Essayer de parser la réponse JSON
        $json_response = $this->extract_json_from_response( $response );
        
        if ( $json_response ) {
            return $json_response;
        }

        // Si la réponse n'est pas un JSON valide, essayer de la structurer manuellement
        return $this->format_non_json_response( $response );
    }

    /**
     * Calcule la densité des mots-clés dans un contenu
     *
     * @param string $content Contenu à analyser.
     * @param string $keyword Mot-clé à rechercher.
     * @return array Résultats de l'analyse de densité.
     */
    public function calculate_keyword_density( $content, $keyword ) {
        // Nettoyer le contenu
        $clean_content = strip_tags( $content );
        $clean_content = strtolower( $clean_content );

        // Compter le nombre total de mots
        $total_words = str_word_count( $clean_content );

        // Compter les occurrences du mot-clé
        $keyword = strtolower( $keyword );
        $keyword_count = substr_count( $clean_content, $keyword );

        // Calculer la densité
        $density = $total_words > 0 ? ( $keyword_count / $total_words ) * 100 : 0;

        // Déterminer si la densité est optimale
        $status = 'poor';
        if ( $density >= 0.5 && $density <= 2.5 ) {
            $status = 'good';
        } elseif ( $density > 0 && $density < 0.5 ) {
            $status = 'average';
        } elseif ( $density > 2.5 && $density <= 3.5 ) {
            $status = 'average';
        }

        return array(
            'keyword' => $keyword,
            'count' => $keyword_count,
            'total_words' => $total_words,
            'density' => round( $density, 2 ),
            'status' => $status,
        );
    }

    /**
     * Suggère des mots-clés associés à un mot-clé principal
     *
     * @param string $keyword Mot-clé principal.
     * @return array|WP_Error Mots-clés associés ou erreur.
     */
    public function suggest_related_keywords( $keyword ) {
        // Générer le prompt pour l'IA
        $prompt = $this->build_prompt(
            "Suggère des mots-clés associés au mot-clé principal suivant : $keyword. " .
            "Fournis 10 mots-clés secondaires pertinents et 5 mots-clés longue traîne. " .
            "Pour chaque mot-clé, estime le volume de recherche (élevé, moyen, faible), " .
            "la difficulté SEO (facile, moyenne, difficile), " .
            "et l'intention de recherche (informationnelle, transactionnelle, navigationnelle). " .
            "Réponds au format JSON avec les champs suivants : " .
            "'secondary_keywords' (tableau d'objets avec 'keyword', 'volume', 'difficulty', 'intent'), " .
            "'long_tail_keywords' (tableau d'objets avec 'keyword', 'volume', 'difficulty', 'intent')."
        );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Essayer de parser la réponse JSON
        $json_response = $this->extract_json_from_response( $response );
        
        if ( $json_response ) {
            return $json_response;
        }

        // Si la réponse n'est pas un JSON valide, essayer de la structurer manuellement
        return $this->format_non_json_response( $response );
    }

    /**
     * Construit un prompt pour l'IA
     *
     * @param string $instruction Instruction pour l'IA.
     * @param array  $context     Contexte pour l'IA.
     * @return string Prompt pour l'IA.
     */
    private function build_prompt( $instruction, $context = array() ) {
        $prompt = $instruction . "\n\n";

        if ( ! empty( $context['title'] ) ) {
            $prompt .= "Titre: " . $context['title'] . "\n\n";
        }

        if ( ! empty( $context['content'] ) ) {
            $prompt .= "Contenu: " . $context['content'] . "\n\n";
        }

        if ( ! empty( $context['excerpt'] ) ) {
            $prompt .= "Extrait: " . $context['excerpt'] . "\n\n";
        }

        $prompt .= "Réponds en français. Sois précis et détaillé.";

        return $prompt;
    }

    /**
     * Extrait le JSON de la réponse de l'IA
     *
     * @param string $response Réponse de l'IA.
     * @return array|false Données JSON ou false si invalide.
     */
    private function extract_json_from_response( $response ) {
        // D'abord, essayer de parser directement la réponse comme JSON
        $data = json_decode( $response, true );
        if ( json_last_error() === JSON_ERROR_NONE ) {
            return $data;
        }
        
        // Essayer de trouver un objet JSON dans la réponse
        preg_match( '/\{.*\}/s', $response, $matches );
        
        if ( ! empty( $matches[0] ) ) {
            $json = $matches[0];
            $data = json_decode( $json, true );
            
            if ( json_last_error() === JSON_ERROR_NONE ) {
                return $data;
            }
        }
        
        return false;
    }

    /**
     * Formate une réponse non-JSON
     *
     * @param string $response Réponse de l'IA.
     * @return array Réponse formatée.
     */
    private function format_non_json_response( $response ) {
        // Essayer de structurer la réponse en fonction des patterns courants
        $primary_keyword = '';
        $secondary_keywords = array();
        $long_tail_keywords = array();

        // Rechercher le mot-clé principal
        if ( preg_match( '/mot-clé principal.*?:.*?([^\n]+)/i', $response, $matches ) ) {
            $primary_keyword = trim( $matches[1] );
        }

        // Rechercher les mots-clés secondaires
        if ( preg_match_all( '/- ([^:]+):/i', $response, $matches ) ) {
            foreach ( $matches[1] as $keyword ) {
                $secondary_keywords[] = array(
                    'keyword' => trim( $keyword ),
                    'volume' => 'moyen',
                    'difficulty' => 'moyenne',
                    'intent' => 'informationnelle',
                    'relevance' => 'élevée',
                );
            }
        }

        // Limiter à 10 mots-clés secondaires
        $secondary_keywords = array_slice( $secondary_keywords, 0, 10 );

        // Créer une structure de réponse
        return array(
            'primary_keyword' => array(
                'keyword' => $primary_keyword,
                'volume' => 'élevé',
                'difficulty' => 'moyenne',
                'intent' => 'informationnelle',
                'relevance' => 'très élevée',
            ),
            'secondary_keywords' => $secondary_keywords,
            'long_tail_keywords' => $long_tail_keywords,
        );
    }
}
