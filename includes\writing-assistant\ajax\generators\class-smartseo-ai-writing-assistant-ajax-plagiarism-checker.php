<?php
/**
 * Classe de vérification de plagiat pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la vérification de plagiat
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Plagiarism_Checker {

    /**
     * Instance de l'API de vérification de plagiat
     *
     * @var SmartSEO_AI_Plagiarism_API
     */
    private $plagiarism_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Initialiser l'API de vérification de plagiat
        $this->plagiarism_api = new SmartSEO_AI_Plagiarism_API();
    }

    /**
     * Vérifie le plagiat
     *
     * @param string $content Contenu à vérifier.
     * @return array|WP_Error Résultats de la vérification ou erreur.
     */
    public function check( $content ) {
        // Vérifier si le contenu est fourni
        if ( empty( $content ) ) {
            return new WP_Error( 'missing_data', __( 'Veuillez fournir un contenu à vérifier.', 'smartseo-ai' ) );
        }

        // Nettoyer le contenu
        $clean_content = wp_strip_all_tags( $content );

        // Vérifier si le contenu est trop court
        if ( strlen( $clean_content ) < 100 ) {
            return new WP_Error( 'content_too_short', __( 'Le contenu est trop court pour être vérifié.', 'smartseo-ai' ) );
        }

        // Vérifier si l'API est disponible
        if ( ! $this->plagiarism_api->is_available() ) {
            // Simuler une vérification de plagiat
            return $this->simulate_plagiarism_check( $clean_content );
        }

        // Appeler l'API de vérification de plagiat
        $response = $this->plagiarism_api->check_plagiarism( $clean_content );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        return $this->process_response( $response );
    }

    /**
     * Simule une vérification de plagiat
     *
     * @param string $content Contenu à vérifier.
     * @return array Résultats simulés de la vérification.
     */
    private function simulate_plagiarism_check( $content ) {
        // Générer un pourcentage d'unicité aléatoire entre 85 et 100
        $unique_percentage = mt_rand( 85, 100 );
        $duplicate_percentage = 100 - $unique_percentage;
        
        // Créer des sources de duplication simulées
        $duplicate_sources = array();
        
        if ( $duplicate_percentage > 0 ) {
            $num_sources = min( 3, ceil( $duplicate_percentage / 5 ) );
            
            for ( $i = 0; $i < $num_sources; $i++ ) {
                // Extraire un extrait aléatoire du contenu
                $words = explode( ' ', $content );
                $start = mt_rand( 0, count( $words ) - 20 );
                $length = mt_rand( 10, 20 );
                $excerpt = implode( ' ', array_slice( $words, $start, $length ) );
                
                // Créer une source simulée
                $duplicate_sources[] = array(
                    'url' => 'https://example.com/article-' . mt_rand( 1000, 9999 ),
                    'similarity' => mt_rand( 1, $duplicate_percentage ),
                    'matched_text' => $excerpt,
                );
            }
            
            // Trier les sources par similarité décroissante
            usort( $duplicate_sources, function( $a, $b ) {
                return $b['similarity'] - $a['similarity'];
            } );
        }
        
        // Retourner les résultats simulés
        return array(
            'unique_percentage' => $unique_percentage,
            'duplicate_percentage' => $duplicate_percentage,
            'duplicate_sources' => $duplicate_sources,
        );
    }

    /**
     * Traite la réponse de l'API
     *
     * @param array $response Réponse de l'API.
     * @return array Résultats de la vérification.
     */
    private function process_response( $response ) {
        // Extraire les données pertinentes
        $unique_percentage = isset( $response['unique_percentage'] ) ? $response['unique_percentage'] : 100;
        $duplicate_percentage = isset( $response['duplicate_percentage'] ) ? $response['duplicate_percentage'] : 0;
        $duplicate_sources = isset( $response['duplicate_sources'] ) ? $response['duplicate_sources'] : array();
        
        // Formater les sources de duplication
        $formatted_sources = array();
        
        foreach ( $duplicate_sources as $source ) {
            $formatted_sources[] = array(
                'url' => isset( $source['url'] ) ? $source['url'] : '',
                'similarity' => isset( $source['similarity'] ) ? $source['similarity'] : 0,
                'matched_text' => isset( $source['matched_text'] ) ? $source['matched_text'] : '',
            );
        }
        
        // Retourner les résultats formatés
        return array(
            'unique_percentage' => $unique_percentage,
            'duplicate_percentage' => $duplicate_percentage,
            'duplicate_sources' => $formatted_sources,
        );
    }
}
