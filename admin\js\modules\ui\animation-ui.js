/**
 * Module de gestion des animations
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire des animations
     */
    const AnimationUI = {
        /**
         * Initialise le gestionnaire des animations
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Affiche un message de chargement
         * @param {string} message Message à afficher
         * @param {string} target  Cible du message
         */
        showLoading: function(message, target) {
            // Déterminer le conteneur de chargement
            let $container;
            
            if (target) {
                // Chercher un conteneur de chargement spécifique à la cible
                $container = $('#smartseo-ai-' + target + '-results .smartseo-ai-loading');
            } else {
                // Créer un conteneur de chargement global
                if ($('#smartseo-ai-global-loading').length === 0) {
                    $('body').append('<div id="smartseo-ai-global-loading" class="smartseo-ai-loading"></div>');
                }
                
                $container = $('#smartseo-ai-global-loading');
            }
            
            // Mettre à jour le message
            if (message) {
                $container.find('p').text(message);
            }
            
            // Afficher le conteneur
            $container.show();
            
            // Ajouter l'animation
            this.addLoadingAnimation($container);
        },

        /**
         * Masque le message de chargement
         * @param {string} target Cible du message
         */
        hideLoading: function(target) {
            // Déterminer le conteneur de chargement
            let $container;
            
            if (target) {
                // Chercher un conteneur de chargement spécifique à la cible
                $container = $('#smartseo-ai-' + target + '-results .smartseo-ai-loading');
            } else {
                // Utiliser le conteneur de chargement global
                $container = $('#smartseo-ai-global-loading');
            }
            
            // Masquer le conteneur
            $container.hide();
            
            // Supprimer l'animation
            this.removeLoadingAnimation($container);
        },

        /**
         * Ajoute une animation de chargement
         * @param {jQuery} $container Conteneur de l'animation
         */
        addLoadingAnimation: function($container) {
            // Vérifier si le conteneur a déjà une animation
            if ($container.find('.smartseo-ai-spinner').length === 0) {
                // Ajouter l'animation
                $container.prepend('<div class="smartseo-ai-spinner"></div>');
                
                // Ajouter la classe d'animation
                $container.addClass('smartseo-ai-loading-active');
            }
        },

        /**
         * Supprime une animation de chargement
         * @param {jQuery} $container Conteneur de l'animation
         */
        removeLoadingAnimation: function($container) {
            // Supprimer l'animation
            $container.find('.smartseo-ai-spinner').remove();
            
            // Supprimer la classe d'animation
            $container.removeClass('smartseo-ai-loading-active');
        },

        /**
         * Ajoute une animation de pulsation
         * @param {jQuery} $element Élément à animer
         */
        addPulseAnimation: function($element) {
            // Ajouter la classe d'animation
            $element.addClass('smartseo-ai-pulse');
            
            // Supprimer la classe après l'animation
            setTimeout(function() {
                $element.removeClass('smartseo-ai-pulse');
            }, 1000);
        },

        /**
         * Ajoute une animation de fondu
         * @param {jQuery} $element Élément à animer
         */
        addFadeAnimation: function($element) {
            // Masquer l'élément
            $element.css('opacity', 0);
            
            // Afficher l'élément avec une animation
            $element.animate({
                opacity: 1
            }, 500);
        },

        /**
         * Ajoute une animation de glissement
         * @param {jQuery} $element Élément à animer
         * @param {string} direction Direction du glissement (up, down, left, right)
         */
        addSlideAnimation: function($element, direction) {
            // Déterminer les propriétés d'animation
            let from = {};
            let to = {};
            
            switch (direction) {
                case 'up':
                    from = { top: '100%', opacity: 0 };
                    to = { top: 0, opacity: 1 };
                    break;
                case 'down':
                    from = { top: '-100%', opacity: 0 };
                    to = { top: 0, opacity: 1 };
                    break;
                case 'left':
                    from = { left: '100%', opacity: 0 };
                    to = { left: 0, opacity: 1 };
                    break;
                case 'right':
                    from = { left: '-100%', opacity: 0 };
                    to = { left: 0, opacity: 1 };
                    break;
            }
            
            // Appliquer les propriétés initiales
            $element.css({
                position: 'relative',
                ...from
            });
            
            // Animer vers les propriétés finales
            $element.animate(to, 500);
        },

        /**
         * Ajoute une animation de rebond
         * @param {jQuery} $element Élément à animer
         */
        addBounceAnimation: function($element) {
            // Ajouter la classe d'animation
            $element.addClass('smartseo-ai-bounce');
            
            // Supprimer la classe après l'animation
            setTimeout(function() {
                $element.removeClass('smartseo-ai-bounce');
            }, 1000);
        },

        /**
         * Ajoute une animation de rotation
         * @param {jQuery} $element Élément à animer
         */
        addRotateAnimation: function($element) {
            // Ajouter la classe d'animation
            $element.addClass('smartseo-ai-rotate');
            
            // Supprimer la classe après l'animation
            setTimeout(function() {
                $element.removeClass('smartseo-ai-rotate');
            }, 1000);
        },

        /**
         * Ajoute une animation de secousse
         * @param {jQuery} $element Élément à animer
         */
        addShakeAnimation: function($element) {
            // Ajouter la classe d'animation
            $element.addClass('smartseo-ai-shake');
            
            // Supprimer la classe après l'animation
            setTimeout(function() {
                $element.removeClass('smartseo-ai-shake');
            }, 1000);
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.AnimationUI = AnimationUI;

})(jQuery);
