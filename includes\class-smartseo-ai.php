<?php
/**
 * Classe principale du plugin SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe principale qui gère le chargement des composants du plugin
 */
class SmartSEO_AI {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI
     */
    private static $instance = null;

    /**
     * Constructeur
     */
    public function __construct() {
        $this->load_dependencies();
        $this->define_admin_hooks();
        $this->define_public_hooks();
    }

    /**
     * Charge les dépendances requises pour le plugin
     */
    private function load_dependencies() {
        // Classes principales
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-metaboxes.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-gutenberg.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-api.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-gemini.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-admin.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-settings.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-bulk-optimizer.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/tag-generator/class-smartseo-ai-tag-generator.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-global-dashboard.php'; // SUPPRIMÉ
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-sitemap-module.php';

        // Classes d'audit SEO - SUPPRIMÉES
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-seo-audit.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-meta-analyzer.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-image-analyzer.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-heading-analyzer.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-url-analyzer.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-link-analyzer.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-keyword-analyzer.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-robots-sitemap-analyzer.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-schema-analyzer.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-audit-report.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-bulk-audit.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-audit-list-table.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-audit-exporter.php';

        // Classes de suggestions SEO - SUPPRIMÉES
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-suggestions.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/suggestions/class-smartseo-ai-suggestion-base.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/suggestions/class-smartseo-ai-keyword-suggestions.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/suggestions/class-smartseo-ai-meta-suggestions.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/suggestions/class-smartseo-ai-heading-suggestions.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/suggestions/class-smartseo-ai-content-structure-suggestions.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/suggestions/class-smartseo-ai-internal-link-suggestions.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/suggestions/class-smartseo-ai-image-alt-suggestions.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/suggestions/class-smartseo-ai-readability-suggestions.php';
        // require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/suggestions/class-smartseo-ai-rich-snippet-suggestions.php';

        // API REST
        require_once SMARTSEO_AI_PLUGIN_DIR . 'api/class-smartseo-ai-rest-controller.php';
    }

    /**
     * Définit les hooks liés à l'administration
     */
    private function define_admin_hooks() {
        // Enregistrement des scripts et styles admin
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_assets' ) );

        // Initialisation des classes
        $metaboxes = new SmartSEO_AI_Metaboxes();
        $gutenberg = new SmartSEO_AI_Gutenberg();
        $admin = new SmartSEO_AI_Admin();
        $settings = new SmartSEO_AI_Settings();
        $bulk_optimizer = new SmartSEO_AI_Bulk_Optimizer();
        // $seo_audit = new SmartSEO_AI_SEO_Audit(); // SUPPRIMÉ
        // $suggestions = SmartSEO_AI_Suggestions::get_instance(); // SUPPRIMÉ
        // $bulk_audit = SmartSEO_AI_Bulk_Audit::get_instance(); // SUPPRIMÉ
        // $global_dashboard = SmartSEO_AI_Global_Dashboard::get_instance(); // SUPPRIMÉ
        $tag_generator = SmartSEO_AI_Tag_Generator::get_instance();
        $sitemap_module = SmartSEO_AI_Sitemap_Module::get_instance();

        // Initialisation de l'API REST
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ) );
    }

    /**
     * Définit les hooks publics
     */
    private function define_public_hooks() {
        // Hooks pour le frontend si nécessaire
        add_action( 'wp_head', array( $this, 'output_meta_tags' ) );
    }

    /**
     * Enregistre les scripts et styles d'administration
     */
    public function enqueue_admin_assets( $hook ) {
        // Styles admin
        wp_enqueue_style(
            'smartseo-ai-admin',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-admin.css',
            array(),
            SMARTSEO_AI_VERSION
        );

        // Styles pour l'audit SEO, l'audit en masse et les suggestions - SUPPRIMÉS
        if ( false ) { // Désactivé car modules supprimés
            // Charger Select2
            wp_enqueue_style(
                'select2',
                'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css',
                array(),
                '4.1.0-rc.0'
            );

            // Charger Handlebars
            wp_enqueue_script(
                'handlebars',
                'https://cdn.jsdelivr.net/npm/handlebars@4.7.7/dist/handlebars.min.js',
                array(),
                '4.7.7',
                true
            );

            // Charger Select2
            wp_enqueue_script(
                'select2',
                'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js',
                array( 'jquery' ),
                '4.1.0-rc.0',
                true
            );

            // Styles spécifiques à l'audit SEO
            wp_enqueue_style(
                'smartseo-ai-audit',
                SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-audit.css',
                array(),
                SMARTSEO_AI_VERSION
            );

            // Styles spécifiques aux suggestions SEO
            if ( strpos( $hook, 'smartseo-ai-suggestions' ) !== false || strpos( $hook, 'post.php' ) !== false || strpos( $hook, 'post-new.php' ) !== false ) {
                wp_enqueue_style(
                    'smartseo-ai-suggestions',
                    SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-suggestions.css',
                    array(),
                    SMARTSEO_AI_VERSION
                );
            }

            // Styles spécifiques à l'audit SEO en masse
            if ( strpos( $hook, 'smartseo-ai-bulk-audit' ) !== false ) {
                wp_enqueue_style(
                    'smartseo-ai-bulk-audit',
                    SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-bulk-audit.css',
                    array(),
                    SMARTSEO_AI_VERSION
                );
            }
        }

        // Scripts admin
        wp_enqueue_script(
            'smartseo-ai-admin',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-admin.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Localize script
        wp_localize_script(
            'smartseo-ai-admin',
            'smartseoAiData',
            array(
                'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                'restUrl' => rest_url( 'smartseo-ai/v1' ),
                'nonce' => wp_create_nonce( 'wp_rest' ),
                'i18n' => array(
                    'optimizing' => __( 'Optimisation en cours...', 'smartseo-ai' ),
                    'success' => __( 'Optimisation réussie !', 'smartseo-ai' ),
                    'error' => __( 'Erreur lors de l\'optimisation.', 'smartseo-ai' ),
                ),
            )
        );
    }

    /**
     * Enregistre les routes REST API
     */
    public function register_rest_routes() {
        $controller = new SmartSEO_AI_REST_Controller();
        $controller->register_routes();
    }

    /**
     * Génère les balises meta dans le head
     */
    public function output_meta_tags() {
        if ( is_singular() ) {
            global $post;

            // Meta description
            $meta_description = get_post_meta( $post->ID, 'smartseo_ai_meta_description', true );
            if ( ! empty( $meta_description ) ) {
                echo '<meta name="description" content="' . esc_attr( $meta_description ) . '" />' . "\n";
            }

            // Open Graph tags
            $og_title = get_post_meta( $post->ID, 'smartseo_ai_og_title', true );
            $og_description = get_post_meta( $post->ID, 'smartseo_ai_og_description', true );
            $og_image = get_post_meta( $post->ID, 'smartseo_ai_og_image', true );

            if ( ! empty( $og_title ) ) {
                echo '<meta property="og:title" content="' . esc_attr( $og_title ) . '" />' . "\n";
            }

            if ( ! empty( $og_description ) ) {
                echo '<meta property="og:description" content="' . esc_attr( $og_description ) . '" />' . "\n";
            }

            if ( ! empty( $og_image ) ) {
                echo '<meta property="og:image" content="' . esc_url( $og_image ) . '" />' . "\n";
            }

            // Keywords (pour les moteurs de recherche qui les utilisent encore)
            $keywords = get_post_meta( $post->ID, 'smartseo_ai_keywords', true );
            if ( ! empty( $keywords ) ) {
                echo '<meta name="keywords" content="' . esc_attr( $keywords ) . '" />' . "\n";
            }
        }
    }

    /**
     * Exécute le plugin
     */
    public function run() {
        // Rien de spécial à faire ici pour l'instant
    }

    /**
     * Retourne l'instance unique de la classe
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
