<?php
/**
 * Initialisation de l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Initialise l'Assistant de Rédaction SEO
 */
function smartseo_ai_init_writing_assistant() {
    // Vérifier si l'Assistant de Rédaction SEO est activé
    if ( ! get_option( 'smartseo_ai_enable_writing_assistant', true ) ) {
        return;
    }

    // Charger les dépendances
    require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai-writing-assistant.php';

    // Initialiser l'Assistant de Rédaction SEO
    $writing_assistant = SmartSEO_AI_Writing_Assistant::get_instance();
}
add_action( 'plugins_loaded', 'smartseo_ai_init_writing_assistant' );

/**
 * Ajoute les styles pour l'Assistant de Rédaction SEO
 */
function smartseo_ai_writing_assistant_admin_styles() {
    // Vérifier si nous sommes sur une page d'édition
    $screen = get_current_screen();
    if ( ! $screen || ! in_array( $screen->base, array( 'post', 'post-new' ), true ) ) {
        return;
    }

    // Ajouter les styles
    ?>
    <style type="text/css">
        /* Styles pour la métabox */
        .smartseo-ai-meta-box-field {
            margin-bottom: 15px;
        }

        .smartseo-ai-meta-description-counter {
            text-align: right;
            font-size: 12px;
            color: #666;
        }

        .smartseo-ai-meta-description-too-long {
            color: #dc3232;
        }

        /* Styles pour le panneau de l'éditeur de blocs */
        .smartseo-ai-meta-box-panel .components-panel__body {
            border-top: 1px solid #e0e0e0;
        }

        .smartseo-ai-meta-box-panel .components-base-control {
            margin-bottom: 16px;
        }

        /* Styles pour les notifications */
        .smartseo-ai-meta-box-message {
            margin-top: 10px;
        }

        .smartseo-ai-meta-box-loading {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }

        .smartseo-ai-meta-box-loading .spinner {
            margin-top: 0;
            margin-right: 5px;
        }
    </style>
    <?php
}
add_action( 'admin_head', 'smartseo_ai_writing_assistant_admin_styles' );
