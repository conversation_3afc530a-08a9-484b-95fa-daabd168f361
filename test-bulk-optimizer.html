<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Module Optimisation en Masse</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 20px;
            background: #f1f1f1;
        }
        
        .test-container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .test-button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #005a87;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Module Optimisation en Masse</h1>
        <p>Cette page teste les fonctionnalités du module d'optimisation en masse SmartSEO AI.</p>
        
        <div class="test-section">
            <h3>🔗 Test des Endpoints API</h3>
            <button class="test-button" onclick="testDashboardStats()">Test Dashboard Stats</button>
            <button class="test-button" onclick="testOptimizePost()">Test Optimiser Article</button>
            <button class="test-button" onclick="testBulkOptimize()">Test Optimisation en Masse</button>
            <button class="test-button" onclick="testApiHealth()">Test Santé API</button>
            <div id="api-results"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 Test des Fonctions JavaScript</h3>
            <button class="test-button" onclick="testUpdateQuickStats()">Test updateQuickStats()</button>
            <button class="test-button" onclick="testUpdateCharts()">Test updateCharts()</button>
            <button class="test-button" onclick="testLoadDashboard()">Test loadDashboardData()</button>
            <div id="js-results"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 Simulation du Module</h3>
            <div id="dashboard-simulation">
                <div style="display: flex; gap: 20px; margin: 20px 0;">
                    <div style="flex: 1; background: #f8f9fa; padding: 15px; border-radius: 4px;">
                        <h4>Articles Optimisés</h4>
                        <span id="total-posts-optimized" style="font-size: 24px; font-weight: bold; color: #0073aa;">0</span>
                    </div>
                    <div style="flex: 1; background: #f8f9fa; padding: 15px; border-radius: 4px;">
                        <h4>Taux de Succès</h4>
                        <span id="optimization-success-rate" style="font-size: 24px; font-weight: bold; color: #46b450;">0%</span>
                    </div>
                    <div style="flex: 1; background: #f8f9fa; padding: 15px; border-radius: 4px;">
                        <h4>Temps Moyen</h4>
                        <span id="avg-optimization-time" style="font-size: 24px; font-weight: bold; color: #ff9800;">0s</span>
                    </div>
                    <div style="flex: 1; background: #f8f9fa; padding: 15px; border-radius: 4px;">
                        <h4>En Attente</h4>
                        <span id="pending-optimizations" style="font-size: 24px; font-weight: bold; color: #dc3232;">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales pour simuler l'environnement
        let dashboardData = null;
        
        // Simulation des données du tableau de bord
        const mockDashboardData = {
            quick_stats: {
                total_optimized: 42,
                success_rate: 87,
                avg_time: 2.3,
                pending: 5
            },
            performance_stats: {
                by_provider: [
                    { provider: 'OpenAI', requests: 100, successes: 95, avg_time: '1.2' },
                    { provider: 'Claude', requests: 80, successes: 78, avg_time: '1.5' }
                ]
            },
            cache_stats: {
                general: {
                    total_entries: 150,
                    total_hits: 1250,
                    active_entries: 120,
                    expired_entries: 30
                }
            }
        };

        // Fonction updateQuickStats (copiée du fichier original)
        function updateQuickStats() {
            if (!dashboardData || !dashboardData.quick_stats) {
                return;
            }

            const stats = dashboardData.quick_stats;
            
            // Mettre à jour les compteurs
            $('#total-posts-optimized').text(stats.total_optimized || 0);
            $('#optimization-success-rate').text((stats.success_rate || 0) + '%');
            $('#avg-optimization-time').text((stats.avg_time || 0) + 's');
            $('#pending-optimizations').text(stats.pending || 0);
            
            // Mettre à jour les barres de progression si elles existent
            if (stats.success_rate) {
                $('.success-rate-bar').css('width', stats.success_rate + '%');
            }
        }

        // Fonction updateCharts (copiée du fichier original)
        function updateCharts() {
            updatePerformanceChart();
            updateCacheChart();
        }

        function updatePerformanceChart() {
            if (!dashboardData || !dashboardData.performance_stats) {
                return;
            }
            console.log('Mise à jour du graphique de performance');
        }

        function updateCacheChart() {
            if (!dashboardData || !dashboardData.cache_stats) {
                return;
            }
            console.log('Mise à jour du graphique de cache');
        }

        // Tests des endpoints API
        function testDashboardStats() {
            addResult('api-results', 'info', 'Test Dashboard Stats...');
            
            // Simuler un appel API réussi
            setTimeout(() => {
                dashboardData = mockDashboardData;
                addResult('api-results', 'success', 'Dashboard Stats: ' + JSON.stringify(dashboardData.quick_stats, null, 2));
            }, 500);
        }

        function testOptimizePost() {
            addResult('api-results', 'info', 'Test Optimiser Article (ID: 2)...');
            
            // Simuler un appel API
            setTimeout(() => {
                const result = {
                    success: true,
                    message: 'Article optimisé avec succès',
                    data: {
                        seo_title: 'Titre SEO optimisé',
                        meta_description: 'Description optimisée',
                        keywords: 'mot-clé1, mot-clé2, mot-clé3'
                    }
                };
                addResult('api-results', 'success', 'Optimisation réussie: ' + JSON.stringify(result, null, 2));
            }, 1000);
        }

        function testBulkOptimize() {
            addResult('api-results', 'info', 'Test Optimisation en Masse...');
            
            setTimeout(() => {
                const result = {
                    success: true,
                    message: 'Optimisation en masse démarrée',
                    data: {
                        batch_id: 'batch_123',
                        total_posts: 10,
                        estimated_time: '30 secondes'
                    }
                };
                addResult('api-results', 'success', 'Optimisation en masse: ' + JSON.stringify(result, null, 2));
            }, 800);
        }

        function testApiHealth() {
            addResult('api-results', 'info', 'Test Santé API...');
            
            setTimeout(() => {
                const result = {
                    openai: { status: 'healthy', response_time: 150 },
                    claude: { status: 'healthy', response_time: 200 },
                    gemini: { status: 'healthy', response_time: 180 }
                };
                addResult('api-results', 'success', 'Santé API: ' + JSON.stringify(result, null, 2));
            }, 600);
        }

        // Tests des fonctions JavaScript
        function testUpdateQuickStats() {
            addResult('js-results', 'info', 'Test updateQuickStats()...');
            
            if (typeof updateQuickStats === 'function') {
                dashboardData = mockDashboardData;
                updateQuickStats();
                addResult('js-results', 'success', 'updateQuickStats() exécutée avec succès. Vérifiez les compteurs ci-dessus.');
            } else {
                addResult('js-results', 'error', 'Fonction updateQuickStats() non trouvée !');
            }
        }

        function testUpdateCharts() {
            addResult('js-results', 'info', 'Test updateCharts()...');
            
            if (typeof updateCharts === 'function') {
                dashboardData = mockDashboardData;
                updateCharts();
                addResult('js-results', 'success', 'updateCharts() exécutée avec succès. Vérifiez la console.');
            } else {
                addResult('js-results', 'error', 'Fonction updateCharts() non trouvée !');
            }
        }

        function testLoadDashboard() {
            addResult('js-results', 'info', 'Test loadDashboardData()...');
            
            // Simuler le chargement des données
            dashboardData = mockDashboardData;
            updateQuickStats();
            updateCharts();
            
            addResult('js-results', 'success', 'Données du tableau de bord chargées et affichées.');
        }

        // Fonction utilitaire pour ajouter des résultats
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = 'test-result ' + type;
            result.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            container.appendChild(result);
            
            // Faire défiler vers le bas
            result.scrollIntoView({ behavior: 'smooth' });
        }

        // Initialisation
        $(document).ready(function() {
            addResult('js-results', 'info', 'Page de test chargée. jQuery version: ' + $.fn.jquery);
            
            // Test automatique des fonctions
            setTimeout(() => {
                addResult('js-results', 'info', 'Test automatique des fonctions...');
                testUpdateQuickStats();
            }, 1000);
        });
    </script>
</body>
</html>
