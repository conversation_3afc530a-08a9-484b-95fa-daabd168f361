<?php
/**
 * Classe de gestion des AJAX de génération de contenu pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les AJAX de génération de contenu de l'Assistant de Rédaction SEO
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Content_Generator {

    /**
     * Gestionnaire de génération de titres
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Title_Generator
     */
    private $title_generator;

    /**
     * Gestionnaire de génération de meta descriptions
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Meta_Description_Generator
     */
    private $meta_description_generator;

    /**
     * Gestionnaire de génération d'introductions
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Introduction_Generator
     */
    private $introduction_generator;

    /**
     * Gestionnaire de génération de conclusions
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Conclusion_Generator
     */
    private $conclusion_generator;

    /**
     * Gestionnaire de génération de titres H2
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Headings_Generator
     */
    private $headings_generator;

    /**
     * Gestionnaire de génération de paragraphes
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Paragraph_Generator
     */
    private $paragraph_generator;

    /**
     * Gestionnaire de génération d'articles complets
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Article_Generator
     */
    private $article_generator;

    /**
     * Gestionnaire d'optimisation de contenu
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Content_Optimizer
     */
    private $content_optimizer;

    /**
     * Gestionnaire de vérification de plagiat
     *
     * @var SmartSEO_AI_Writing_Assistant_Ajax_Plagiarism_Checker
     */
    private $plagiarism_checker;

    /**
     * Constructeur
     */
    public function __construct() {
        // Charger les dépendances
        $this->load_dependencies();

        // Initialiser les composants
        $this->initialize_components();
    }

    /**
     * Charge les dépendances
     */
    private function load_dependencies() {
        // Charger les classes nécessaires
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/generators/class-smartseo-ai-writing-assistant-ajax-title-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/generators/class-smartseo-ai-writing-assistant-ajax-meta-description-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/generators/class-smartseo-ai-writing-assistant-ajax-introduction-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/generators/class-smartseo-ai-writing-assistant-ajax-conclusion-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/generators/class-smartseo-ai-writing-assistant-ajax-headings-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/generators/class-smartseo-ai-writing-assistant-ajax-paragraph-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/generators/class-smartseo-ai-writing-assistant-ajax-article-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/generators/class-smartseo-ai-writing-assistant-ajax-content-optimizer.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/writing-assistant/ajax/generators/class-smartseo-ai-writing-assistant-ajax-plagiarism-checker.php';
    }

    /**
     * Initialise les composants
     */
    private function initialize_components() {
        $this->title_generator = new SmartSEO_AI_Writing_Assistant_Ajax_Title_Generator();
        $this->meta_description_generator = new SmartSEO_AI_Writing_Assistant_Ajax_Meta_Description_Generator();
        $this->introduction_generator = new SmartSEO_AI_Writing_Assistant_Ajax_Introduction_Generator();
        $this->conclusion_generator = new SmartSEO_AI_Writing_Assistant_Ajax_Conclusion_Generator();
        $this->headings_generator = new SmartSEO_AI_Writing_Assistant_Ajax_Headings_Generator();
        $this->paragraph_generator = new SmartSEO_AI_Writing_Assistant_Ajax_Paragraph_Generator();
        $this->article_generator = new SmartSEO_AI_Writing_Assistant_Ajax_Article_Generator();
        $this->content_optimizer = new SmartSEO_AI_Writing_Assistant_Ajax_Content_Optimizer();
        $this->plagiarism_checker = new SmartSEO_AI_Writing_Assistant_Ajax_Plagiarism_Checker();
    }

    /**
     * Génère du contenu
     */
    public function generate_content() {
        // Vérifier le nonce
        if ( ! check_ajax_referer( 'smartseo_ai_writing_assistant_nonce', 'nonce', false ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page.', 'smartseo-ai' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les données
        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $content_type = isset( $_POST['content_type'] ) ? sanitize_text_field( wp_unslash( $_POST['content_type'] ) ) : '';
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';
        $topic = isset( $_POST['topic'] ) ? sanitize_text_field( wp_unslash( $_POST['topic'] ) ) : '';
        $content = isset( $_POST['content'] ) ? wp_kses_post( wp_unslash( $_POST['content'] ) ) : '';

        // Générer le contenu en fonction du type
        switch ( $content_type ) {
            case 'title':
                $result = $this->title_generator->generate( $post_id, $keyword, $topic );
                break;

            case 'meta_description':
                $result = $this->meta_description_generator->generate( $post_id, $keyword, $topic, $content );
                break;

            case 'introduction':
                $result = $this->introduction_generator->generate( $post_id, $keyword, $topic );
                break;

            case 'conclusion':
                $result = $this->conclusion_generator->generate( $post_id, $keyword, $topic, $content );
                break;

            case 'h2_headings':
                $result = $this->headings_generator->generate( $post_id, $keyword, $topic, 2 );
                break;

            case 'h3_headings':
                $result = $this->headings_generator->generate( $post_id, $keyword, $topic, 3 );
                break;

            case 'paragraph':
                $result = $this->paragraph_generator->generate( $post_id, $keyword, $topic );
                break;

            case 'full_article':
                $result = $this->article_generator->generate( $post_id, $keyword, $topic );
                break;

            default:
                wp_send_json_error( array( 'message' => __( 'Type de contenu non valide.', 'smartseo-ai' ) ) );
                break;
        }

        // Vérifier si la génération a réussi
        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        // Envoyer la réponse
        wp_send_json_success( $result );
    }

    /**
     * Génère une meta description
     */
    public function generate_meta_description() {
        // Vérifier le nonce
        if ( ! check_ajax_referer( 'smartseo_ai_writing_assistant_nonce', 'nonce', false ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page.', 'smartseo-ai' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les données
        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';
        $title = isset( $_POST['title'] ) ? sanitize_text_field( wp_unslash( $_POST['title'] ) ) : '';
        $content = isset( $_POST['content'] ) ? wp_kses_post( wp_unslash( $_POST['content'] ) ) : '';

        // Générer la meta description
        $result = $this->meta_description_generator->generate( $post_id, $keyword, $title, $content );

        // Vérifier si la génération a réussi
        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        // Récupérer la première description
        $meta_description = isset( $result['descriptions'][0] ) ? $result['descriptions'][0] : '';

        // Enregistrer la meta description
        if ( $post_id > 0 && ! empty( $meta_description ) ) {
            update_post_meta( $post_id, 'smartseo_ai_meta_description', $meta_description );
        }

        // Envoyer la réponse
        wp_send_json_success( array(
            'meta_description' => $meta_description,
            'message' => __( 'Meta description générée avec succès !', 'smartseo-ai' ),
        ) );
    }

    /**
     * Optimise tout le contenu
     */
    public function optimize_all() {
        // Vérifier le nonce
        if ( ! check_ajax_referer( 'smartseo_ai_writing_assistant_nonce', 'nonce', false ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page.', 'smartseo-ai' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les données
        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';

        // Optimiser le contenu
        $result = $this->content_optimizer->optimize( $post_id, $keyword );

        // Vérifier si l'optimisation a réussi
        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        // Envoyer la réponse
        wp_send_json_success( $result );
    }

    /**
     * Vérifie le plagiat
     */
    public function check_plagiarism() {
        // Vérifier le nonce
        if ( ! check_ajax_referer( 'smartseo_ai_writing_assistant_nonce', 'nonce', false ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page.', 'smartseo-ai' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les données
        $content = isset( $_POST['content'] ) ? wp_kses_post( wp_unslash( $_POST['content'] ) ) : '';

        // Vérifier le plagiat
        $result = $this->plagiarism_checker->check( $content );

        // Vérifier si la vérification a réussi
        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        // Envoyer la réponse
        wp_send_json_success( $result );
    }
}
