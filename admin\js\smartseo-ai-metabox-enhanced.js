/**
 * JavaScript amélioré pour la meta box SmartSEO AI
 */
(function($) {
    'use strict';

    // Variables globales
    let isOptimizing = false;
    let mediaUploader = null;

    // Initialisation
    $(document).ready(function() {
        initMetabox();
        bindEvents();
        updateCounters();
        initKeywordTags();
    });

    /**
     * Initialise la meta box
     */
    function initMetabox() {
        // Animation d'entrée
        $('.smartseo-ai-metabox-enhanced').addClass('loaded');
        
        // Initialiser les barres de progression
        updateProgressBars();
        
        // Vérifier l'état initial des champs
        validateFields();
    }

    /**
     * Lie tous les événements
     */
    function bindEvents() {
        // Bouton d'optimisation
        $('#smartseo-ai-optimize-button').on('click', handleOptimization);
        
        // Compteurs de caractères
        $('#smartseo_ai_seo_title').on('input', function() {
            updateCounter(this, 'title', 60);
            updateProgressBar('title', this.value.length, 60);
            validateField(this, 50, 60);
        });
        
        $('#smartseo_ai_meta_description').on('input', function() {
            updateCounter(this, 'description', 160);
            updateProgressBar('description', this.value.length, 160);
            validateField(this, 120, 160);
        });
        
        // Gestion des mots-clés
        $('#smartseo_ai_keywords').on('input', handleKeywordsInput);
        $('#smartseo_ai_keywords').on('blur', updateKeywordTags);
        
        // Sélection d'image
        $('#smartseo-ai-select-image').on('click', openMediaUploader);
        
        // Application du slug
        $('#smartseo-ai-apply-slug').on('click', applySlug);
        
        // Validation en temps réel
        $('.smartseo-ai-input, .smartseo-ai-textarea').on('blur', function() {
            validateField(this);
        });
    }

    /**
     * Gère l'optimisation avec l'IA
     */
    function handleOptimization() {
        if (isOptimizing) {
            return;
        }

        const $button = $('#smartseo-ai-optimize-button');
        const $status = $('#smartseo-ai-optimize-status');
        
        // Récupérer l'ID du post
        const postId = $('#post_ID').val() || wp.media.view.settings.post.id;
        
        if (!postId) {
            showNotification('error', smartseoAiMetabox.i18n.error);
            return;
        }

        // Démarrer l'optimisation
        isOptimizing = true;
        $button.addClass('loading');
        $status.html('<span style="color: #0073aa;">⏳ ' + smartseoAiMetabox.i18n.optimizing + '</span>');

        // Appel AJAX
        $.ajax({
            url: smartseoAiMetabox.restUrl + '/optimize/' + postId,
            method: 'POST',
            headers: {
                'X-WP-Nonce': smartseoAiMetabox.nonce
            },
            timeout: 60000,
            success: function(response) {
                if (response && response.seo_title) {
                    // Mettre à jour les champs
                    updateFields(response);
                    
                    // Mettre à jour le score
                    if (response.seo_score) {
                        updateScoreDisplay(response.seo_score);
                    }
                    
                    $status.html('<span style="color: #46b450;">✅ ' + smartseoAiMetabox.i18n.success + '</span>');
                    
                    // Animation de succès
                    animateSuccess();
                } else {
                    $status.html('<span style="color: #dc3232;">❌ ' + smartseoAiMetabox.i18n.error + '</span>');
                }
            },
            error: function(xhr, status, error) {
                let message = smartseoAiMetabox.i18n.error;
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (status === 'timeout') {
                    message = 'Délai d\'attente dépassé';
                }
                
                $status.html('<span style="color: #dc3232;">❌ ' + message + '</span>');
            },
            complete: function() {
                isOptimizing = false;
                $button.removeClass('loading');
                
                // Effacer le statut après 5 secondes
                setTimeout(function() {
                    $status.fadeOut(300, function() {
                        $(this).html('').show();
                    });
                }, 5000);
            }
        });
    }

    /**
     * Met à jour les champs avec les données optimisées
     */
    function updateFields(data) {
        if (data.seo_title) {
            $('#smartseo_ai_seo_title').val(data.seo_title).trigger('input');
        }
        
        if (data.meta_description) {
            $('#smartseo_ai_meta_description').val(data.meta_description).trigger('input');
        }
        
        if (data.keywords) {
            $('#smartseo_ai_keywords').val(data.keywords).trigger('input').trigger('blur');
        }
        
        if (data.seo_slug) {
            $('#smartseo_ai_seo_slug').val(data.seo_slug);
        }
        
        if (data.og_title) {
            $('#smartseo_ai_og_title').val(data.og_title);
        }
        
        if (data.og_description) {
            $('#smartseo_ai_og_description').val(data.og_description);
        }
    }

    /**
     * Met à jour l'affichage du score
     */
    function updateScoreDisplay(score) {
        // Si pas de score affiché, créer l'affichage
        if ($('.smartseo-ai-score-placeholder').length > 0) {
            const scoreHtml = `
                <div class="smartseo-ai-score-display">
                    <div class="smartseo-ai-score-circle">
                        <svg class="smartseo-ai-score-svg" width="80" height="80" viewBox="0 0 80 80">
                            <circle cx="40" cy="40" r="35" fill="none" stroke="#e6e6e6" stroke-width="6"/>
                            <circle cx="40" cy="40" r="35" fill="none" 
                                    stroke="${getScoreColor(score)}" 
                                    stroke-width="6" 
                                    stroke-linecap="round"
                                    stroke-dasharray="${2 * Math.PI * 35}"
                                    stroke-dashoffset="${2 * Math.PI * 35 * (1 - score / 100)}"
                                    transform="rotate(-90 40 40)"/>
                        </svg>
                        <div class="smartseo-ai-score-text">
                            <span class="smartseo-ai-score-number">${score}</span>
                            <span class="smartseo-ai-score-max">/100</span>
                        </div>
                    </div>
                    <div class="smartseo-ai-score-label">
                        <span class="smartseo-ai-score-title">Score SEO</span>
                        <span class="smartseo-ai-score-status smartseo-ai-score-${getScoreClass(score)}">
                            ${getScoreLabel(score)}
                        </span>
                    </div>
                </div>
            `;
            
            $('.smartseo-ai-score-placeholder').replaceWith(scoreHtml);
        } else {
            // Mettre à jour le score existant
            $('.smartseo-ai-score-number').text(score);
            $('.smartseo-ai-score-svg circle:last-child').attr('stroke', getScoreColor(score));
            $('.smartseo-ai-score-svg circle:last-child').attr('stroke-dashoffset', 2 * Math.PI * 35 * (1 - score / 100));
            $('.smartseo-ai-score-status').attr('class', `smartseo-ai-score-status smartseo-ai-score-${getScoreClass(score)}`).text(getScoreLabel(score));
        }
    }

    /**
     * Met à jour les compteurs de caractères
     */
    function updateCounter(element, type, max) {
        const length = element.value.length;
        const $counter = $('#smartseo-ai-' + type + '-counter');
        
        $counter.text(length);
        
        // Changer la couleur selon la longueur
        if (length > max * 0.9) {
            $counter.css('color', '#dc3232');
        } else if (length > max * 0.8) {
            $counter.css('color', '#ff9800');
        } else {
            $counter.css('color', '#0073aa');
        }
    }

    /**
     * Met à jour les barres de progression
     */
    function updateProgressBar(type, current, max) {
        const percentage = Math.min(100, (current / max) * 100);
        const $progressBar = $('#' + type + '-progress-bar');
        
        $progressBar.css('width', percentage + '%');
        
        // Changer la couleur selon le pourcentage
        if (percentage > 90) {
            $progressBar.addClass('warning');
        } else {
            $progressBar.removeClass('warning');
        }
    }

    /**
     * Met à jour toutes les barres de progression
     */
    function updateProgressBars() {
        const titleLength = $('#smartseo_ai_seo_title').val().length;
        const descLength = $('#smartseo_ai_meta_description').val().length;
        
        updateProgressBar('title', titleLength, 60);
        updateProgressBar('description', descLength, 160);
    }

    /**
     * Met à jour tous les compteurs
     */
    function updateCounters() {
        $('#smartseo_ai_seo_title').trigger('input');
        $('#smartseo_ai_meta_description').trigger('input');
        updateKeywordCounter();
    }

    /**
     * Gère la saisie des mots-clés
     */
    function handleKeywordsInput() {
        updateKeywordCounter();
        
        // Délai pour éviter trop d'appels
        clearTimeout(this.keywordTimeout);
        this.keywordTimeout = setTimeout(updateKeywordTags, 500);
    }

    /**
     * Met à jour le compteur de mots-clés
     */
    function updateKeywordCounter() {
        const keywords = $('#smartseo_ai_keywords').val().trim();
        const count = keywords ? keywords.split(',').filter(k => k.trim()).length : 0;
        $('#smartseo-ai-keywords-counter').text(count);
    }

    /**
     * Met à jour les tags des mots-clés
     */
    function updateKeywordTags() {
        const keywords = $('#smartseo_ai_keywords').val().trim();
        const $tagsContainer = $('#smartseo-ai-keywords-tags');
        
        $tagsContainer.empty();
        
        if (keywords) {
            const keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
            
            keywordArray.forEach(function(keyword, index) {
                const $tag = $('<span class="smartseo-ai-keyword-tag">' + 
                    '<span class="keyword-text">' + keyword + '</span>' +
                    '<span class="remove-tag" data-index="' + index + '">×</span>' +
                    '</span>');
                
                $tagsContainer.append($tag);
            });
        }
        
        updateKeywordCounter();
    }

    /**
     * Initialise les tags des mots-clés
     */
    function initKeywordTags() {
        updateKeywordTags();
        
        // Gestion de la suppression des tags
        $(document).on('click', '.remove-tag', function() {
            const index = $(this).data('index');
            const keywords = $('#smartseo_ai_keywords').val().split(',').map(k => k.trim()).filter(k => k);
            
            keywords.splice(index, 1);
            $('#smartseo_ai_keywords').val(keywords.join(', ')).trigger('blur');
        });
    }

    /**
     * Valide un champ
     */
    function validateField(element, min, max) {
        const $wrapper = $(element).closest('.smartseo-ai-field-wrapper');
        const length = element.value.length;
        
        $wrapper.removeClass('valid warning error');
        
        if (min && max) {
            if (length >= min && length <= max) {
                $wrapper.addClass('valid');
            } else if (length > max * 0.8) {
                $wrapper.addClass('warning');
            } else {
                $wrapper.addClass('error');
            }
        } else if (element.value.trim()) {
            $wrapper.addClass('valid');
        }
    }

    /**
     * Valide tous les champs
     */
    function validateFields() {
        validateField(document.getElementById('smartseo_ai_seo_title'), 50, 60);
        validateField(document.getElementById('smartseo_ai_meta_description'), 120, 160);
        validateField(document.getElementById('smartseo_ai_keywords'));
    }

    /**
     * Ouvre le sélecteur de média WordPress
     */
    function openMediaUploader() {
        // Si le sélecteur existe déjà, l'ouvrir
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        // Créer le sélecteur de média
        mediaUploader = wp.media({
            title: smartseoAiMetabox.i18n.selectImage,
            button: {
                text: smartseoAiMetabox.i18n.useThisImage
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });

        // Quand une image est sélectionnée
        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();

            // Mettre à jour le champ
            $('#smartseo_ai_og_image').val(attachment.url);

            // Ajouter/mettre à jour l'aperçu
            updateImagePreview(attachment.url);
        });

        // Ouvrir le sélecteur
        mediaUploader.open();
    }

    /**
     * Met à jour l'aperçu de l'image
     */
    function updateImagePreview(imageUrl) {
        let $preview = $('.smartseo-ai-image-preview');

        if ($preview.length === 0) {
            $preview = $('<div class="smartseo-ai-image-preview"></div>');
            $('#smartseo_ai_og_image').closest('.smartseo-ai-field-wrapper').append($preview);
        }

        $preview.html('<img src="' + imageUrl + '" alt="Aperçu image OG" />');
    }

    /**
     * Applique le slug optimisé
     */
    function applySlug() {
        const slug = $('#smartseo_ai_seo_slug').val().trim();

        if (!slug) {
            showNotification('warning', 'Veuillez saisir un slug avant de l\'appliquer.');
            return;
        }

        // Mettre à jour le slug WordPress si possible
        if ($('#post_name').length > 0) {
            $('#post_name').val(slug);
        }

        // Mettre à jour l'aperçu du permalien si disponible
        if (typeof(wp) !== 'undefined' && wp.permalink) {
            wp.permalink.updateSlug(slug);
        }

        showNotification('success', 'Slug appliqué avec succès !');
    }

    /**
     * Animation de succès
     */
    function animateSuccess() {
        $('.smartseo-ai-metabox-enhanced').addClass('success-animation');

        setTimeout(function() {
            $('.smartseo-ai-metabox-enhanced').removeClass('success-animation');
        }, 1000);
    }

    /**
     * Affiche une notification
     */
    function showNotification(type, message) {
        const $notification = $('<div class="smartseo-ai-notification notification-' + type + '">' + message + '</div>');

        $('body').append($notification);

        setTimeout(function() {
            $notification.addClass('show');
        }, 100);

        setTimeout(function() {
            $notification.removeClass('show');
            setTimeout(function() {
                $notification.remove();
            }, 300);
        }, 3000);
    }

    /**
     * Retourne la couleur pour un score SEO
     */
    function getScoreColor(score) {
        if (score >= 80) return '#4caf50';
        if (score >= 60) return '#2196f3';
        if (score >= 40) return '#ff9800';
        return '#f44336';
    }

    /**
     * Retourne la classe pour un score SEO
     */
    function getScoreClass(score) {
        if (score >= 80) return 'excellent';
        if (score >= 60) return 'good';
        if (score >= 40) return 'average';
        return 'poor';
    }

    /**
     * Retourne le label pour un score SEO
     */
    function getScoreLabel(score) {
        if (score >= 80) return 'Excellent';
        if (score >= 60) return 'Bon';
        if (score >= 40) return 'Moyen';
        return 'Faible';
    }

    // Styles pour les notifications
    const notificationStyles = `
        <style>
        .smartseo-ai-notification {
            position: fixed;
            top: 32px;
            right: 20px;
            z-index: 999999;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 300px;
        }

        .smartseo-ai-notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .smartseo-ai-notification.notification-success {
            background: #4caf50;
        }

        .smartseo-ai-notification.notification-error {
            background: #f44336;
        }

        .smartseo-ai-notification.notification-warning {
            background: #ff9800;
        }

        .smartseo-ai-notification.notification-info {
            background: #2196f3;
        }

        .smartseo-ai-metabox-enhanced.success-animation {
            animation: successPulse 1s ease;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
        </style>
    `;

    // Ajouter les styles
    $('head').append(notificationStyles);

})(jQuery);
