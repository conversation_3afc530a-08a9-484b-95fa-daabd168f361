<?php
/**
 * Classe pour l'analyse des liens
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'analyse des liens
 */
class SmartSEO_AI_Link_Analyzer {

    /**
     * Analyse les liens du contenu
     *
     * @param string $content Contenu à analyser.
     * @param int    $post_id ID de l'article.
     * @return array Résultats de l'analyse.
     */
    public function analyze( $content, $post_id = 0 ) {
        // Extraire tous les liens
        preg_match_all( '/<a[^>]+href=([\'"])(?<href>.+?)\1[^>]*>(?<text>.*?)<\/a>/i', $content, $matches );
        
        $links = array();
        $internal_links = 0;
        $external_links = 0;
        $nofollow_links = 0;
        $empty_anchors = 0;
        
        $site_url = get_site_url();
        $site_domain = parse_url( $site_url, PHP_URL_HOST );
        
        foreach ( $matches[0] as $i => $link ) {
            $href = $matches['href'][$i];
            $text = strip_tags( $matches['text'][$i] );
            
            // Vérifier si le lien est interne ou externe
            $is_internal = strpos( $href, $site_url ) === 0 || strpos( $href, '/' ) === 0;
            $is_external = ! $is_internal && strpos( $href, 'http' ) === 0;
            
            // Vérifier si le lien est nofollow
            $is_nofollow = strpos( $link, 'rel="nofollow"' ) !== false || strpos( $link, "rel='nofollow'" ) !== false;
            
            // Vérifier si l'ancre est vide
            $is_empty_anchor = empty( trim( $text ) );
            
            $links[] = array(
                'href' => $href,
                'text' => $text,
                'is_internal' => $is_internal,
                'is_external' => $is_external,
                'is_nofollow' => $is_nofollow,
                'is_empty_anchor' => $is_empty_anchor,
            );
            
            if ( $is_internal ) {
                $internal_links++;
            }
            
            if ( $is_external ) {
                $external_links++;
            }
            
            if ( $is_nofollow ) {
                $nofollow_links++;
            }
            
            if ( $is_empty_anchor ) {
                $empty_anchors++;
            }
        }
        
        // Analyser les textes d'ancrage
        $anchor_text_analysis = $this->analyze_anchor_texts( $links );
        
        // Analyser les liens internes
        $internal_links_analysis = $this->analyze_internal_links( $links, $post_id );
        
        // Analyser les liens externes
        $external_links_analysis = $this->analyze_external_links( $links );
        
        // Créer la liste des vérifications
        $checks = array(
            array(
                'label' => __( 'Liens internes', 'smartseo-ai' ),
                'status' => $internal_links_analysis['status'],
                'recommendation' => $internal_links_analysis['recommendation'],
            ),
            array(
                'label' => __( 'Liens externes', 'smartseo-ai' ),
                'status' => $external_links_analysis['status'],
                'recommendation' => $external_links_analysis['recommendation'],
            ),
            array(
                'label' => __( 'Textes d\'ancrage', 'smartseo-ai' ),
                'status' => $anchor_text_analysis['status'],
                'recommendation' => $anchor_text_analysis['recommendation'],
            ),
        );
        
        // Calculer le score
        $score = $this->calculate_score( $internal_links_analysis, $external_links_analysis, $anchor_text_analysis );
        
        // Retourner les résultats
        return array(
            'score' => $score,
            'total_links' => count( $links ),
            'internal_links' => $internal_links,
            'external_links' => $external_links,
            'nofollow_links' => $nofollow_links,
            'empty_anchors' => $empty_anchors,
            'internal_links_analysis' => $internal_links_analysis,
            'external_links_analysis' => $external_links_analysis,
            'anchor_text_analysis' => $anchor_text_analysis,
            'checks' => $checks,
        );
    }

    /**
     * Analyse les textes d'ancrage des liens
     *
     * @param array $links Liens à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_anchor_texts( $links ) {
        $generic_anchors = 0;
        $descriptive_anchors = 0;
        
        // Mots génériques à éviter dans les textes d'ancrage
        $generic_words = array(
            'cliquez ici', 'click here', 'lien', 'link', 'ici', 'here', 'voir', 'see',
            'en savoir plus', 'learn more', 'plus d\'infos', 'more info', 'lire la suite', 'read more',
        );
        
        foreach ( $links as $link ) {
            if ( $link['is_empty_anchor'] ) {
                $generic_anchors++;
                continue;
            }
            
            $text = strtolower( trim( $link['text'] ) );
            
            // Vérifier si le texte d'ancrage est générique
            $is_generic = false;
            foreach ( $generic_words as $word ) {
                if ( strpos( $text, $word ) !== false ) {
                    $is_generic = true;
                    break;
                }
            }
            
            if ( $is_generic || strlen( $text ) < 3 ) {
                $generic_anchors++;
            } else {
                $descriptive_anchors++;
            }
        }
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( count( $links ) === 0 ) {
            $recommendation = __( 'Aucun lien détecté. Ajoutez des liens internes et externes pour améliorer le SEO.', 'smartseo-ai' );
        } elseif ( $generic_anchors === 0 && $descriptive_anchors > 0 ) {
            $status = 'good';
            $recommendation = __( 'Excellents textes d\'ancrage. Tous les liens utilisent des textes d\'ancrage descriptifs.', 'smartseo-ai' );
        } elseif ( $descriptive_anchors > $generic_anchors ) {
            $status = 'average';
            $recommendation = __( 'Textes d\'ancrage acceptables. Remplacez les textes d\'ancrage génériques par des textes plus descriptifs.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'Textes d\'ancrage à améliorer. Évitez les textes génériques comme "cliquez ici" et utilisez des textes descriptifs.', 'smartseo-ai' );
        }
        
        return array(
            'generic_anchors' => $generic_anchors,
            'descriptive_anchors' => $descriptive_anchors,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Analyse les liens internes
     *
     * @param array $links   Liens à analyser.
     * @param int   $post_id ID de l'article.
     * @return array Résultats de l'analyse.
     */
    private function analyze_internal_links( $links, $post_id ) {
        $internal_links = array_filter( $links, function( $link ) {
            return $link['is_internal'];
        } );
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( count( $internal_links ) >= 3 ) {
            $status = 'good';
            $recommendation = __( 'Bon nombre de liens internes. Les liens internes aident à la navigation et au référencement.', 'smartseo-ai' );
        } elseif ( count( $internal_links ) >= 1 ) {
            $status = 'average';
            $recommendation = __( 'Nombre acceptable de liens internes. Envisagez d\'ajouter plus de liens internes vers des contenus pertinents.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'Aucun lien interne détecté. Ajoutez des liens vers d\'autres pages de votre site pour améliorer la navigation et le référencement.', 'smartseo-ai' );
        }
        
        // Si l'ID de l'article est fourni, suggérer des articles connexes
        if ( $post_id > 0 && count( $internal_links ) < 3 ) {
            $related_posts = $this->get_related_posts( $post_id );
            
            if ( ! empty( $related_posts ) ) {
                $recommendation .= ' ' . __( 'Voici quelques articles connexes que vous pourriez lier :', 'smartseo-ai' );
                
                foreach ( $related_posts as $related_post ) {
                    $recommendation .= ' "' . $related_post->post_title . '",';
                }
                
                $recommendation = rtrim( $recommendation, ',' ) . '.';
            }
        }
        
        return array(
            'count' => count( $internal_links ),
            'links' => $internal_links,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Analyse les liens externes
     *
     * @param array $links Liens à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_external_links( $links ) {
        $external_links = array_filter( $links, function( $link ) {
            return $link['is_external'];
        } );
        
        $nofollow_count = 0;
        foreach ( $external_links as $link ) {
            if ( $link['is_nofollow'] ) {
                $nofollow_count++;
            }
        }
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( count( $external_links ) >= 1 ) {
            if ( $nofollow_count === count( $external_links ) ) {
                $status = 'average';
                $recommendation = __( 'Tous les liens externes sont en nofollow. Envisagez d\'ajouter quelques liens dofollow vers des sources fiables.', 'smartseo-ai' );
            } elseif ( $nofollow_count > 0 ) {
                $status = 'good';
                $recommendation = __( 'Bon équilibre de liens externes dofollow et nofollow. Les liens externes vers des sources fiables améliorent la crédibilité.', 'smartseo-ai' );
            } else {
                $status = 'average';
                $recommendation = __( 'Liens externes présents, mais aucun n\'est en nofollow. Envisagez d\'ajouter l\'attribut nofollow aux liens commerciaux ou non fiables.', 'smartseo-ai' );
            }
        } else {
            $recommendation = __( 'Aucun lien externe détecté. Ajoutez des liens vers des sources fiables pour améliorer la crédibilité et le référencement.', 'smartseo-ai' );
        }
        
        return array(
            'count' => count( $external_links ),
            'nofollow_count' => $nofollow_count,
            'links' => $external_links,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Récupère les articles connexes
     *
     * @param int $post_id ID de l'article.
     * @return array Articles connexes.
     */
    private function get_related_posts( $post_id ) {
        // Récupérer les catégories de l'article
        $categories = wp_get_post_categories( $post_id );
        
        if ( empty( $categories ) ) {
            return array();
        }
        
        // Récupérer les articles de la même catégorie
        $args = array(
            'category__in' => $categories,
            'post__not_in' => array( $post_id ),
            'posts_per_page' => 3,
            'post_status' => 'publish',
        );
        
        $query = new WP_Query( $args );
        
        return $query->posts;
    }

    /**
     * Calcule le score global de l'analyse de liens
     *
     * @param array $internal_links_analysis Résultats de l'analyse des liens internes.
     * @param array $external_links_analysis Résultats de l'analyse des liens externes.
     * @param array $anchor_text_analysis    Résultats de l'analyse des textes d'ancrage.
     * @return int Score (0-100).
     */
    private function calculate_score( $internal_links_analysis, $external_links_analysis, $anchor_text_analysis ) {
        // Pondération des différents facteurs
        $weights = array(
            'internal_links' => 0.4,
            'external_links' => 0.3,
            'anchor_text' => 0.3,
        );
        
        // Convertir les statuts en scores numériques
        $scores = array(
            'internal_links' => $this->status_to_score( $internal_links_analysis['status'] ),
            'external_links' => $this->status_to_score( $external_links_analysis['status'] ),
            'anchor_text' => $this->status_to_score( $anchor_text_analysis['status'] ),
        );
        
        // Calculer le score pondéré
        $weighted_score = 
            $scores['internal_links'] * $weights['internal_links'] +
            $scores['external_links'] * $weights['external_links'] +
            $scores['anchor_text'] * $weights['anchor_text'];
        
        // Arrondir le score
        return round( $weighted_score );
    }

    /**
     * Convertit un statut en score numérique
     *
     * @param string $status Statut (good, average, poor).
     * @return int Score (0-100).
     */
    private function status_to_score( $status ) {
        switch ( $status ) {
            case 'good':
                return 100;
            case 'average':
                return 50;
            case 'poor':
                return 0;
            default:
                return 0;
        }
    }
}
