<?php
/**
 * Vue des paramètres de la meta box SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Traitement du formulaire
if ( isset( $_POST['submit'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'smartseo_ai_metabox_settings' ) ) {
    // Sauvegarder les paramètres
    update_option( 'smartseo_ai_enhanced_metabox', isset( $_POST['enhanced_metabox'] ) );
    update_option( 'smartseo_ai_metabox_position', sanitize_text_field( $_POST['metabox_position'] ) );
    update_option( 'smartseo_ai_metabox_priority', sanitize_text_field( $_POST['metabox_priority'] ) );
    update_option( 'smartseo_ai_auto_optimize', isset( $_POST['auto_optimize'] ) );
    update_option( 'smartseo_ai_show_score_in_list', isset( $_POST['show_score_in_list'] ) );
    update_option( 'smartseo_ai_enable_og_fields', isset( $_POST['enable_og_fields'] ) );
    update_option( 'smartseo_ai_enable_advanced_fields', isset( $_POST['enable_advanced_fields'] ) );
    
    echo '<div class="notice notice-success is-dismissible"><p>' . __( 'Paramètres de la meta box sauvegardés avec succès !', 'smartseo-ai' ) . '</p></div>';
}

// Récupérer les paramètres actuels
$enhanced_metabox = get_option( 'smartseo_ai_enhanced_metabox', true );
$metabox_position = get_option( 'smartseo_ai_metabox_position', 'side' );
$metabox_priority = get_option( 'smartseo_ai_metabox_priority', 'high' );
$auto_optimize = get_option( 'smartseo_ai_auto_optimize', false );
$show_score_in_list = get_option( 'smartseo_ai_show_score_in_list', true );
$enable_og_fields = get_option( 'smartseo_ai_enable_og_fields', true );
$enable_advanced_fields = get_option( 'smartseo_ai_enable_advanced_fields', true );
?>

<div class="wrap">
    <h1><?php _e( 'Paramètres de la Meta Box SEO', 'smartseo-ai' ); ?></h1>
    
    <form method="post" action="">
        <?php wp_nonce_field( 'smartseo_ai_metabox_settings' ); ?>
        
        <table class="form-table">
            <tbody>
                <!-- Interface utilisateur -->
                <tr>
                    <th scope="row">
                        <label for="enhanced_metabox"><?php _e( 'Interface de la meta box', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="enhanced_metabox" 
                                       id="enhanced_metabox" 
                                       value="1" 
                                       <?php checked( $enhanced_metabox ); ?>>
                                <?php _e( 'Utiliser l\'interface améliorée avec design moderne', 'smartseo-ai' ); ?>
                            </label>
                            <p class="description">
                                <?php _e( 'L\'interface améliorée offre un design moderne, des animations fluides et une meilleure expérience utilisateur.', 'smartseo-ai' ); ?>
                            </p>
                        </fieldset>
                    </td>
                </tr>

                <!-- Position de la meta box -->
                <tr>
                    <th scope="row">
                        <label for="metabox_position"><?php _e( 'Position de la meta box', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <select name="metabox_position" id="metabox_position">
                            <option value="normal" <?php selected( $metabox_position, 'normal' ); ?>>
                                <?php _e( 'Zone principale (sous l\'éditeur)', 'smartseo-ai' ); ?>
                            </option>
                            <option value="side" <?php selected( $metabox_position, 'side' ); ?>>
                                <?php _e( 'Barre latérale', 'smartseo-ai' ); ?>
                            </option>
                            <option value="advanced" <?php selected( $metabox_position, 'advanced' ); ?>>
                                <?php _e( 'Zone avancée (en bas)', 'smartseo-ai' ); ?>
                            </option>
                        </select>
                        <p class="description">
                            <?php _e( 'Choisissez où afficher la meta box dans l\'éditeur d\'articles.', 'smartseo-ai' ); ?>
                        </p>
                    </td>
                </tr>

                <!-- Priorité de la meta box -->
                <tr>
                    <th scope="row">
                        <label for="metabox_priority"><?php _e( 'Priorité d\'affichage', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <select name="metabox_priority" id="metabox_priority">
                            <option value="high" <?php selected( $metabox_priority, 'high' ); ?>>
                                <?php _e( 'Haute (en haut)', 'smartseo-ai' ); ?>
                            </option>
                            <option value="core" <?php selected( $metabox_priority, 'core' ); ?>>
                                <?php _e( 'Normale', 'smartseo-ai' ); ?>
                            </option>
                            <option value="default" <?php selected( $metabox_priority, 'default' ); ?>>
                                <?php _e( 'Par défaut', 'smartseo-ai' ); ?>
                            </option>
                            <option value="low" <?php selected( $metabox_priority, 'low' ); ?>>
                                <?php _e( 'Basse (en bas)', 'smartseo-ai' ); ?>
                            </option>
                        </select>
                        <p class="description">
                            <?php _e( 'Ordre d\'affichage de la meta box par rapport aux autres.', 'smartseo-ai' ); ?>
                        </p>
                    </td>
                </tr>

                <!-- Optimisation automatique -->
                <tr>
                    <th scope="row">
                        <label for="auto_optimize"><?php _e( 'Optimisation automatique', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="auto_optimize" 
                                       id="auto_optimize" 
                                       value="1" 
                                       <?php checked( $auto_optimize ); ?>>
                                <?php _e( 'Optimiser automatiquement lors de la publication', 'smartseo-ai' ); ?>
                            </label>
                            <p class="description">
                                <?php _e( 'Lance automatiquement l\'optimisation SEO lors de la première publication d\'un article.', 'smartseo-ai' ); ?>
                            </p>
                        </fieldset>
                    </td>
                </tr>

                <!-- Affichage du score dans la liste -->
                <tr>
                    <th scope="row">
                        <label for="show_score_in_list"><?php _e( 'Score dans la liste des articles', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="show_score_in_list" 
                                       id="show_score_in_list" 
                                       value="1" 
                                       <?php checked( $show_score_in_list ); ?>>
                                <?php _e( 'Afficher le score SEO dans la liste des articles', 'smartseo-ai' ); ?>
                            </label>
                            <p class="description">
                                <?php _e( 'Ajoute une colonne avec le score SEO dans la liste des articles de l\'administration.', 'smartseo-ai' ); ?>
                            </p>
                        </fieldset>
                    </td>
                </tr>

                <!-- Champs Open Graph -->
                <tr>
                    <th scope="row">
                        <label for="enable_og_fields"><?php _e( 'Champs Open Graph', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="enable_og_fields" 
                                       id="enable_og_fields" 
                                       value="1" 
                                       <?php checked( $enable_og_fields ); ?>>
                                <?php _e( 'Afficher les champs Open Graph (réseaux sociaux)', 'smartseo-ai' ); ?>
                            </label>
                            <p class="description">
                                <?php _e( 'Permet de personnaliser l\'apparence du contenu lors du partage sur les réseaux sociaux.', 'smartseo-ai' ); ?>
                            </p>
                        </fieldset>
                    </td>
                </tr>

                <!-- Champs avancés -->
                <tr>
                    <th scope="row">
                        <label for="enable_advanced_fields"><?php _e( 'Champs avancés', 'smartseo-ai' ); ?></label>
                    </th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="enable_advanced_fields" 
                                       id="enable_advanced_fields" 
                                       value="1" 
                                       <?php checked( $enable_advanced_fields ); ?>>
                                <?php _e( 'Afficher les champs avancés (slug, etc.)', 'smartseo-ai' ); ?>
                            </label>
                            <p class="description">
                                <?php _e( 'Affiche des options supplémentaires pour les utilisateurs expérimentés.', 'smartseo-ai' ); ?>
                            </p>
                        </fieldset>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Aperçu de l'interface -->
        <h2><?php _e( 'Aperçu de l\'interface', 'smartseo-ai' ); ?></h2>
        <div class="interface-preview">
            <div class="preview-card" id="classic-preview">
                <h3><?php _e( 'Interface classique', 'smartseo-ai' ); ?></h3>
                <div class="preview-image">
                    <div class="classic-mockup">
                        <div class="classic-header">SmartSEO AI</div>
                        <div class="classic-button">Optimiser avec l'IA</div>
                        <div class="classic-fields">
                            <div class="classic-field">Titre SEO</div>
                            <div class="classic-field">Meta Description</div>
                            <div class="classic-field">Mots-clés</div>
                        </div>
                    </div>
                </div>
                <p><?php _e( 'Interface simple et fonctionnelle', 'smartseo-ai' ); ?></p>
            </div>

            <div class="preview-card" id="enhanced-preview">
                <h3><?php _e( 'Interface améliorée', 'smartseo-ai' ); ?></h3>
                <div class="preview-image">
                    <div class="enhanced-mockup">
                        <div class="enhanced-header">
                            <div class="enhanced-button">✨ Optimiser avec l'IA</div>
                            <div class="enhanced-score">85/100</div>
                        </div>
                        <div class="enhanced-fields">
                            <div class="enhanced-field">📝 Titre SEO <span class="counter">45/60</span></div>
                            <div class="enhanced-field">📄 Meta Description <span class="counter">120/160</span></div>
                            <div class="enhanced-field">🔍 Mots-clés <span class="tags">3 mots-clés</span></div>
                        </div>
                    </div>
                </div>
                <p><?php _e( 'Design moderne avec animations et feedback visuel', 'smartseo-ai' ); ?></p>
            </div>
        </div>

        <?php submit_button( __( 'Sauvegarder les paramètres', 'smartseo-ai' ) ); ?>
    </form>
</div>

<style>
.interface-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.preview-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.preview-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.preview-card h3 {
    margin-top: 0;
    color: #0073aa;
    text-align: center;
}

.preview-image {
    margin: 15px 0;
    text-align: center;
}

.classic-mockup,
.enhanced-mockup {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #f9f9f9;
    max-width: 250px;
    margin: 0 auto;
}

.classic-header {
    background: #0073aa;
    color: white;
    padding: 8px;
    text-align: center;
    font-weight: bold;
    margin-bottom: 10px;
}

.classic-button {
    background: #0073aa;
    color: white;
    padding: 6px 12px;
    border-radius: 3px;
    text-align: center;
    margin-bottom: 10px;
    font-size: 12px;
}

.classic-fields {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.classic-field {
    background: white;
    border: 1px solid #ddd;
    padding: 6px;
    font-size: 11px;
}

.enhanced-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.enhanced-button {
    font-size: 11px;
    font-weight: bold;
}

.enhanced-score {
    background: rgba(255,255,255,0.2);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
}

.enhanced-fields {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.enhanced-field {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
    font-size: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.counter,
.tags {
    background: #0073aa;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 9px;
}

.preview-card p {
    text-align: center;
    margin-bottom: 0;
    font-style: italic;
    color: #666;
}

#enhanced-preview.active {
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}

#classic-preview.active {
    border-color: #666;
    box-shadow: 0 0 0 2px rgba(102, 102, 102, 0.2);
}
</style>

<script>
jQuery(document).ready(function($) {
    // Mettre à jour l'aperçu selon la sélection
    function updatePreview() {
        const isEnhanced = $('#enhanced_metabox').is(':checked');
        
        $('.preview-card').removeClass('active');
        
        if (isEnhanced) {
            $('#enhanced-preview').addClass('active');
        } else {
            $('#classic-preview').addClass('active');
        }
    }
    
    // Initialiser l'aperçu
    updatePreview();
    
    // Écouter les changements
    $('#enhanced_metabox').on('change', updatePreview);
});
</script>
