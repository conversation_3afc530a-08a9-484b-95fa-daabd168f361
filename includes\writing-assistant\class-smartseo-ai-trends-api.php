<?php
/**
 * Classe pour l'intégration avec l'API Google Trends
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'intégration avec l'API Google Trends
 */
class SmartSEO_AI_Trends_API {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Récupère les tendances pour un mot-clé
     *
     * @param string $keyword Mot-clé à analyser.
     * @return array|WP_Error Tendances ou erreur.
     */
    public function get_trends( $keyword ) {
        // Vérifier si l'API Google Trends est configurée
        $api_key = get_option( 'smartseo_ai_google_trends_api_key', '' );
        
        if ( ! empty( $api_key ) ) {
            // Utiliser l'API Google Trends si configurée
            return $this->get_trends_from_api( $keyword, $api_key );
        } else {
            // Utiliser l'IA pour générer des tendances simulées
            return $this->get_trends_from_ai( $keyword );
        }
    }

    /**
     * Récupère les tendances depuis l'API Google Trends
     *
     * @param string $keyword Mot-clé à analyser.
     * @param string $api_key Clé API Google Trends.
     * @return array|WP_Error Tendances ou erreur.
     */
    private function get_trends_from_api( $keyword, $api_key ) {
        // Note: Google Trends n'a pas d'API officielle, donc ceci est une simulation
        // Dans un cas réel, vous devriez utiliser une API tierce ou scraper les données
        
        // Simuler une requête API
        $url = 'https://example.com/api/trends?keyword=' . urlencode( $keyword ) . '&api_key=' . $api_key;
        
        $response = wp_remote_get( $url );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );
        
        if ( ! $data ) {
            return new WP_Error( 'invalid_response', __( 'Réponse invalide de l\'API Google Trends.', 'smartseo-ai' ) );
        }
        
        return $data;
    }

    /**
     * Génère des tendances simulées avec l'IA
     *
     * @param string $keyword Mot-clé à analyser.
     * @return array|WP_Error Tendances simulées ou erreur.
     */
    private function get_trends_from_ai( $keyword ) {
        // Générer le prompt pour l'IA
        $prompt = $this->build_prompt(
            "Analyse le mot-clé suivant et génère des sujets tendances associés : $keyword. " .
            "Fournis 5 sujets tendances liés à ce mot-clé, avec pour chacun : " .
            "le sujet, une estimation de la popularité (élevée, moyenne, faible), " .
            "une estimation de la saisonnalité (toute l'année, saisonnier, événementiel), " .
            "et une brève explication de la tendance. " .
            "Réponds au format JSON avec les champs suivants : " .
            "'trending_topics' (tableau d'objets avec 'topic', 'popularity', 'seasonality', 'explanation')."
        );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Essayer de parser la réponse JSON
        $json_response = $this->extract_json_from_response( $response );
        
        if ( $json_response ) {
            return $json_response;
        }

        // Si la réponse n'est pas un JSON valide, essayer de la structurer manuellement
        return $this->format_non_json_response( $response, $keyword );
    }

    /**
     * Construit un prompt pour l'IA
     *
     * @param string $instruction Instruction pour l'IA.
     * @return string Prompt pour l'IA.
     */
    private function build_prompt( $instruction ) {
        $prompt = $instruction . "\n\n";
        $prompt .= "Réponds en français. Sois précis et détaillé.";

        return $prompt;
    }

    /**
     * Extrait le JSON de la réponse de l'IA
     *
     * @param string $response Réponse de l'IA.
     * @return array|false Données JSON ou false si invalide.
     */
    private function extract_json_from_response( $response ) {
        // D'abord, essayer de parser directement la réponse comme JSON
        $data = json_decode( $response, true );
        if ( json_last_error() === JSON_ERROR_NONE ) {
            return $data;
        }
        
        // Essayer de trouver un objet JSON dans la réponse
        preg_match( '/\{.*\}/s', $response, $matches );
        
        if ( ! empty( $matches[0] ) ) {
            $json = $matches[0];
            $data = json_decode( $json, true );
            
            if ( json_last_error() === JSON_ERROR_NONE ) {
                return $data;
            }
        }
        
        return false;
    }

    /**
     * Formate une réponse non-JSON
     *
     * @param string $response Réponse de l'IA.
     * @param string $keyword  Mot-clé analysé.
     * @return array Réponse formatée.
     */
    private function format_non_json_response( $response, $keyword ) {
        // Essayer de structurer la réponse en fonction des patterns courants
        $trending_topics = array();
        
        // Rechercher les sujets tendances
        if ( preg_match_all( '/(\d+\.\s*|-)([^:]+):/i', $response, $matches ) ) {
            foreach ( $matches[2] as $index => $topic ) {
                $topic = trim( $topic );
                
                // Extraire l'explication
                $explanation = '';
                if ( preg_match( '/' . preg_quote( $topic, '/' ) . ':[^:]*?([^\.]+)/i', $response, $exp_match ) ) {
                    $explanation = trim( $exp_match[1] );
                }
                
                $trending_topics[] = array(
                    'topic' => $topic,
                    'popularity' => $this->get_random_popularity(),
                    'seasonality' => $this->get_random_seasonality(),
                    'explanation' => $explanation ?: "Sujet tendance lié à $keyword",
                );
            }
        }
        
        // Si aucun sujet n'a été trouvé, créer des sujets par défaut
        if ( empty( $trending_topics ) ) {
            $trending_topics = array(
                array(
                    'topic' => "$keyword actualités",
                    'popularity' => 'élevée',
                    'seasonality' => 'toute l\'année',
                    'explanation' => "Actualités récentes concernant $keyword",
                ),
                array(
                    'topic' => "$keyword tutoriel",
                    'popularity' => 'moyenne',
                    'seasonality' => 'toute l\'année',
                    'explanation' => "Guides et tutoriels sur $keyword",
                ),
                array(
                    'topic' => "$keyword comparatif",
                    'popularity' => 'moyenne',
                    'seasonality' => 'toute l\'année',
                    'explanation' => "Comparaisons de différents aspects de $keyword",
                ),
                array(
                    'topic' => "$keyword prix",
                    'popularity' => 'élevée',
                    'seasonality' => 'toute l\'année',
                    'explanation' => "Informations sur les prix liés à $keyword",
                ),
                array(
                    'topic' => "$keyword avis",
                    'popularity' => 'élevée',
                    'seasonality' => 'toute l\'année',
                    'explanation' => "Avis et témoignages sur $keyword",
                ),
            );
        }
        
        // Limiter à 5 sujets
        $trending_topics = array_slice( $trending_topics, 0, 5 );
        
        // Créer une structure de réponse
        return array(
            'trending_topics' => $trending_topics,
        );
    }

    /**
     * Génère une popularité aléatoire
     *
     * @return string Popularité (élevée, moyenne, faible).
     */
    private function get_random_popularity() {
        $popularities = array( 'élevée', 'moyenne', 'faible' );
        return $popularities[array_rand( $popularities )];
    }

    /**
     * Génère une saisonnalité aléatoire
     *
     * @return string Saisonnalité (toute l'année, saisonnier, événementiel).
     */
    private function get_random_seasonality() {
        $seasonalities = array( 'toute l\'année', 'saisonnier', 'événementiel' );
        return $seasonalities[array_rand( $seasonalities )];
    }
}
