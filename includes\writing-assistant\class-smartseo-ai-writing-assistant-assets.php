<?php
/**
 * Classe de gestion des assets pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les assets de l'Assistant de Rédaction SEO
 */
class SmartSEO_AI_Writing_Assistant_Assets {

    /**
     * Initialise le gestionnaire des assets
     */
    public function init() {
        // Ajouter les hooks
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
    }

    /**
     * Enregistre et charge les scripts et styles pour l'admin
     *
     * @param string $hook_suffix Suffixe du hook.
     */
    public function enqueue_admin_scripts( $hook_suffix ) {
        // Vérifier si nous sommes sur une page d'édition
        if ( ! in_array( $hook_suffix, array( 'post.php', 'post-new.php' ), true ) ) {
            return;
        }

        // Enregistrer les styles
        wp_register_style(
            'smartseo-ai-writing-assistant',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-writing-assistant.css',
            array(),
            SMARTSEO_AI_VERSION
        );

        // Enregistrer les styles pour forcer l'affichage
        wp_register_style(
            'smartseo-ai-force-display',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-force-display.css',
            array('smartseo-ai-writing-assistant'),
            SMARTSEO_AI_VERSION
        );

        // Enregistrer les styles pour les tests
        wp_register_style(
            'smartseo-ai-test',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-test.css',
            array('smartseo-ai-writing-assistant'),
            SMARTSEO_AI_VERSION
        );

        // Enregistrer les styles spécifiques pour l'éditeur classique
        wp_register_style(
            'smartseo-ai-classic-editor',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-classic-editor.css',
            array('smartseo-ai-writing-assistant'),
            SMARTSEO_AI_VERSION
        );

        // Enregistrer les styles spécifiques pour la métabox
        wp_register_style(
            'smartseo-ai-metabox',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-metabox.css',
            array('smartseo-ai-writing-assistant'),
            SMARTSEO_AI_VERSION
        );

        // Enregistrer les styles spécifiques pour l'éditeur Gutenberg
        wp_register_style(
            'smartseo-ai-gutenberg-editor',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-gutenberg-editor.css',
            array('smartseo-ai-writing-assistant'),
            SMARTSEO_AI_VERSION
        );

        // Enregistrer les styles spécifiques pour la barre latérale de l'éditeur Gutenberg
        wp_register_style(
            'smartseo-ai-gutenberg-sidebar',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-gutenberg-sidebar.css',
            array('smartseo-ai-writing-assistant', 'smartseo-ai-gutenberg-editor'),
            SMARTSEO_AI_VERSION
        );

        // Enregistrer les scripts
        $this->register_scripts();

        // Charger les styles
        wp_enqueue_style( 'smartseo-ai-writing-assistant' );
        wp_enqueue_style( 'smartseo-ai-force-display' );

        // Charger les styles de test en mode développement
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            wp_enqueue_style( 'smartseo-ai-test' );
        }

        // Charger les styles et scripts spécifiques pour l'éditeur
        if ( $this->is_gutenberg_editor() ) {
            // Charger les styles spécifiques pour l'éditeur Gutenberg
            wp_enqueue_style( 'smartseo-ai-gutenberg-editor' );
            wp_enqueue_style( 'smartseo-ai-gutenberg-sidebar' );

            // Charger le script d'initialisation pour l'éditeur Gutenberg
            wp_enqueue_script(
                'smartseo-ai-gutenberg-editor-init',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-gutenberg-editor-init.js',
                array( 'jquery', 'smartseo-ai-namespace', 'smartseo-ai-writing-assistant', 'wp-data', 'wp-editor', 'wp-plugins', 'wp-edit-post' ),
                SMARTSEO_AI_VERSION,
                true
            );
        } else {
            // Charger les styles spécifiques pour l'éditeur classique
            wp_enqueue_style( 'smartseo-ai-classic-editor' );
            wp_enqueue_style( 'smartseo-ai-metabox' );

            // Charger le script d'initialisation pour l'éditeur classique
            wp_enqueue_script(
                'smartseo-ai-classic-editor-init',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-classic-editor-init.js',
                array( 'jquery', 'smartseo-ai-namespace', 'smartseo-ai-writing-assistant' ),
                SMARTSEO_AI_VERSION,
                true
            );

            // Charger le script d'initialisation pour la métabox
            wp_enqueue_script(
                'smartseo-ai-metabox-init',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-metabox-init.js',
                array( 'jquery', 'smartseo-ai-namespace', 'smartseo-ai-writing-assistant' ),
                SMARTSEO_AI_VERSION,
                true
            );
        }

        // Charger les scripts
        wp_enqueue_script( 'smartseo-ai-namespace' );
        wp_enqueue_script( 'smartseo-ai-writing-assistant' );

        // Charger le script d'initialisation principal
        wp_enqueue_script(
            'smartseo-ai-init',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-init.js',
            array( 'jquery', 'smartseo-ai-namespace', 'smartseo-ai-writing-assistant' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Charger le script d'initialisation de l'interface utilisateur
        wp_enqueue_script(
            'smartseo-ai-ui-init',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-ui-init.js',
            array( 'jquery', 'smartseo-ai-namespace', 'smartseo-ai-writing-assistant' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Charger le script de débogage en mode développement
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            wp_enqueue_script(
                'smartseo-ai-debug',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-debug.js',
                array( 'jquery', 'smartseo-ai-namespace', 'smartseo-ai-writing-assistant', 'smartseo-ai-ui-init' ),
                SMARTSEO_AI_VERSION,
                true
            );

            // Charger le script de test
            wp_enqueue_script(
                'smartseo-ai-test',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-test.js',
                array( 'jquery', 'smartseo-ai-namespace', 'smartseo-ai-writing-assistant', 'smartseo-ai-ui-init', 'smartseo-ai-debug' ),
                SMARTSEO_AI_VERSION,
                true
            );

            // Charger le script de détection JSX
            wp_enqueue_script(
                'smartseo-ai-jsx-detector',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-jsx-detector.js',
                array( 'jquery', 'smartseo-ai-namespace', 'smartseo-ai-writing-assistant', 'smartseo-ai-ui-init', 'smartseo-ai-debug' ),
                SMARTSEO_AI_VERSION,
                true
            );

            // Charger le script de correction SVG
            wp_enqueue_script(
                'smartseo-ai-svg-fixer',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-svg-fixer.js',
                array( 'jquery', 'smartseo-ai-namespace', 'smartseo-ai-writing-assistant', 'smartseo-ai-ui-init', 'smartseo-ai-debug' ),
                SMARTSEO_AI_VERSION,
                true
            );
        }

        // Localiser les scripts
        $this->localize_scripts();

        // Ajouter un script inline pour forcer l'initialisation
        wp_add_inline_script( 'smartseo-ai-writing-assistant', '
            jQuery(document).ready(function($) {
                // Attendre que tous les scripts soient chargés
                setTimeout(function() {
                    if (window.SmartSEOAI && window.SmartSEOAI.UIManager) {
                        console.log("SmartSEO AI: Initialisation forcée de l\'interface");
                        window.SmartSEOAI.UIManager.init();
                    }
                }, 1000);
            });
        ' );
    }

    /**
     * Enregistre les scripts
     */
    private function register_scripts() {
        // Enregistrer le script de namespace global
        wp_register_script(
            'smartseo-ai-namespace',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-namespace.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );
        // Enregistrer les modules UI
        $this->register_ui_modules();

        // Enregistrer les modules fonctionnels
        $this->register_functional_modules();

        // Enregistrer le script principal
        wp_register_script(
            'smartseo-ai-writing-assistant',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-writing-assistant.js',
            array(
                'jquery',
                'smartseo-ai-namespace',
                'smartseo-ai-ui-manager',
                'smartseo-ai-content-generator',
                'smartseo-ai-keyword-analyzer',
                'smartseo-ai-live-analyzer',
                'smartseo-ai-trends-manager',
            ),
            SMARTSEO_AI_VERSION,
            true
        );
    }

    /**
     * Enregistre les modules UI
     */
    private function register_ui_modules() {
        // Enregistrer les modules UI de base
        wp_register_script(
            'smartseo-ai-notification-ui',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/notification-ui.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-animation-ui',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/animation-ui.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-modal-ui',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/modal-ui.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Enregistrer les modules UI pour Gutenberg
        wp_register_script(
            'smartseo-ai-gutenberg-content-panel',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/gutenberg-content-panel.js',
            array( 'jquery', 'wp-element', 'wp-components', 'wp-i18n' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-gutenberg-keyword-panel',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/gutenberg-keyword-panel.js',
            array( 'jquery', 'wp-element', 'wp-components', 'wp-i18n' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-gutenberg-analysis-panel',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/gutenberg-analysis-panel.js',
            array( 'jquery', 'wp-element', 'wp-components', 'wp-i18n' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-gutenberg-trends-panel',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/gutenberg-trends-panel.js',
            array( 'jquery', 'wp-element', 'wp-components', 'wp-i18n' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-gutenberg-sidebar',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/gutenberg-sidebar.js',
            array(
                'jquery',
                'wp-element',
                'wp-components',
                'wp-i18n',
                'wp-plugins',
                'wp-edit-post',
                'wp-data',
                'smartseo-ai-gutenberg-content-panel',
                'smartseo-ai-gutenberg-keyword-panel',
                'smartseo-ai-gutenberg-analysis-panel',
                'smartseo-ai-gutenberg-trends-panel',
            ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Enregistrer les modules UI pour l'éditeur classique
        wp_register_script(
            'smartseo-ai-classic-content-panel',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/classic-content-panel.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-classic-keyword-panel',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/classic-keyword-panel.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-classic-analysis-panel',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/classic-analysis-panel.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-classic-trends-panel',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/classic-trends-panel.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_register_script(
            'smartseo-ai-classic-editor-sidebar',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/classic-editor-sidebar.js',
            array(
                'jquery',
                'smartseo-ai-classic-content-panel',
                'smartseo-ai-classic-keyword-panel',
                'smartseo-ai-classic-analysis-panel',
                'smartseo-ai-classic-trends-panel',
            ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Enregistrer le gestionnaire de barre latérale
        wp_register_script(
            'smartseo-ai-sidebar-ui',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui/sidebar-ui.js',
            array(
                'jquery',
                'smartseo-ai-gutenberg-sidebar',
                'smartseo-ai-classic-editor-sidebar',
            ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Enregistrer le gestionnaire d'interface utilisateur
        wp_register_script(
            'smartseo-ai-ui-manager',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/ui-manager.js',
            array(
                'jquery',
                'smartseo-ai-sidebar-ui',
                'smartseo-ai-notification-ui',
                'smartseo-ai-animation-ui',
                'smartseo-ai-modal-ui',
            ),
            SMARTSEO_AI_VERSION,
            true
        );
    }

    /**
     * Enregistre les modules fonctionnels
     */
    private function register_functional_modules() {
        // Enregistrer le générateur de contenu
        wp_register_script(
            'smartseo-ai-content-generator',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/content-generator.js',
            array( 'jquery', 'smartseo-ai-ui-manager' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Enregistrer l'analyseur de mots-clés
        wp_register_script(
            'smartseo-ai-keyword-analyzer',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/keyword-analyzer.js',
            array( 'jquery', 'smartseo-ai-ui-manager' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Enregistrer l'analyseur en temps réel
        wp_register_script(
            'smartseo-ai-live-analyzer',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/live-analyzer.js',
            array( 'jquery', 'smartseo-ai-ui-manager' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Enregistrer le gestionnaire de tendances
        wp_register_script(
            'smartseo-ai-trends-manager',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/modules/trends-manager.js',
            array( 'jquery', 'smartseo-ai-ui-manager' ),
            SMARTSEO_AI_VERSION,
            true
        );
    }

    /**
     * Localise les scripts
     */
    private function localize_scripts() {
        // Localiser le script principal
        wp_localize_script(
            'smartseo-ai-writing-assistant',
            'smartseoAiWritingAssistant',
            array(
                'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'smartseo_ai_writing_assistant_nonce' ),
                'isGutenberg' => $this->is_gutenberg_editor(),
                'i18n' => array(
                    'generating' => __( 'Génération en cours...', 'smartseo-ai' ),
                    'analyzing' => __( 'Analyse en cours...', 'smartseo-ai' ),
                    'optimizing' => __( 'Optimisation en cours...', 'smartseo-ai' ),
                    'checking' => __( 'Vérification en cours...', 'smartseo-ai' ),
                    'getting_trends' => __( 'Récupération des tendances...', 'smartseo-ai' ),
                    'searching_topics' => __( 'Recherche de sujets connexes...', 'smartseo-ai' ),
                    'success' => __( 'Opération réussie !', 'smartseo-ai' ),
                    'error' => __( 'Une erreur est survenue', 'smartseo-ai' ),
                    'no_keyword' => __( 'Veuillez entrer un mot-clé', 'smartseo-ai' ),
                    'copied' => __( 'Copié dans le presse-papier !', 'smartseo-ai' ),
                    'keyword_updated' => __( 'Mot-clé principal mis à jour.', 'smartseo-ai' ),
                    'select_content_type' => __( 'Veuillez sélectionner un type de contenu à générer dans l\'onglet "Contenu".', 'smartseo-ai' ),
                ),
            )
        );
    }

    /**
     * Vérifie si l'éditeur Gutenberg est actif
     *
     * @return boolean Vrai si l'éditeur Gutenberg est actif
     */
    private function is_gutenberg_editor() {
        // Vérifier si nous sommes dans l'éditeur de blocs
        if ( function_exists( 'is_gutenberg_page' ) && is_gutenberg_page() ) {
            return true;
        }

        // Vérifier si l'éditeur de blocs est activé
        if ( function_exists( 'use_block_editor_for_post' ) && use_block_editor_for_post( get_post() ) ) {
            return true;
        }

        // Vérifier si l'éditeur de blocs est activé via le filtre
        if ( has_filter( 'replace_editor', 'gutenberg_init' ) ) {
            return true;
        }

        // Vérifier si l'éditeur de blocs est activé via le script
        global $wp_scripts;
        if ( $wp_scripts && isset( $wp_scripts->registered['wp-editor'] ) ) {
            return true;
        }

        return false;
    }
}
