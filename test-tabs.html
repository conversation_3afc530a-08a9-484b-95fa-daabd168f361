<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Onglets SmartSEO AI</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* Variables CSS */
        :root {
            --smartseo-primary: #0073aa;
            --smartseo-gray-light: #f8f9fa;
            --smartseo-gray-medium: #e9ecef;
            --smartseo-gray-dark: #6c757d;
            --smartseo-border-radius: 8px;
            --smartseo-transition: all 0.3s ease;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 20px;
            background: #f1f1f1;
        }

        .smartseo-ai-metabox-enhanced {
            background: #fff;
            border-radius: var(--smartseo-border-radius);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* Navigation par onglets */
        .smartseo-ai-tabs-navigation {
            background: var(--smartseo-gray-light);
            border-bottom: 1px solid var(--smartseo-gray-medium);
            padding: 0;
        }

        .smartseo-ai-tabs-nav {
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .smartseo-ai-tab-nav {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 12px 16px;
            cursor: pointer !important;
            border-bottom: 3px solid transparent;
            font-size: 13px;
            font-weight: 500;
            color: var(--smartseo-gray-dark);
            transition: var(--smartseo-transition);
            user-select: none;
            border: 2px solid transparent;
        }

        .smartseo-ai-tab-nav:hover {
            background: rgba(0, 115, 170, 0.1) !important;
            border-color: rgba(0, 115, 170, 0.3);
        }

        .smartseo-ai-tab-nav.active {
            background: #fff !important;
            color: var(--smartseo-primary) !important;
            border-bottom-color: var(--smartseo-primary) !important;
            font-weight: 600 !important;
        }

        .tab-icon svg {
            width: 16px;
            height: 16px;
        }

        /* Contenu des onglets */
        .smartseo-ai-tabs-content {
            position: relative;
            min-height: 200px;
        }

        .smartseo-ai-tab-panel {
            display: none;
            padding: 25px;
            border: 1px solid #ddd;
            min-height: 100px;
        }

        .smartseo-ai-tab-panel.active {
            display: block;
            border-color: var(--smartseo-primary);
            background: #f9f9f9;
        }

        .test-content {
            padding: 20px;
            background: #fff;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Test des Onglets SmartSEO AI</h1>
    
    <div class="smartseo-ai-metabox-enhanced">
        <!-- Navigation par onglets -->
        <div class="smartseo-ai-tabs-navigation">
            <ul class="smartseo-ai-tabs-nav">
                <li class="smartseo-ai-tab-nav active" data-tab="seo-basics">
                    <span class="tab-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" fill="currentColor"/>
                        </svg>
                    </span>
                    SEO de base
                </li>
                <li class="smartseo-ai-tab-nav" data-tab="social-media">
                    <span class="tab-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5,3H19A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5A2,2 0 0,1 3,19V5A2,2 0 0,1 5,3M18,5H12V9H18V5M10,5H6V9H10V5M6,11V15H10V11H6M12,11V15H18V11H12M6,17V19H10V17H6M12,17V19H18V17H12Z" fill="currentColor"/>
                        </svg>
                    </span>
                    Réseaux sociaux
                </li>
                <li class="smartseo-ai-tab-nav" data-tab="advanced">
                    <span class="tab-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" fill="currentColor"/>
                        </svg>
                    </span>
                    Avancé
                </li>
                <li class="smartseo-ai-tab-nav" data-tab="ai-advice">
                    <span class="tab-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z" fill="currentColor"/>
                        </svg>
                    </span>
                    Conseils IA
                </li>
            </ul>
        </div>

        <!-- Contenu des onglets -->
        <div class="smartseo-ai-tabs-content">
            
            <!-- Onglet SEO de base -->
            <div class="smartseo-ai-tab-panel active" id="seo-basics">
                <div class="test-content">
                    <h3>SEO de base</h3>
                    <p>Contenu de l'onglet SEO de base avec les champs titre, description et mots-clés.</p>
                    <input type="text" placeholder="Titre SEO" style="width: 100%; padding: 8px; margin: 5px 0;">
                    <textarea placeholder="Méta description" style="width: 100%; padding: 8px; margin: 5px 0; height: 60px;"></textarea>
                    <input type="text" placeholder="Mots-clés" style="width: 100%; padding: 8px; margin: 5px 0;">
                </div>
            </div>

            <!-- Onglet Réseaux sociaux -->
            <div class="smartseo-ai-tab-panel" id="social-media">
                <div class="test-content">
                    <h3>Réseaux sociaux</h3>
                    <p>Contenu de l'onglet réseaux sociaux avec les champs Open Graph.</p>
                    <input type="text" placeholder="Titre Open Graph" style="width: 100%; padding: 8px; margin: 5px 0;">
                    <textarea placeholder="Description Open Graph" style="width: 100%; padding: 8px; margin: 5px 0; height: 60px;"></textarea>
                    <input type="text" placeholder="URL Image OG" style="width: 100%; padding: 8px; margin: 5px 0;">
                </div>
            </div>

            <!-- Onglet Avancé -->
            <div class="smartseo-ai-tab-panel" id="advanced">
                <div class="test-content">
                    <h3>Paramètres avancés</h3>
                    <p>Contenu de l'onglet avancé avec les paramètres techniques.</p>
                    <input type="text" placeholder="Slug URL optimisé" style="width: 100%; padding: 8px; margin: 5px 0;">
                    <button style="padding: 8px 16px; margin: 5px 0;">Appliquer le slug</button>
                </div>
            </div>

            <!-- Onglet Conseils IA -->
            <div class="smartseo-ai-tab-panel" id="ai-advice">
                <div class="test-content">
                    <h3>Conseils d'optimisation IA</h3>
                    <p>Contenu de l'onglet conseils IA avec les recommandations personnalisées.</p>
                    <ul>
                        <li>Conseil 1 : Optimisez votre titre</li>
                        <li>Conseil 2 : Améliorez votre méta description</li>
                        <li>Conseil 3 : Ajoutez des mots-clés pertinents</li>
                    </ul>
                </div>
            </div>
            
        </div>
    </div>

    <script>
        jQuery(document).ready(function($) {
            console.log('Test onglets - Script chargé');
            
            // Fonction simple pour les onglets
            function initSimpleTabs() {
                console.log('Initialisation des onglets de test');
                
                // Cacher tous les panneaux sauf le premier
                $('.smartseo-ai-tab-panel').hide();
                $('.smartseo-ai-tab-panel').first().show().addClass('active');
                
                // Activer le premier onglet
                $('.smartseo-ai-tab-nav').removeClass('active');
                $('.smartseo-ai-tab-nav').first().addClass('active');
                
                // Gérer les clics
                $('.smartseo-ai-tab-nav').off('click.tabs').on('click.tabs', function(e) {
                    e.preventDefault();
                    
                    var tabId = $(this).data('tab');
                    console.log('Clic onglet:', tabId);
                    
                    // Désactiver tous
                    $('.smartseo-ai-tab-nav').removeClass('active');
                    $('.smartseo-ai-tab-panel').removeClass('active').hide();
                    
                    // Activer le sélectionné
                    $(this).addClass('active');
                    $('#' + tabId).addClass('active').show();
                    
                    console.log('Onglet activé:', tabId);
                    
                    return false;
                });
                
                console.log('Onglets initialisés');
            }
            
            // Initialiser immédiatement
            initSimpleTabs();
            
            // Ajouter un indicateur visuel
            $('body').prepend('<div style="background: #4CAF50; color: white; padding: 10px; text-align: center; margin-bottom: 20px;">✅ Script des onglets chargé et initialisé</div>');
        });
    </script>
</body>
</html>
