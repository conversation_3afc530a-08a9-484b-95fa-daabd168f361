<?php
/**
 * Classe de génération de titres pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la génération de titres
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Title_Generator {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }
    }

    /**
     * Génère des titres
     *
     * @param int    $post_id ID de l'article.
     * @param string $keyword Mot-clé principal.
     * @param string $topic   Sujet de l'article.
     * @return array|WP_Error Résultats de la génération ou erreur.
     */
    public function generate( $post_id, $keyword, $topic ) {
        // Vérifier si le mot-clé ou le sujet est fourni
        if ( empty( $keyword ) && empty( $topic ) ) {
            return new WP_Error( 'missing_data', __( 'Veuillez fournir un mot-clé ou un sujet.', 'smartseo-ai' ) );
        }

        // Utiliser le mot-clé comme sujet s'il n'est pas fourni
        if ( empty( $topic ) ) {
            $topic = $keyword;
        }

        // Récupérer les données de l'article
        $post = get_post( $post_id );
        $post_type = $post ? $post->post_type : 'post';
        $post_type_object = get_post_type_object( $post_type );
        $post_type_label = $post_type_object ? $post_type_object->labels->singular_name : __( 'Article', 'smartseo-ai' );

        // Construire le prompt
        $prompt = $this->build_prompt( $keyword, $topic, $post_type_label );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Traiter la réponse
        $titles = $this->process_response( $response );

        // Retourner les résultats
        return array(
            'titles' => $titles,
        );
    }

    /**
     * Construit le prompt pour l'IA
     *
     * @param string $keyword       Mot-clé principal.
     * @param string $topic         Sujet de l'article.
     * @param string $post_type_label Label du type de publication.
     * @return string Prompt pour l'IA.
     */
    private function build_prompt( $keyword, $topic, $post_type_label ) {
        $prompt = "Génère 5 titres SEO accrocheurs pour un {$post_type_label} sur le sujet : \"{$topic}\".";
        
        if ( ! empty( $keyword ) && $keyword !== $topic ) {
            $prompt .= " Le mot-clé principal à inclure est : \"{$keyword}\".";
        }
        
        $prompt .= " Les titres doivent être :
1. Optimisés pour le SEO
2. Accrocheurs et engageants
3. Entre 50 et 60 caractères
4. Clairs et précis
5. Pertinents pour le sujet

Réponds uniquement avec les 5 titres, un par ligne, sans numérotation ni autres explications.";

        return $prompt;
    }

    /**
     * Traite la réponse de l'IA
     *
     * @param string $response Réponse de l'IA.
     * @return array Titres générés.
     */
    private function process_response( $response ) {
        // Diviser la réponse en lignes
        $lines = explode( "\n", $response );
        
        // Filtrer les lignes vides et les lignes qui commencent par un chiffre suivi d'un point (numérotation)
        $titles = array_filter( array_map( 'trim', $lines ), function( $line ) {
            return ! empty( $line ) && ! preg_match( '/^\d+\./', $line );
        } );
        
        // Nettoyer les titres (supprimer les guillemets, etc.)
        $titles = array_map( function( $title ) {
            return trim( $title, " \t\n\r\0\x0B\"'" );
        }, $titles );
        
        // Limiter à 5 titres
        $titles = array_slice( array_values( $titles ), 0, 5 );
        
        return $titles;
    }
}
