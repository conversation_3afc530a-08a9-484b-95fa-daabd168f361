<?php
/**
 * Vue améliorée de l'optimisation en masse avec filtres avancés
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Utiliser les statistiques globales fournies par le contrôleur
$total_posts = $stats['total_posts'];
$optimized_posts = $stats['optimized_posts'];
$not_optimized_posts = $stats['not_optimized_posts'];
$optimization_rate = $stats['optimization_rate'];
$stats_by_type = $stats['by_post_type'];
$score_distribution = $stats['score_distribution'];

// Paramètres de pagination et filtres
$current_page = isset( $_GET['paged'] ) ? absint( $_GET['paged'] ) : 1;
$per_page = isset( $_GET['per_page'] ) ? absint( $_GET['per_page'] ) : 20;
$current_filters = $result['filters'] ?? array();

// Options de nombre d'articles par page
$per_page_options = array( 10, 20, 50, 100 );

// Types de posts disponibles
$available_post_types = array();
foreach ( $stats_by_type as $post_type => $type_stats ) {
    $available_post_types[ $post_type ] = $type_stats['label'];
}
?>

<div class="wrap smartseo-ai-bulk-optimizer-enhanced">
    <h1><?php _e( 'SmartSEO AI - Optimisation en masse', 'smartseo-ai' ); ?></h1>

    <!-- Statistiques globales -->
    <div class="smartseo-stats-overview">
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                    <h3><?php _e( 'Total', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo number_format( $total_posts ); ?></div>
                    <div class="stat-label"><?php _e( 'contenus', 'smartseo-ai' ); ?></div>
                </div>
            </div>

            <div class="stat-card optimized">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <h3><?php _e( 'Optimisés', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo number_format( $optimized_posts ); ?></div>
                    <div class="stat-label"><?php echo $optimization_rate; ?>% <?php _e( 'du total', 'smartseo-ai' ); ?></div>
                </div>
            </div>

            <div class="stat-card pending">
                <div class="stat-icon">⏳</div>
                <div class="stat-content">
                    <h3><?php _e( 'En attente', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo number_format( $not_optimized_posts ); ?></div>
                    <div class="stat-label"><?php _e( 'à optimiser', 'smartseo-ai' ); ?></div>
                </div>
            </div>

            <div class="stat-card progress">
                <div class="stat-icon">📈</div>
                <div class="stat-content">
                    <h3><?php _e( 'Progression', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value"><?php echo $optimization_rate; ?>%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?php echo $optimization_rate; ?>%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques par type de contenu -->
        <div class="stats-by-type">
            <h3><?php _e( 'Répartition par type de contenu', 'smartseo-ai' ); ?></h3>
            <div class="type-stats-grid">
                <?php foreach ( $stats_by_type as $post_type => $type_stats ) : ?>
                    <div class="type-stat-item">
                        <div class="type-name"><?php echo esc_html( $type_stats['label'] ); ?></div>
                        <div class="type-numbers">
                            <span class="optimized"><?php echo $type_stats['optimized']; ?></span>
                            /
                            <span class="total"><?php echo $type_stats['total']; ?></span>
                        </div>
                        <div class="type-progress">
                            <div class="type-progress-bar">
                                <div class="type-progress-fill" style="width: <?php echo $type_stats['rate']; ?>%"></div>
                            </div>
                            <span class="type-percentage"><?php echo $type_stats['rate']; ?>%</span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Filtres avancés -->
    <div class="smartseo-filters-panel">
        <h3>
            <span class="dashicons dashicons-filter"></span>
            <?php _e( 'Filtres avancés', 'smartseo-ai' ); ?>
        </h3>
        
        <form method="get" action="" class="filters-form">
            <input type="hidden" name="page" value="smartseo-ai-bulk-optimizer">
            
            <div class="filters-grid">
                <!-- Types de contenu -->
                <div class="filter-group">
                    <label><?php _e( 'Types de contenu', 'smartseo-ai' ); ?></label>
                    <div class="checkbox-group">
                        <?php foreach ( $available_post_types as $post_type => $label ) : ?>
                            <label class="checkbox-item">
                                <input type="checkbox" 
                                       name="post_types[]" 
                                       value="<?php echo esc_attr( $post_type ); ?>"
                                       <?php checked( in_array( $post_type, $current_filters['post_types'] ?? array() ) ); ?>>
                                <span class="checkmark"></span>
                                <?php echo esc_html( $label ); ?>
                                <span class="count">(<?php echo $stats_by_type[ $post_type ]['total']; ?>)</span>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Statut d'optimisation -->
                <div class="filter-group">
                    <label for="optimization_status"><?php _e( 'Statut d\'optimisation', 'smartseo-ai' ); ?></label>
                    <select name="optimization_status" id="optimization_status">
                        <option value="all" <?php selected( $current_filters['optimization_status'] ?? '', 'all' ); ?>>
                            <?php _e( 'Tous les statuts', 'smartseo-ai' ); ?>
                        </option>
                        <option value="optimized" <?php selected( $current_filters['optimization_status'] ?? '', 'optimized' ); ?>>
                            <?php _e( 'Optimisés', 'smartseo-ai' ); ?> (<?php echo $optimized_posts; ?>)
                        </option>
                        <option value="not_optimized" <?php selected( $current_filters['optimization_status'] ?? '', 'not_optimized' ); ?>>
                            <?php _e( 'Non optimisés', 'smartseo-ai' ); ?> (<?php echo $not_optimized_posts; ?>)
                        </option>
                    </select>
                </div>

                <!-- Plage de score -->
                <div class="filter-group">
                    <label for="score_range"><?php _e( 'Score SEO', 'smartseo-ai' ); ?></label>
                    <select name="score_range" id="score_range">
                        <option value="all" <?php selected( $current_filters['score_range'] ?? '', 'all' ); ?>>
                            <?php _e( 'Tous les scores', 'smartseo-ai' ); ?>
                        </option>
                        <option value="excellent" <?php selected( $current_filters['score_range'] ?? '', 'excellent' ); ?>>
                            <?php _e( 'Excellent (80-100)', 'smartseo-ai' ); ?> (<?php echo $score_distribution['excellent']; ?>)
                        </option>
                        <option value="good" <?php selected( $current_filters['score_range'] ?? '', 'good' ); ?>>
                            <?php _e( 'Bon (60-79)', 'smartseo-ai' ); ?> (<?php echo $score_distribution['good']; ?>)
                        </option>
                        <option value="average" <?php selected( $current_filters['score_range'] ?? '', 'average' ); ?>>
                            <?php _e( 'Moyen (40-59)', 'smartseo-ai' ); ?> (<?php echo $score_distribution['average']; ?>)
                        </option>
                        <option value="poor" <?php selected( $current_filters['score_range'] ?? '', 'poor' ); ?>>
                            <?php _e( 'Faible (0-39)', 'smartseo-ai' ); ?> (<?php echo $score_distribution['poor']; ?>)
                        </option>
                    </select>
                </div>

                <!-- Période -->
                <div class="filter-group">
                    <label for="date_range"><?php _e( 'Période de publication', 'smartseo-ai' ); ?></label>
                    <select name="date_range" id="date_range">
                        <option value="all" <?php selected( $current_filters['date_range'] ?? '', 'all' ); ?>>
                            <?php _e( 'Toutes les périodes', 'smartseo-ai' ); ?>
                        </option>
                        <option value="last_week" <?php selected( $current_filters['date_range'] ?? '', 'last_week' ); ?>>
                            <?php _e( 'Dernière semaine', 'smartseo-ai' ); ?>
                        </option>
                        <option value="last_month" <?php selected( $current_filters['date_range'] ?? '', 'last_month' ); ?>>
                            <?php _e( 'Dernier mois', 'smartseo-ai' ); ?>
                        </option>
                        <option value="last_3_months" <?php selected( $current_filters['date_range'] ?? '', 'last_3_months' ); ?>>
                            <?php _e( 'Derniers 3 mois', 'smartseo-ai' ); ?>
                        </option>
                        <option value="last_6_months" <?php selected( $current_filters['date_range'] ?? '', 'last_6_months' ); ?>>
                            <?php _e( 'Derniers 6 mois', 'smartseo-ai' ); ?>
                        </option>
                        <option value="last_year" <?php selected( $current_filters['date_range'] ?? '', 'last_year' ); ?>>
                            <?php _e( 'Dernière année', 'smartseo-ai' ); ?>
                        </option>
                        <option value="older_than_year" <?php selected( $current_filters['date_range'] ?? '', 'older_than_year' ); ?>>
                            <?php _e( 'Plus d\'un an', 'smartseo-ai' ); ?>
                        </option>
                    </select>
                </div>

                <!-- Recherche -->
                <div class="filter-group">
                    <label for="search"><?php _e( 'Rechercher', 'smartseo-ai' ); ?></label>
                    <input type="text" 
                           name="search" 
                           id="search" 
                           value="<?php echo esc_attr( $current_filters['search_term'] ?? '' ); ?>"
                           placeholder="<?php _e( 'Titre, contenu...', 'smartseo-ai' ); ?>">
                </div>

                <!-- Tri -->
                <div class="filter-group">
                    <label for="orderby"><?php _e( 'Trier par', 'smartseo-ai' ); ?></label>
                    <div class="sort-controls">
                        <select name="orderby" id="orderby">
                            <?php foreach ( $this->get_sort_options() as $key => $label ) : ?>
                                <option value="<?php echo esc_attr( $key ); ?>" <?php selected( $current_filters['orderby'] ?? '', $key ); ?>>
                                    <?php echo esc_html( $label ); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <select name="order" id="order">
                            <option value="DESC" <?php selected( $current_filters['order'] ?? '', 'DESC' ); ?>>
                                <?php _e( 'Décroissant', 'smartseo-ai' ); ?>
                            </option>
                            <option value="ASC" <?php selected( $current_filters['order'] ?? '', 'ASC' ); ?>>
                                <?php _e( 'Croissant', 'smartseo-ai' ); ?>
                            </option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="filters-actions">
                <button type="submit" class="button button-primary">
                    <span class="dashicons dashicons-search"></span>
                    <?php _e( 'Appliquer les filtres', 'smartseo-ai' ); ?>
                </button>
                <a href="<?php echo esc_url( admin_url( 'admin.php?page=smartseo-ai-bulk-optimizer' ) ); ?>" class="button button-secondary">
                    <span class="dashicons dashicons-dismiss"></span>
                    <?php _e( 'Réinitialiser', 'smartseo-ai' ); ?>
                </a>
            </div>
        </form>
    </div>

    <!-- Actions en masse -->
    <div class="smartseo-bulk-actions-panel">
        <div class="bulk-actions-header">
            <h3><?php _e( 'Actions en masse', 'smartseo-ai' ); ?></h3>
            <div class="selection-info">
                <span id="selected-count">0</span> <?php _e( 'élément(s) sélectionné(s)', 'smartseo-ai' ); ?>
            </div>
        </div>
        
        <div class="bulk-actions-controls">
            <button type="button" class="button button-primary" id="optimize-selected" disabled>
                <span class="dashicons dashicons-superhero"></span>
                <?php _e( 'Optimiser la sélection', 'smartseo-ai' ); ?>
            </button>
            
            <button type="button" class="button button-secondary" id="optimize-all-filtered">
                <span class="dashicons dashicons-superhero-alt"></span>
                <?php _e( 'Optimiser tous les résultats filtrés', 'smartseo-ai' ); ?>
            </button>
            
            <button type="button" class="button button-secondary" id="stop-optimization" style="display: none;">
                <span class="dashicons dashicons-no-alt"></span>
                <?php _e( 'Arrêter l\'optimisation', 'smartseo-ai' ); ?>
            </button>
        </div>

        <!-- Barre de progression -->
        <div id="optimization-progress" class="optimization-progress" style="display: none;">
            <div class="progress-header">
                <h4><?php _e( 'Optimisation en cours...', 'smartseo-ai' ); ?></h4>
                <span class="progress-stats">
                    <span id="progress-current">0</span> / <span id="progress-total">0</span>
                </span>
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-percentage" id="progress-percentage">0%</div>
            </div>
            <div class="progress-details">
                <div class="progress-status" id="progress-status"><?php _e( 'Initialisation...', 'smartseo-ai' ); ?></div>
                <div class="progress-eta" id="progress-eta"></div>
            </div>
        </div>
    </div>

    <!-- Table des contenus -->
    <div class="smartseo-content-table-panel">
        <div class="table-header">
            <div class="table-controls">
                <div class="table-info">
                    <?php
                    printf(
                        __( 'Affichage de %1$s à %2$s sur %3$s résultats', 'smartseo-ai' ),
                        ( $current_page - 1 ) * $per_page + 1,
                        min( $current_page * $per_page, $total_posts ),
                        number_format( $total_posts )
                    );
                    ?>
                </div>

                <div class="table-actions">
                    <label for="per-page-select"><?php _e( 'Afficher:', 'smartseo-ai' ); ?></label>
                    <select id="per-page-select" onchange="window.location.href='<?php echo esc_url( $this->get_filtered_url( array( 'paged' => 1 ) ) ); ?>&per_page=' + this.value">
                        <?php foreach ( $per_page_options as $option ) : ?>
                            <option value="<?php echo esc_attr( $option ); ?>" <?php selected( $per_page, $option ); ?>>
                                <?php echo esc_html( $option ); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>

                    <button type="button" class="button button-small" id="select-all-visible">
                        <?php _e( 'Tout sélectionner', 'smartseo-ai' ); ?>
                    </button>

                    <button type="button" class="button button-small" id="deselect-all">
                        <?php _e( 'Tout désélectionner', 'smartseo-ai' ); ?>
                    </button>
                </div>
            </div>
        </div>

        <div class="table-container">
            <table class="smartseo-content-table">
                <thead>
                    <tr>
                        <th class="column-select">
                            <input type="checkbox" id="select-all-checkbox">
                        </th>
                        <th class="column-id sortable" data-sort="ID">
                            <?php _e( 'ID', 'smartseo-ai' ); ?>
                            <?php if ( ( $current_filters['orderby'] ?? '' ) === 'ID' ) : ?>
                                <span class="sort-indicator <?php echo ( $current_filters['order'] ?? '' ) === 'ASC' ? 'asc' : 'desc'; ?>"></span>
                            <?php endif; ?>
                        </th>
                        <th class="column-title sortable" data-sort="title">
                            <?php _e( 'Titre', 'smartseo-ai' ); ?>
                            <?php if ( ( $current_filters['orderby'] ?? '' ) === 'title' ) : ?>
                                <span class="sort-indicator <?php echo ( $current_filters['order'] ?? '' ) === 'ASC' ? 'asc' : 'desc'; ?>"></span>
                            <?php endif; ?>
                        </th>
                        <th class="column-type"><?php _e( 'Type', 'smartseo-ai' ); ?></th>
                        <th class="column-date sortable" data-sort="date">
                            <?php _e( 'Date', 'smartseo-ai' ); ?>
                            <?php if ( ( $current_filters['orderby'] ?? '' ) === 'date' ) : ?>
                                <span class="sort-indicator <?php echo ( $current_filters['order'] ?? '' ) === 'ASC' ? 'asc' : 'desc'; ?>"></span>
                            <?php endif; ?>
                        </th>
                        <th class="column-author"><?php _e( 'Auteur', 'smartseo-ai' ); ?></th>
                        <th class="column-words"><?php _e( 'Mots', 'smartseo-ai' ); ?></th>
                        <th class="column-score"><?php _e( 'Score SEO', 'smartseo-ai' ); ?></th>
                        <th class="column-status"><?php _e( 'Statut', 'smartseo-ai' ); ?></th>
                        <th class="column-actions"><?php _e( 'Actions', 'smartseo-ai' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( empty( $posts ) ) : ?>
                        <tr class="no-results">
                            <td colspan="10">
                                <div class="no-results-message">
                                    <span class="dashicons dashicons-search"></span>
                                    <h3><?php _e( 'Aucun contenu trouvé', 'smartseo-ai' ); ?></h3>
                                    <p><?php _e( 'Essayez de modifier vos critères de recherche ou vos filtres.', 'smartseo-ai' ); ?></p>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ( $posts as $post ) : ?>
                            <tr class="content-row <?php echo esc_attr( $post['status'] ); ?>"
                                data-post-id="<?php echo esc_attr( $post['id'] ); ?>"
                                data-post-type="<?php echo esc_attr( $post['post_type_key'] ); ?>">

                                <td class="column-select">
                                    <input type="checkbox"
                                           class="post-checkbox"
                                           value="<?php echo esc_attr( $post['id'] ); ?>"
                                           data-title="<?php echo esc_attr( $post['title'] ); ?>">
                                </td>

                                <td class="column-id">
                                    <strong><?php echo esc_html( $post['id'] ); ?></strong>
                                </td>

                                <td class="column-title">
                                    <div class="title-wrapper">
                                        <a href="<?php echo esc_url( $post['permalink'] ); ?>"
                                           target="_blank"
                                           class="post-title">
                                            <?php echo esc_html( $post['title'] ); ?>
                                        </a>
                                        <div class="row-actions">
                                            <span class="edit">
                                                <a href="<?php echo esc_url( $post['edit_link'] ); ?>">
                                                    <?php _e( 'Modifier', 'smartseo-ai' ); ?>
                                                </a>
                                            </span>
                                            |
                                            <span class="view">
                                                <a href="<?php echo esc_url( $post['permalink'] ); ?>" target="_blank">
                                                    <?php _e( 'Voir', 'smartseo-ai' ); ?>
                                                </a>
                                            </span>
                                        </div>
                                    </div>
                                </td>

                                <td class="column-type">
                                    <span class="post-type-badge post-type-<?php echo esc_attr( $post['post_type_key'] ); ?>">
                                        <?php echo esc_html( $post['post_type'] ); ?>
                                    </span>
                                </td>

                                <td class="column-date">
                                    <div class="date-info">
                                        <div class="published"><?php echo esc_html( $post['date'] ); ?></div>
                                        <?php if ( $post['modified'] !== $post['date'] ) : ?>
                                            <div class="modified">
                                                <small><?php _e( 'Modifié:', 'smartseo-ai' ); ?> <?php echo esc_html( $post['modified'] ); ?></small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>

                                <td class="column-author">
                                    <?php echo esc_html( $post['author'] ); ?>
                                </td>

                                <td class="column-words">
                                    <span class="word-count"><?php echo number_format( $post['word_count'] ); ?></span>
                                </td>

                                <td class="column-score">
                                    <?php if ( $post['has_score'] ) : ?>
                                        <div class="score-badge score-<?php echo esc_attr( $this->get_score_class( $post['seo_score'] ) ); ?>">
                                            <span class="score-value"><?php echo esc_html( $post['seo_score'] ); ?></span>
                                            <span class="score-max">/100</span>
                                        </div>
                                    <?php else : ?>
                                        <div class="score-badge score-none">
                                            <span class="score-text"><?php _e( 'Non optimisé', 'smartseo-ai' ); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </td>

                                <td class="column-status">
                                    <div class="status-badge status-<?php echo esc_attr( $post['status'] ); ?>">
                                        <?php
                                        switch ( $post['status'] ) {
                                            case 'optimized':
                                                echo '<span class="dashicons dashicons-yes-alt"></span>' . __( 'Optimisé', 'smartseo-ai' );
                                                break;
                                            case 'in-progress':
                                                echo '<span class="dashicons dashicons-update"></span>' . __( 'En cours...', 'smartseo-ai' );
                                                break;
                                            case 'error':
                                                echo '<span class="dashicons dashicons-warning"></span>' . __( 'Erreur', 'smartseo-ai' );
                                                break;
                                            default:
                                                echo '<span class="dashicons dashicons-clock"></span>' . __( 'En attente', 'smartseo-ai' );
                                                break;
                                        }
                                        ?>
                                    </div>
                                </td>

                                <td class="column-actions">
                                    <div class="action-buttons">
                                        <button type="button"
                                                class="button button-small optimize-single"
                                                data-post-id="<?php echo esc_attr( $post['id'] ); ?>"
                                                title="<?php _e( 'Optimiser ce contenu', 'smartseo-ai' ); ?>">
                                            <span class="dashicons dashicons-superhero"></span>
                                            <?php _e( 'Optimiser', 'smartseo-ai' ); ?>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ( $total_pages > 1 ) : ?>
            <div class="table-pagination">
                <?php
                $pagination_links = paginate_links( array(
                    'base'      => $this->get_filtered_url( array( 'paged' => '%#%' ) ),
                    'format'    => '',
                    'prev_text' => '<span class="dashicons dashicons-arrow-left-alt2"></span> ' . __( 'Précédent', 'smartseo-ai' ),
                    'next_text' => __( 'Suivant', 'smartseo-ai' ) . ' <span class="dashicons dashicons-arrow-right-alt2"></span>',
                    'total'     => $total_pages,
                    'current'   => $current_page,
                    'type'      => 'array',
                    'show_all'  => false,
                    'end_size'  => 1,
                    'mid_size'  => 2,
                ) );

                if ( ! empty( $pagination_links ) ) :
                ?>
                    <div class="pagination-wrapper">
                        <div class="pagination-info">
                            <?php
                            printf(
                                __( 'Page %1$s sur %2$s', 'smartseo-ai' ),
                                number_format( $current_page ),
                                number_format( $total_pages )
                            );
                            ?>
                        </div>
                        <div class="pagination-links">
                            <?php foreach ( $pagination_links as $link ) : ?>
                                <?php echo $link; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
