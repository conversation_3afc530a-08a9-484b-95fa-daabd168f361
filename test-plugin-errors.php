<?php
/**
 * Script de test pour vérifier les erreurs du plugin SmartSEO AI
 * 
 * À exécuter dans l'admin WordPress pour diagnostiquer les problèmes
 */

// Vérifier que nous sommes dans WordPress
if ( ! defined( 'ABSPATH' ) ) {
    die( 'Accès direct interdit' );
}

/**
 * Classe de test pour diagnostiquer les erreurs
 */
class SmartSEO_AI_Error_Tester {
    
    /**
     * Lance tous les tests
     */
    public static function run_tests() {
        echo '<div style="background: #fff; padding: 20px; margin: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">';
        echo '<h2>🔍 Tests de Diagnostic SmartSEO AI</h2>';
        
        self::test_constants();
        self::test_sitemap_options();
        self::test_textdomain();
        self::test_metabox_files();
        
        echo '</div>';
    }
    
    /**
     * Test des constantes
     */
    private static function test_constants() {
        echo '<h3>📋 Test des Constantes</h3>';
        
        $constants = array(
            'SMARTSEO_AI_VERSION',
            'SMARTSEO_AI_PLUGIN_FILE',
            'SMARTSEO_AI_PLUGIN_DIR',
            'SMARTSEO_AI_PLUGIN_URL',
            'SMARTSEO_AI_PLUGIN_BASENAME'
        );
        
        foreach ( $constants as $constant ) {
            if ( defined( $constant ) ) {
                echo '<p style="color: #46b450;">✅ ' . $constant . ' = ' . constant( $constant ) . '</p>';
            } else {
                echo '<p style="color: #dc3232;">❌ ' . $constant . ' non définie</p>';
            }
        }
    }
    
    /**
     * Test des options du sitemap
     */
    private static function test_sitemap_options() {
        echo '<h3>🗺️ Test des Options Sitemap</h3>';
        
        $sitemap_options = get_option( 'smartseo_ai_sitemap_options', array() );
        
        if ( empty( $sitemap_options ) ) {
            echo '<p style="color: #ff9800;">⚠️ Options sitemap non trouvées, initialisation...</p>';
            
            // Initialiser les options par défaut
            $default_options = array(
                'enabled' => 'no',
                'use_index' => 'yes',
                'include_posts' => 'yes',
                'include_pages' => 'yes',
                'include_products' => 'no',
                'include_custom_post_types' => 'no',
                'max_entries' => 1000,
                'update_frequency' => 'daily',
                'priority' => '0.8'
            );
            
            update_option( 'smartseo_ai_sitemap_options', $default_options );
            echo '<p style="color: #46b450;">✅ Options sitemap initialisées</p>';
        } else {
            echo '<p style="color: #46b450;">✅ Options sitemap trouvées</p>';
            
            $required_keys = array( 'enabled', 'use_index', 'include_posts', 'include_pages' );
            
            foreach ( $required_keys as $key ) {
                if ( isset( $sitemap_options[ $key ] ) ) {
                    echo '<p style="color: #46b450;">✅ ' . $key . ' = ' . $sitemap_options[ $key ] . '</p>';
                } else {
                    echo '<p style="color: #dc3232;">❌ ' . $key . ' manquant</p>';
                }
            }
        }
    }
    
    /**
     * Test du domaine de traduction
     */
    private static function test_textdomain() {
        echo '<h3>🌍 Test du Domaine de Traduction</h3>';
        
        // Vérifier si le domaine est chargé
        $loaded = is_textdomain_loaded( 'smartseo-ai' );
        
        if ( $loaded ) {
            echo '<p style="color: #46b450;">✅ Domaine de traduction chargé</p>';
        } else {
            echo '<p style="color: #ff9800;">⚠️ Domaine de traduction non chargé</p>';
            
            // Essayer de le charger
            if ( defined( 'SMARTSEO_AI_PLUGIN_FILE' ) ) {
                $result = load_plugin_textdomain(
                    'smartseo-ai',
                    false,
                    dirname( plugin_basename( SMARTSEO_AI_PLUGIN_FILE ) ) . '/languages/'
                );
                
                if ( $result ) {
                    echo '<p style="color: #46b450;">✅ Domaine de traduction chargé manuellement</p>';
                } else {
                    echo '<p style="color: #dc3232;">❌ Échec du chargement du domaine de traduction</p>';
                }
            }
        }
        
        // Tester une traduction
        $test_string = __( 'Optimiser avec l\'IA', 'smartseo-ai' );
        echo '<p>🧪 Test de traduction : "' . $test_string . '"</p>';
    }
    
    /**
     * Test des fichiers de la meta box
     */
    private static function test_metabox_files() {
        echo '<h3>📦 Test des Fichiers Meta Box</h3>';
        
        if ( ! defined( 'SMARTSEO_AI_PLUGIN_DIR' ) ) {
            echo '<p style="color: #dc3232;">❌ SMARTSEO_AI_PLUGIN_DIR non définie</p>';
            return;
        }
        
        $files_to_check = array(
            'includes/class-smartseo-ai-metaboxes.php',
            'admin/css/smartseo-ai-metabox-enhanced.css',
            'admin/js/smartseo-ai-metabox-enhanced.js',
            'admin/views/metabox-settings.php',
            'languages/smartseo-ai.pot',
            'languages/smartseo-ai-fr_FR.po',
            'languages/smartseo-ai-en_US.po'
        );
        
        foreach ( $files_to_check as $file ) {
            $file_path = SMARTSEO_AI_PLUGIN_DIR . $file;
            
            if ( file_exists( $file_path ) ) {
                $size = filesize( $file_path );
                echo '<p style="color: #46b450;">✅ ' . $file . ' (' . self::format_bytes( $size ) . ')</p>';
            } else {
                echo '<p style="color: #dc3232;">❌ ' . $file . ' manquant</p>';
            }
        }
    }
    
    /**
     * Formate la taille des fichiers
     */
    private static function format_bytes( $size, $precision = 2 ) {
        $units = array( 'B', 'KB', 'MB', 'GB' );
        
        for ( $i = 0; $size > 1024 && $i < count( $units ) - 1; $i++ ) {
            $size /= 1024;
        }
        
        return round( $size, $precision ) . ' ' . $units[ $i ];
    }
}

// Si ce fichier est appelé directement via l'admin
if ( is_admin() && isset( $_GET['smartseo_ai_test'] ) ) {
    add_action( 'admin_notices', function() {
        SmartSEO_AI_Error_Tester::run_tests();
    });
}

// Ajouter un lien de test dans le menu admin
add_action( 'admin_menu', function() {
    if ( current_user_can( 'manage_options' ) ) {
        add_submenu_page(
            'tools.php',
            'Test SmartSEO AI',
            'Test SmartSEO AI',
            'manage_options',
            'smartseo-ai-test',
            function() {
                echo '<div class="wrap">';
                echo '<h1>Test SmartSEO AI</h1>';
                SmartSEO_AI_Error_Tester::run_tests();
                echo '</div>';
            }
        );
    }
});
?>
