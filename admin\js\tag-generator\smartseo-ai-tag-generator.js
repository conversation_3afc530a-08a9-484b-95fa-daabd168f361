/**
 * Script JavaScript pour le Générateur de Tags IA (éditeur classique)
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Générateur de Tags IA
     */
    const TagGenerator = {
        /**
         * Initialise le générateur de tags
         */
        init: function() {
            // Ajouter les écouteurs d'événements
            this.bindEvents();
        },

        /**
         * Ajoute les écouteurs d'événements
         */
        bindEvents: function() {
            // Générer les tags
            $('#smartseo-ai-tag-generator-generate').on('click', this.generateTags);
            
            // Appliquer les tags
            $('#smartseo-ai-tag-generator-apply').on('click', this.applyTags);
            
            // Régénérer les tags
            $('#smartseo-ai-tag-generator-regenerate').on('click', this.generateTags);
            
            // Activer/désactiver l'API externe
            $('#smartseo-ai-tag-generator-use-external-api').on('change', function() {
                $('#smartseo-ai-tag-generator-external-api').prop('disabled', !$(this).is(':checked'));
            });
            
            // Sélectionner/désélectionner un tag
            $(document).on('click', '.smartseo-ai-tag-generator-generated-tag', function() {
                $(this).toggleClass('selected');
            });
        },

        /**
         * Génère les tags
         */
        generateTags: function() {
            // Afficher le loader
            $('.smartseo-ai-tag-generator-loading').show();
            $('.smartseo-ai-tag-generator-results').hide();
            $('.smartseo-ai-tag-generator-error').hide();
            
            // Récupérer les options
            const includeBrands = $('#smartseo-ai-tag-generator-include-brands').is(':checked');
            const includeRegions = $('#smartseo-ai-tag-generator-include-regions').is(':checked');
            const useExternalApi = $('#smartseo-ai-tag-generator-use-external-api').is(':checked');
            const externalApi = $('#smartseo-ai-tag-generator-external-api').val();
            
            // Récupérer le contenu de l'article
            const content = $('#content').val();
            const title = $('#title').val();
            
            // Récupérer la catégorie
            let category = '';
            
            if ($('#categorychecklist input:checked').length > 0) {
                category = $('#categorychecklist input:checked').first().parent().text().trim();
            }
            
            // Récupérer l'ID de l'article
            const postId = $('#post_ID').val();
            
            // Appeler l'API
            $.ajax({
                url: smartseoAiTagGenerator.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartseo_ai_generate_tags',
                    nonce: smartseoAiTagGenerator.nonce,
                    post_id: postId,
                    content: content,
                    title: title,
                    category: category,
                    include_brands: includeBrands,
                    include_regions: includeRegions,
                    use_external_api: useExternalApi,
                    external_api: externalApi
                },
                success: function(response) {
                    // Masquer le loader
                    $('.smartseo-ai-tag-generator-loading').hide();
                    
                    if (response.success) {
                        // Afficher les résultats
                        TagGenerator.displayResults(response.data);
                    } else {
                        // Afficher l'erreur
                        $('.smartseo-ai-tag-generator-error').show();
                        $('.smartseo-ai-tag-generator-error-message').text(response.data.message);
                    }
                },
                error: function() {
                    // Masquer le loader
                    $('.smartseo-ai-tag-generator-loading').hide();
                    
                    // Afficher l'erreur
                    $('.smartseo-ai-tag-generator-error').show();
                    $('.smartseo-ai-tag-generator-error-message').text(smartseoAiTagGenerator.i18n.error);
                }
            });
        },

        /**
         * Affiche les résultats
         *
         * @param {Object} data Données des résultats.
         */
        displayResults: function(data) {
            // Vérifier que les données sont valides
            if (!data.tags || data.tags.length === 0) {
                $('.smartseo-ai-tag-generator-error').show();
                $('.smartseo-ai-tag-generator-error-message').text(smartseoAiTagGenerator.i18n.no_tags);
                return;
            }
            
            // Afficher les résultats
            $('.smartseo-ai-tag-generator-results').show();
            
            // Vider les listes
            $('.smartseo-ai-tag-generator-optimal-tags-list').empty();
            $('.smartseo-ai-tag-generator-generated-tags-list').empty();
            
            // Afficher la combinaison optimale
            if (data.optimal_tags && data.optimal_tags.length > 0) {
                data.optimal_tags.forEach(function(tag) {
                    $('.smartseo-ai-tag-generator-optimal-tags-list').append(
                        $('<span class="smartseo-ai-tag-generator-optimal-tag" data-name="' + tag.name + '" data-score="' + tag.score + '">' +
                            tag.name +
                            '<span class="smartseo-ai-tag-generator-tag-score">' + tag.score + '</span>' +
                        '</span>')
                    );
                });
            } else {
                $('.smartseo-ai-tag-generator-optimal-tags-list').append(
                    $('<p class="smartseo-ai-tag-generator-no-tags">' + smartseoAiTagGenerator.i18n.no_tags + '</p>')
                );
            }
            
            // Afficher les tags générés
            data.tags.forEach(function(tag) {
                $('.smartseo-ai-tag-generator-generated-tags-list').append(
                    $('<span class="smartseo-ai-tag-generator-generated-tag" data-name="' + tag.name + '" data-score="' + tag.score + '">' +
                        tag.name +
                        '<span class="smartseo-ai-tag-generator-tag-score">' + tag.score + '</span>' +
                    '</span>')
                );
            });
            
            // Stocker les tags dans l'objet
            TagGenerator.tags = data.tags;
        },

        /**
         * Applique les tags
         */
        applyTags: function() {
            // Vérifier que les tags sont disponibles
            if (!TagGenerator.tags || TagGenerator.tags.length === 0) {
                return;
            }
            
            // Récupérer les tags sélectionnés
            const selectedTags = [];
            
            $('.smartseo-ai-tag-generator-generated-tag.selected').each(function() {
                selectedTags.push({
                    name: $(this).data('name'),
                    score: $(this).data('score')
                });
            });
            
            // Si aucun tag n'est sélectionné, utiliser tous les tags
            const tagsToApply = selectedTags.length > 0 ? selectedTags : TagGenerator.tags;
            
            // Récupérer l'ID de l'article
            const postId = $('#post_ID').val();
            
            // Appeler l'API
            $.ajax({
                url: smartseoAiTagGenerator.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartseo_ai_apply_tags',
                    nonce: smartseoAiTagGenerator.nonce,
                    post_id: postId,
                    tags: tagsToApply
                },
                success: function(response) {
                    if (response.success) {
                        // Afficher un message de succès
                        alert(smartseoAiTagGenerator.i18n.apply_success);
                        
                        // Recharger la page pour afficher les tags appliqués
                        location.reload();
                    } else {
                        // Afficher un message d'erreur
                        alert(response.data.message);
                    }
                },
                error: function() {
                    // Afficher un message d'erreur
                    alert(smartseoAiTagGenerator.i18n.apply_error);
                }
            });
        }
    };
    
    // Initialiser le générateur de tags au chargement du document
    $(document).ready(function() {
        TagGenerator.init();
    });
    
})(jQuery);
