<?php
/**
 * Classe pour l'analyse de la structure du contenu
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'analyse de la structure du contenu
 */
class SmartSEO_AI_Structure_Analyzer {

    /**
     * Analyse la structure du contenu
     *
     * @param string $content Contenu à analyser.
     * @param string $title   Titre de l'article.
     * @return array Résultats de l'analyse.
     */
    public function analyze( $content, $title ) {
        // Analyser les titres
        $headings = $this->analyze_headings( $content, $title );
        
        // Analyser les listes
        $lists = $this->analyze_lists( $content );
        
        // Analyser les éléments visuels
        $visual_elements = $this->analyze_visual_elements( $content );
        
        // Créer la liste des vérifications
        $checks = array(
            array(
                'label' => __( 'Structure des titres', 'smartseo-ai' ),
                'status' => $headings['status'],
                'recommendation' => $headings['recommendation'],
            ),
            array(
                'label' => __( 'Listes à puces et numérotées', 'smartseo-ai' ),
                'status' => $lists['status'],
                'recommendation' => $lists['recommendation'],
            ),
            array(
                'label' => __( 'Éléments visuels', 'smartseo-ai' ),
                'status' => $visual_elements['status'],
                'recommendation' => $visual_elements['recommendation'],
            ),
        );
        
        // Calculer le score
        $score = $this->calculate_score( $headings, $lists, $visual_elements );
        
        // Retourner les résultats
        return array(
            'score' => $score,
            'headings' => $headings,
            'lists' => $lists,
            'visual_elements' => $visual_elements,
            'checks' => $checks,
        );
    }

    /**
     * Analyse les titres du contenu
     *
     * @param string $content Contenu à analyser.
     * @param string $title   Titre de l'article.
     * @return array Résultats de l'analyse.
     */
    private function analyze_headings( $content, $title ) {
        // Extraire les titres
        preg_match_all( '/<h([1-6])[^>]*>(.*?)<\/h\1>/i', $content, $matches, PREG_SET_ORDER );
        
        $headings = array();
        $heading_levels = array();
        
        foreach ( $matches as $match ) {
            $level = intval( $match[1] );
            $text = strip_tags( $match[2] );
            
            $headings[] = array(
                'level' => $level,
                'text' => $text,
            );
            
            $heading_levels[] = $level;
        }
        
        // Vérifier la hiérarchie des titres
        $hierarchy_issues = $this->check_heading_hierarchy( $heading_levels );
        
        // Vérifier la présence d'un H1
        $has_h1 = in_array( 1, $heading_levels );
        
        // Vérifier la présence de H2
        $has_h2 = in_array( 2, $heading_levels );
        
        // Vérifier la longueur des titres
        $long_headings = 0;
        foreach ( $headings as $heading ) {
            if ( str_word_count( $heading['text'] ) > 10 ) {
                $long_headings++;
            }
        }
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( count( $headings ) >= 3 && $has_h2 && $hierarchy_issues === 0 && $long_headings === 0 ) {
            $status = 'good';
            $recommendation = __( 'Bonne structure de titres. Les titres sont bien hiérarchisés et de longueur appropriée.', 'smartseo-ai' );
        } elseif ( count( $headings ) >= 2 && $hierarchy_issues <= 1 && $long_headings <= 1 ) {
            $status = 'average';
            $recommendation = __( 'Structure de titres acceptable. Quelques améliorations pourraient être apportées.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'Structure de titres à améliorer. Utilisez des titres hiérarchisés (H2, H3, etc.) pour structurer votre contenu.', 'smartseo-ai' );
        }
        
        // Ajouter des recommandations spécifiques
        if ( ! $has_h1 && ! $has_h2 ) {
            $recommendation .= ' ' . __( 'Ajoutez des titres H2 pour structurer votre contenu.', 'smartseo-ai' );
        } elseif ( $hierarchy_issues > 0 ) {
            $recommendation .= ' ' . __( 'Respectez la hiérarchie des titres (ne sautez pas de niveaux).', 'smartseo-ai' );
        }
        
        if ( $long_headings > 0 ) {
            $recommendation .= ' ' . __( 'Raccourcissez les titres trop longs (plus de 10 mots).', 'smartseo-ai' );
        }
        
        return array(
            'headings' => $headings,
            'has_h1' => $has_h1,
            'has_h2' => $has_h2,
            'hierarchy_issues' => $hierarchy_issues,
            'long_headings' => $long_headings,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Vérifie la hiérarchie des titres
     *
     * @param array $heading_levels Niveaux des titres.
     * @return int Nombre de problèmes de hiérarchie.
     */
    private function check_heading_hierarchy( $heading_levels ) {
        $issues = 0;
        $prev_level = 0;
        
        foreach ( $heading_levels as $level ) {
            // Si on saute un niveau (ex: H2 -> H4)
            if ( $prev_level > 0 && $level > $prev_level + 1 ) {
                $issues++;
            }
            
            $prev_level = $level;
        }
        
        return $issues;
    }

    /**
     * Analyse les listes du contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_lists( $content ) {
        // Compter les listes à puces
        preg_match_all( '/<ul[^>]*>.*?<\/ul>/is', $content, $ul_matches );
        $ul_count = count( $ul_matches[0] );
        
        // Compter les listes numérotées
        preg_match_all( '/<ol[^>]*>.*?<\/ol>/is', $content, $ol_matches );
        $ol_count = count( $ol_matches[0] );
        
        // Compter le nombre total de listes
        $total_lists = $ul_count + $ol_count;
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( $total_lists >= 2 ) {
            $status = 'good';
            $recommendation = __( 'Bonne utilisation des listes. Les listes améliorent la lisibilité et la structure du contenu.', 'smartseo-ai' );
        } elseif ( $total_lists >= 1 ) {
            $status = 'average';
            $recommendation = __( 'Utilisation acceptable des listes. Envisagez d\'ajouter plus de listes pour améliorer la lisibilité.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'Aucune liste détectée. Utilisez des listes à puces ou numérotées pour présenter des informations de manière structurée et améliorer la lisibilité.', 'smartseo-ai' );
        }
        
        return array(
            'ul_count' => $ul_count,
            'ol_count' => $ol_count,
            'total_lists' => $total_lists,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Analyse les éléments visuels du contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_visual_elements( $content ) {
        // Compter les images
        preg_match_all( '/<img[^>]*>/i', $content, $img_matches );
        $img_count = count( $img_matches[0] );
        
        // Compter les tableaux
        preg_match_all( '/<table[^>]*>.*?<\/table>/is', $content, $table_matches );
        $table_count = count( $table_matches[0] );
        
        // Compter les blocs de citation
        preg_match_all( '/<blockquote[^>]*>.*?<\/blockquote>/is', $content, $blockquote_matches );
        $blockquote_count = count( $blockquote_matches[0] );
        
        // Compter le nombre total d'éléments visuels
        $total_elements = $img_count + $table_count + $blockquote_count;
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( $total_elements >= 3 ) {
            $status = 'good';
            $recommendation = __( 'Bonne utilisation des éléments visuels. Les images, tableaux et citations améliorent l\'engagement et la compréhension.', 'smartseo-ai' );
        } elseif ( $total_elements >= 1 ) {
            $status = 'average';
            $recommendation = __( 'Utilisation acceptable des éléments visuels. Envisagez d\'ajouter plus d\'images, tableaux ou citations pour améliorer l\'engagement.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'Aucun élément visuel détecté. Ajoutez des images, tableaux ou citations pour améliorer l\'engagement et la compréhension.', 'smartseo-ai' );
        }
        
        return array(
            'img_count' => $img_count,
            'table_count' => $table_count,
            'blockquote_count' => $blockquote_count,
            'total_elements' => $total_elements,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Calcule le score global de l'analyse de structure
     *
     * @param array $headings        Résultats de l'analyse des titres.
     * @param array $lists           Résultats de l'analyse des listes.
     * @param array $visual_elements Résultats de l'analyse des éléments visuels.
     * @return int Score (0-100).
     */
    private function calculate_score( $headings, $lists, $visual_elements ) {
        // Pondération des différents facteurs
        $weights = array(
            'headings' => 0.5,
            'lists' => 0.25,
            'visual_elements' => 0.25,
        );
        
        // Convertir les statuts en scores numériques
        $scores = array(
            'headings' => $this->status_to_score( $headings['status'] ),
            'lists' => $this->status_to_score( $lists['status'] ),
            'visual_elements' => $this->status_to_score( $visual_elements['status'] ),
        );
        
        // Calculer le score pondéré
        $weighted_score = 
            $scores['headings'] * $weights['headings'] +
            $scores['lists'] * $weights['lists'] +
            $scores['visual_elements'] * $weights['visual_elements'];
        
        // Arrondir le score
        return round( $weighted_score );
    }

    /**
     * Convertit un statut en score numérique
     *
     * @param string $status Statut (good, average, poor).
     * @return int Score (0-100).
     */
    private function status_to_score( $status ) {
        switch ( $status ) {
            case 'good':
                return 100;
            case 'average':
                return 50;
            case 'poor':
                return 0;
            default:
                return 0;
        }
    }
}
