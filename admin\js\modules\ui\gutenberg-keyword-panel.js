/**
 * Module de gestion du panneau de mots-clés pour Gutenberg
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire du panneau de mots-clés pour Gutenberg
     */
    const G<PERSON>nbergKeywordPanel = {
        /**
         * Initialise le gestionnaire du panneau de mots-clés
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Rend le panneau de mots-clés
         * @return {string} HTML du panneau
         */
        render: function() {
            const { __ } = wp.i18n;
            const { Button, TextControl, Spinner } = wp.components;
            const { Fragment } = wp.element;
            
            return (
                <Fragment>
                    <div className="smartseo-ai-panel-header">
                        <h3>{__('Analyse de mots-clés', 'smartseo-ai')}</h3>
                        <p>{__('Analysez et trouvez les meilleurs mots-clés pour votre contenu.', 'smartseo-ai')}</p>
                    </div>
                    
                    <div className="smartseo-ai-panel-content">
                        <TextControl
                            label={__('Mot-clé principal', 'smartseo-ai')}
                            id="smartseo-ai-focus-keyword-analysis"
                            placeholder={__('Entrez votre mot-clé principal', 'smartseo-ai')}
                        />
                        
                        <div className="smartseo-ai-button-group">
                            <Button
                                isPrimary
                                className="smartseo-ai-analyze-keywords"
                            >
                                {__('Analyser les mots-clés', 'smartseo-ai')}
                            </Button>
                            
                            <Button
                                isPrimary
                                className="smartseo-ai-get-trends"
                            >
                                {__('Obtenir les tendances', 'smartseo-ai')}
                            </Button>
                        </div>
                    </div>
                    
                    <div className="smartseo-ai-results-container" id="smartseo-ai-keyword-results">
                        <div className="smartseo-ai-loading" style={{ display: 'none' }}>
                            <Spinner />
                            <p>{__('Analyse en cours...', 'smartseo-ai')}</p>
                        </div>
                        
                        <div className="smartseo-ai-results" style={{ display: 'none' }}>
                            <h4>{__('Résultats de l\'analyse', 'smartseo-ai')}</h4>
                            <div className="smartseo-ai-results-content"></div>
                        </div>
                    </div>
                </Fragment>
            );
        },

        /**
         * Affiche les résultats d'analyse des mots-clés
         * @param {Object} results Résultats d'analyse
         */
        showResults: function(results) {
            const { __ } = wp.i18n;
            const $container = $('#smartseo-ai-keyword-results');
            const $loading = $container.find('.smartseo-ai-loading');
            const $results = $container.find('.smartseo-ai-results');
            const $content = $results.find('.smartseo-ai-results-content');
            
            // Masquer le chargement
            $loading.hide();
            
            // Vider le contenu précédent
            $content.empty();
            
            // Afficher les résultats
            $results.show();
            
            // Vérifier si les résultats sont valides
            if (!results || (!results.primary_keyword && (!results.secondary_keywords || results.secondary_keywords.length === 0))) {
                $content.append('<p>' + __('Aucun résultat disponible.', 'smartseo-ai') + '</p>');
                return;
            }
            
            // Afficher le mot-clé principal
            if (results.primary_keyword) {
                const primaryKeyword = results.primary_keyword;
                
                $content.append('<h5>' + __('Mot-clé principal', 'smartseo-ai') + '</h5>');
                
                const $primaryItem = $('<div class="smartseo-ai-keyword-item smartseo-ai-primary-keyword"></div>');
                $primaryItem.append('<div class="smartseo-ai-keyword-name">' + primaryKeyword.keyword + '</div>');
                
                const $primaryDetails = $('<div class="smartseo-ai-keyword-details"></div>');
                $primaryDetails.append('<div class="smartseo-ai-keyword-volume"><span>' + __('Volume', 'smartseo-ai') + ':</span> ' + primaryKeyword.volume + '</div>');
                $primaryDetails.append('<div class="smartseo-ai-keyword-difficulty"><span>' + __('Difficulté', 'smartseo-ai') + ':</span> ' + primaryKeyword.difficulty + '</div>');
                $primaryDetails.append('<div class="smartseo-ai-keyword-intent"><span>' + __('Intention', 'smartseo-ai') + ':</span> ' + primaryKeyword.intent + '</div>');
                
                $primaryItem.append($primaryDetails);
                
                $primaryItem.append('<div class="smartseo-ai-keyword-actions">' +
                    '<button class="button smartseo-ai-use-keyword" data-keyword="' + primaryKeyword.keyword + '">' + __('Utiliser', 'smartseo-ai') + '</button>' +
                    '</div>');
                
                $content.append($primaryItem);
            }
            
            // Afficher les mots-clés secondaires
            if (results.secondary_keywords && results.secondary_keywords.length > 0) {
                $content.append('<h5>' + __('Mots-clés secondaires', 'smartseo-ai') + '</h5>');
                
                const $secondaryList = $('<div class="smartseo-ai-keyword-list"></div>');
                
                results.secondary_keywords.forEach(keyword => {
                    const $item = $('<div class="smartseo-ai-keyword-item"></div>');
                    $item.append('<div class="smartseo-ai-keyword-name">' + keyword.keyword + '</div>');
                    
                    const $details = $('<div class="smartseo-ai-keyword-details"></div>');
                    $details.append('<div class="smartseo-ai-keyword-volume"><span>' + __('Volume', 'smartseo-ai') + ':</span> ' + keyword.volume + '</div>');
                    $details.append('<div class="smartseo-ai-keyword-difficulty"><span>' + __('Difficulté', 'smartseo-ai') + ':</span> ' + keyword.difficulty + '</div>');
                    $details.append('<div class="smartseo-ai-keyword-intent"><span>' + __('Intention', 'smartseo-ai') + ':</span> ' + keyword.intent + '</div>');
                    
                    $item.append($details);
                    
                    $item.append('<div class="smartseo-ai-keyword-actions">' +
                        '<button class="button smartseo-ai-use-keyword" data-keyword="' + keyword.keyword + '">' + __('Utiliser', 'smartseo-ai') + '</button>' +
                        '</div>');
                    
                    $secondaryList.append($item);
                });
                
                $content.append($secondaryList);
            }
            
            // Afficher les mots-clés longue traîne
            if (results.long_tail_keywords && results.long_tail_keywords.length > 0) {
                $content.append('<h5>' + __('Mots-clés longue traîne', 'smartseo-ai') + '</h5>');
                
                const $longTailList = $('<div class="smartseo-ai-keyword-list"></div>');
                
                results.long_tail_keywords.forEach(keyword => {
                    const $item = $('<div class="smartseo-ai-keyword-item"></div>');
                    $item.append('<div class="smartseo-ai-keyword-name">' + keyword.keyword + '</div>');
                    
                    const $details = $('<div class="smartseo-ai-keyword-details"></div>');
                    $details.append('<div class="smartseo-ai-keyword-volume"><span>' + __('Volume', 'smartseo-ai') + ':</span> ' + keyword.volume + '</div>');
                    $details.append('<div class="smartseo-ai-keyword-difficulty"><span>' + __('Difficulté', 'smartseo-ai') + ':</span> ' + keyword.difficulty + '</div>');
                    $details.append('<div class="smartseo-ai-keyword-intent"><span>' + __('Intention', 'smartseo-ai') + ':</span> ' + keyword.intent + '</div>');
                    
                    $item.append($details);
                    
                    $item.append('<div class="smartseo-ai-keyword-actions">' +
                        '<button class="button smartseo-ai-use-keyword" data-keyword="' + keyword.keyword + '">' + __('Utiliser', 'smartseo-ai') + '</button>' +
                        '</div>');
                    
                    $longTailList.append($item);
                });
                
                $content.append($longTailList);
            }
            
            // Ajouter les écouteurs d'événements pour les boutons "Utiliser"
            $('.smartseo-ai-use-keyword').on('click', function() {
                const keyword = $(this).data('keyword');
                $('#smartseo-ai-focus-keyword, #smartseo-ai-focus-keyword-analysis').val(keyword);
                
                // Mettre à jour le mot-clé principal dans les métadonnées
                wp.data.dispatch('core/editor').editPost({ meta: { smartseo_ai_focus_keyword: keyword } });
                
                // Afficher un message de succès
                window.SmartSEOAI.UIManager.showSuccess(__('Mot-clé principal mis à jour.', 'smartseo-ai'));
            });
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.GutenbergKeywordPanel = GutenbergKeywordPanel;

})(jQuery);
