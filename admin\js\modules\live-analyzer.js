/**
 * Module d'analyse en temps réel
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire d'analyse en temps réel
     */
    const LiveAnalyzer = {
        /**
         * Timer pour l'analyse différée
         * @type {number}
         */
        analysisTimer: null,

        /**
         * Initialise le gestionnaire d'analyse en temps réel
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Planifie une analyse en temps réel
         */
        scheduleAnalysis: function() {
            // Annuler le timer précédent
            if (this.analysisTimer) {
                clearTimeout(this.analysisTimer);
            }
            
            // Planifier une nouvelle analyse après 2 secondes
            this.analysisTimer = setTimeout(function() {
                LiveAnalyzer.analyzeNow();
            }, 2000);
        },

        /**
         * Analyse immédiatement le contenu
         */
        analyzeNow: function() {
            // Récupérer les données nécessaires
            let postId = 0;
            let content = '';
            let title = '';
            let keyword = '';
            
            // Récupérer l'ID de l'article
            if (window.SmartSEOAI.UIManager.isGutenbergEditor()) {
                postId = wp.data.select('core/editor').getCurrentPostId();
            } else {
                postId = $('#post_ID').val();
            }
            
            // Récupérer le contenu
            if (window.SmartSEOAI.UIManager.isGutenbergEditor()) {
                content = wp.data.select('core/editor').getEditedPostContent();
            } else {
                if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                    content = tinyMCE.get('content').getContent();
                } else {
                    content = $('#content').val();
                }
            }
            
            // Récupérer le titre
            if (window.SmartSEOAI.UIManager.isGutenbergEditor()) {
                title = wp.data.select('core/editor').getEditedPostAttribute('title');
            } else {
                title = $('#title').val();
            }
            
            // Récupérer le mot-clé
            keyword = $('#smartseo-ai-focus-keyword').val();
            
            // Analyser le contenu
            this.analyzeContent(postId, content, title, keyword);
        },

        /**
         * Analyse le contenu
         * @param {number} postId  ID de l'article
         * @param {string} content Contenu à analyser
         * @param {string} title   Titre de l'article
         * @param {string} keyword Mot-clé principal
         */
        analyzeContent: function(postId, content, title, keyword) {
            // Afficher le chargement
            window.SmartSEOAI.UIManager.showLoading(smartseoAiWritingAssistant.i18n.analyzing, 'analysis');
            
            // Préparer les données
            const data = {
                action: 'smartseo_ai_analyze_content',
                nonce: smartseoAiWritingAssistant.nonce,
                post_id: postId,
                content: content,
                title: title,
                keyword: keyword
            };
            
            // Envoyer la requête AJAX
            $.ajax({
                url: smartseoAiWritingAssistant.ajaxUrl,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading('analysis');
                    
                    if (response.success) {
                        // Afficher les résultats
                        window.SmartSEOAI.UIManager.showLiveAnalysisResults(response.data);
                    } else {
                        // Afficher un message d'erreur
                        window.SmartSEOAI.UIManager.showError(response.data.message || smartseoAiWritingAssistant.i18n.error);
                    }
                },
                error: function(xhr, status, error) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading('analysis');
                    
                    // Afficher un message d'erreur
                    window.SmartSEOAI.UIManager.showError(smartseoAiWritingAssistant.i18n.error + ': ' + error);
                }
            });
        },

        /**
         * Analyse la lisibilité du contenu
         * @param {string} content Contenu à analyser
         * @return {Object} Résultats de l'analyse
         */
        analyzeReadability: function(content) {
            // Nettoyer le contenu
            const cleanContent = this.stripTags(content);
            
            // Calculer le score de lisibilité Flesch
            const fleschScore = this.calculateFleschScore(cleanContent);
            
            // Déterminer le niveau de lisibilité
            let level = '';
            let status = 'poor';
            
            if (fleschScore >= 90) {
                level = 'Très facile';
                status = 'good';
            } else if (fleschScore >= 80) {
                level = 'Facile';
                status = 'good';
            } else if (fleschScore >= 70) {
                level = 'Assez facile';
                status = 'good';
            } else if (fleschScore >= 60) {
                level = 'Standard';
                status = 'good';
            } else if (fleschScore >= 50) {
                level = 'Assez difficile';
                status = 'average';
            } else if (fleschScore >= 30) {
                level = 'Difficile';
                status = 'poor';
            } else {
                level = 'Très difficile';
                status = 'poor';
            }
            
            return {
                flesch_score: fleschScore.toFixed(1),
                level: level,
                status: status
            };
        },

        /**
         * Calcule le score de lisibilité Flesch
         * @param {string} text Texte à analyser
         * @return {number} Score de lisibilité Flesch
         */
        calculateFleschScore: function(text) {
            // Compter les mots
            const words = text.split(/\s+/).filter(word => word.length > 0);
            const wordCount = words.length;
            
            if (wordCount === 0) {
                return 0;
            }
            
            // Compter les phrases
            const sentences = text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
            const sentenceCount = sentences.length || 1;
            
            // Compter les syllabes (approximation)
            let syllableCount = 0;
            
            words.forEach(word => {
                syllableCount += this.countSyllables(word);
            });
            
            // Calculer le score Flesch
            const wordsPerSentence = wordCount / sentenceCount;
            const syllablesPerWord = syllableCount / wordCount;
            
            const fleschScore = 206.835 - (1.015 * wordsPerSentence) - (84.6 * syllablesPerWord);
            
            // Limiter le score entre 0 et 100
            return Math.max(0, Math.min(100, fleschScore));
        },

        /**
         * Compte approximativement le nombre de syllabes dans un mot
         * @param {string} word Mot à analyser
         * @return {number} Nombre approximatif de syllabes
         */
        countSyllables: function(word) {
            word = word.toLowerCase();
            
            // Supprimer les caractères non alphabétiques
            word = word.replace(/[^a-z]/g, '');
            
            if (word.length <= 3) {
                return 1;
            }
            
            // Compter les voyelles
            const vowels = word.match(/[aeiouy]+/g);
            
            if (!vowels) {
                return 1;
            }
            
            // Ajuster pour les diphtongues et les e muets à la fin
            let count = vowels.length;
            
            if (word.match(/[aeiouy]e$/)) {
                count--;
            }
            
            // Chaque mot a au moins une syllabe
            return Math.max(1, count);
        },

        /**
         * Supprime les balises HTML d'une chaîne
         * @param {string} html Chaîne HTML
         * @return {string} Chaîne sans balises HTML
         */
        stripTags: function(html) {
            if (!html) {
                return '';
            }
            
            // Créer un élément temporaire
            const temp = document.createElement('div');
            temp.innerHTML = html;
            
            // Récupérer le texte
            return temp.textContent || temp.innerText || '';
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.LiveAnalyzer = LiveAnalyzer;

})(jQuery);
