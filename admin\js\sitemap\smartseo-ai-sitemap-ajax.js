/**
 * Script JavaScript pour les actions AJAX du Sitemap XML
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire AJAX du Sitemap
     */
    const SitemapAjax = {
        /**
         * Initialise le gestionnaire AJAX
         */
        init: function() {
            // Ajouter les écouteurs d'événements
            this.bindEvents();
            
            // Initialiser les sliders de priorité
            this.initPrioritySliders();
            
            // Initialiser le bouton de copie d'URL
            this.initCopyUrlButton();
        },

        /**
         * Ajoute les écouteurs d'événements
         */
        bindEvents: function() {
            // Régénérer le sitemap
            $('#smartseo_ai_regenerate_sitemap').on('click', this.regenerateSitemap);
            
            // Valider le sitemap
            $('#smartseo_ai_validate_sitemap').on('click', this.validateSitemap);
            
            // Analyser le sitemap
            $('#smartseo_ai_analyze_sitemap').on('click', this.analyzeSitemap);
        },

        /**
         * Initialise les sliders de priorité
         */
        initPrioritySliders: function() {
            // Mettre à jour la valeur affichée lors du changement du slider
            $('input[type="range"]').on('input', function() {
                $(this).next('.smartseo-ai-range-value').text($(this).val());
            });
        },

        /**
         * Initialise le bouton de copie d'URL
         */
        initCopyUrlButton: function() {
            $('.smartseo-ai-copy-url').on('click', function() {
                const url = $(this).data('url');
                
                // Créer un élément temporaire
                const temp = $('<input>');
                $('body').append(temp);
                temp.val(url).select();
                
                // Copier l'URL
                document.execCommand('copy');
                
                // Supprimer l'élément temporaire
                temp.remove();
                
                // Changer le texte du bouton temporairement
                const $button = $(this);
                const originalText = $button.html();
                
                $button.html('<span class="dashicons dashicons-yes"></span> ' + 'URL copiée');
                
                setTimeout(function() {
                    $button.html(originalText);
                }, 2000);
            });
        },

        /**
         * Régénère le sitemap
         */
        regenerateSitemap: function() {
            // Afficher un message de chargement
            SitemapAjax.showMessage('regenerate', 'info', smartseoAiSitemap.i18n.regenerating);
            
            // Appeler l'API
            $.ajax({
                url: smartseoAiSitemap.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartseo_ai_regenerate_sitemap',
                    nonce: smartseoAiSitemap.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Afficher un message de succès
                        SitemapAjax.showMessage('regenerate', 'success', response.data.message);
                    } else {
                        // Afficher un message d'erreur
                        SitemapAjax.showMessage('regenerate', 'error', response.data.message);
                    }
                },
                error: function() {
                    // Afficher un message d'erreur
                    SitemapAjax.showMessage('regenerate', 'error', smartseoAiSitemap.i18n.regenerate_error);
                }
            });
        },

        /**
         * Valide le sitemap
         */
        validateSitemap: function() {
            // Afficher un message de chargement
            SitemapAjax.showMessage('validate', 'info', smartseoAiSitemap.i18n.validating);
            
            // Appeler l'API
            $.ajax({
                url: smartseoAiSitemap.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartseo_ai_validate_sitemap',
                    nonce: smartseoAiSitemap.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Afficher un message de succès
                        SitemapAjax.showMessage('validate', 'success', response.data.message);
                    } else {
                        // Afficher un message d'erreur
                        SitemapAjax.showMessage('validate', 'error', response.data.message);
                    }
                },
                error: function() {
                    // Afficher un message d'erreur
                    SitemapAjax.showMessage('validate', 'error', smartseoAiSitemap.i18n.validate_error);
                }
            });
        },

        /**
         * Analyse le sitemap
         */
        analyzeSitemap: function() {
            // Afficher un message de chargement
            SitemapAjax.showMessage('analyze', 'info', smartseoAiSitemap.i18n.analyzing);
            
            // Appeler l'API
            $.ajax({
                url: smartseoAiSitemap.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartseo_ai_analyze_sitemap',
                    nonce: smartseoAiSitemap.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Afficher un message de succès
                        SitemapAjax.showMessage('analyze', 'success', response.data.message);
                        
                        // Afficher les suggestions
                        SitemapAjax.displaySuggestions(response.data.suggestions);
                    } else {
                        // Afficher un message d'erreur
                        SitemapAjax.showMessage('analyze', 'error', response.data.message);
                    }
                },
                error: function() {
                    // Afficher un message d'erreur
                    SitemapAjax.showMessage('analyze', 'error', smartseoAiSitemap.i18n.analyze_error);
                }
            });
        },

        /**
         * Affiche un message
         *
         * @param {string} action Action concernée.
         * @param {string} type   Type de message (success, error, info).
         * @param {string} message Message à afficher.
         */
        showMessage: function(action, type, message) {
            let $container;
            
            // Déterminer le conteneur en fonction de l'action
            switch (action) {
                case 'regenerate':
                    $container = $('#smartseo_ai_regenerate_sitemap').parent();
                    break;
                    
                case 'validate':
                    $container = $('#smartseo_ai_sitemap_validation_result');
                    break;
                    
                case 'analyze':
                    $container = $('#smartseo_ai_sitemap_analysis_result');
                    break;
                    
                default:
                    return;
            }
            
            // Déterminer la classe en fonction du type
            let className;
            let icon;
            
            switch (type) {
                case 'success':
                    className = 'notice-success';
                    icon = 'yes-alt';
                    break;
                    
                case 'error':
                    className = 'notice-error';
                    icon = 'warning';
                    break;
                    
                case 'info':
                    className = 'notice-info';
                    icon = 'info';
                    break;
                    
                default:
                    className = 'notice-info';
                    icon = 'info';
                    break;
            }
            
            // Créer le message
            const $message = $('<div class="notice ' + className + ' inline"><p><span class="dashicons dashicons-' + icon + '"></span> ' + message + '</p></div>');
            
            // Afficher le message
            $container.html($message);
        },

        /**
         * Affiche les suggestions
         *
         * @param {Object} suggestions Suggestions à afficher.
         */
        displaySuggestions: function(suggestions) {
            // Récupérer le conteneur
            const $container = $('#smartseo_ai_sitemap_analysis_result');
            
            // Créer le contenu
            let content = '<div class="smartseo-ai-suggestions">';
            
            // Ajouter l'analyse de l'IA
            content += '<div class="smartseo-ai-suggestions-analysis">';
            content += '<h4>Analyse de l\'IA</h4>';
            content += '<div class="smartseo-ai-suggestions-analysis-content">';
            content += '<p>' + suggestions.ai_analysis.replace(/\n/g, '<br>') + '</p>';
            content += '</div>';
            content += '</div>';
            
            // Ajouter les URLs manquantes
            content += '<div class="smartseo-ai-suggestions-missing-urls">';
            content += '<h4>URLs manquantes</h4>';
            content += '<ul>';
            
            suggestions.missing_urls.forEach(function(url) {
                content += '<li><a href="' + url + '" target="_blank">' + url + '</a></li>';
            });
            
            content += '</ul>';
            content += '</div>';
            
            content += '</div>';
            
            // Afficher le contenu
            $container.append(content);
        }
    };
    
    // Initialiser le gestionnaire AJAX au chargement du document
    $(document).ready(function() {
        SitemapAjax.init();
    });
    
})(jQuery);
