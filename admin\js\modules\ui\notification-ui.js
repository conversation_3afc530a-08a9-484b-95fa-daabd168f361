/**
 * Module de gestion des notifications
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire des notifications
     */
    const NotificationUI = {
        /**
         * Initialise le gestionnaire des notifications
         */
        init: function() {
            // Créer le conteneur de notifications s'il n'existe pas
            if ($('#smartseo-ai-notifications').length === 0) {
                $('body').append('<div id="smartseo-ai-notifications"></div>');
            }
        },

        /**
         * Affiche un message de succès
         * @param {string} message Message à afficher
         * @param {string} target  Cible du message (optionnel)
         */
        showSuccess: function(message, target) {
            this.showNotification(message, 'success', target);
        },

        /**
         * Affiche un message d'erreur
         * @param {string} message Message à afficher
         * @param {string} target  Cible du message (optionnel)
         */
        showError: function(message, target) {
            this.showNotification(message, 'error', target);
        },

        /**
         * Affiche un message d'information
         * @param {string} message Message à afficher
         * @param {string} target  Cible du message (optionnel)
         */
        showInfo: function(message, target) {
            this.showNotification(message, 'info', target);
        },

        /**
         * Affiche un message d'avertissement
         * @param {string} message Message à afficher
         * @param {string} target  Cible du message (optionnel)
         */
        showWarning: function(message, target) {
            this.showNotification(message, 'warning', target);
        },

        /**
         * Affiche une notification
         * @param {string} message Message à afficher
         * @param {string} type    Type de notification (success, error, info, warning)
         * @param {string} target  Cible du message (optionnel)
         */
        showNotification: function(message, type, target) {
            // Déterminer le conteneur de notification
            let $container;
            
            if (target) {
                // Chercher un conteneur de notification spécifique à la cible
                const $targetContainer = $('#smartseo-ai-notification-' + target);
                
                if ($targetContainer.length > 0) {
                    $container = $targetContainer;
                } else {
                    // Créer un conteneur de notification spécifique à la cible
                    const $targetElement = $('#' + target);
                    
                    if ($targetElement.length > 0) {
                        $container = $('<div id="smartseo-ai-notification-' + target + '" class="smartseo-ai-notification-container"></div>');
                        $targetElement.after($container);
                    } else {
                        // Utiliser le conteneur global
                        $container = $('#smartseo-ai-notifications');
                    }
                }
            } else {
                // Utiliser le conteneur global
                $container = $('#smartseo-ai-notifications');
            }
            
            // Créer la notification
            const $notification = $('<div class="smartseo-ai-notification smartseo-ai-notification-' + type + '"></div>');
            
            // Ajouter l'icône
            let icon = '';
            switch (type) {
                case 'success':
                    icon = '✅';
                    break;
                case 'error':
                    icon = '❌';
                    break;
                case 'info':
                    icon = 'ℹ️';
                    break;
                case 'warning':
                    icon = '⚠️';
                    break;
            }
            
            $notification.append('<div class="smartseo-ai-notification-icon">' + icon + '</div>');
            
            // Ajouter le message
            $notification.append('<div class="smartseo-ai-notification-message">' + message + '</div>');
            
            // Ajouter le bouton de fermeture
            $notification.append('<div class="smartseo-ai-notification-close">×</div>');
            
            // Ajouter la notification au conteneur
            $container.append($notification);
            
            // Ajouter l'écouteur d'événement pour le bouton de fermeture
            $notification.find('.smartseo-ai-notification-close').on('click', function() {
                $(this).parent().fadeOut(300, function() {
                    $(this).remove();
                });
            });
            
            // Masquer automatiquement la notification après 5 secondes
            setTimeout(function() {
                $notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.NotificationUI = NotificationUI;

})(jQuery);
