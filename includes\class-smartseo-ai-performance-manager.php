<?php
/**
 * Gestionnaire de performance et fallback pour SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les performances et le fallback des API IA
 */
class SmartSEO_AI_Performance_Manager {

    /**
     * Instance singleton
     *
     * @var SmartSEO_AI_Performance_Manager
     */
    private static $instance = null;

    /**
     * Table des statistiques
     *
     * @var string
     */
    private $stats_table;

    /**
     * Paramètres de cache
     *
     * @var array
     */
    private $cache_settings;

    /**
     * Gestionnaire de cache
     *
     * @var SmartSEO_AI_Cache_Manager
     */
    private $cache_manager;

    /**
     * Constructeur
     */
    private function __construct() {
        global $wpdb;
        $this->stats_table = $wpdb->prefix . 'smartseo_ai_stats';
        $this->cache_settings = get_option( 'smartseo_ai_cache_settings', array() );
        $this->cache_manager = SmartSEO_AI_Cache_Manager::get_instance();
        
        // Valeurs par défaut
        $this->cache_settings = wp_parse_args( $this->cache_settings, array(
            'enable_fallback' => 'yes',
            'retry_attempts' => 3,
            'retry_delay' => 2,
        ) );
    }

    /**
     * Récupère l'instance singleton
     *
     * @return SmartSEO_AI_Performance_Manager
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Exécute une requête IA avec retry et fallback
     *
     * @param callable $primary_callback Fonction principale à exécuter.
     * @param callable $fallback_callback Fonction de fallback (optionnel).
     * @param array    $context Contexte pour les logs et stats.
     * @return mixed Résultat de l'exécution.
     */
    public function execute_with_fallback( $primary_callback, $fallback_callback = null, $context = array() ) {
        $start_time = microtime( true );
        $attempts = 0;
        $max_attempts = $this->cache_settings['retry_attempts'];
        $last_error = null;

        // Tentatives avec retry
        while ( $attempts < $max_attempts ) {
            $attempts++;
            
            try {
                $result = call_user_func( $primary_callback );
                
                if ( ! is_wp_error( $result ) ) {
                    // Succès - enregistrer les stats
                    $this->record_stats( $context, true, microtime( true ) - $start_time, $attempts );
                    return $result;
                }
                
                $last_error = $result;
                
                // Log de l'erreur
                if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                    error_log( "SmartSEO AI Performance: Tentative {$attempts}/{$max_attempts} échouée - " . $last_error->get_error_message() );
                }
                
            } catch ( Exception $e ) {
                $last_error = new WP_Error( 'exception', $e->getMessage() );
                
                // Log de l'exception
                if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                    error_log( "SmartSEO AI Performance: Exception lors de la tentative {$attempts} - " . $e->getMessage() );
                }
            }

            // Attendre avant la prochaine tentative (sauf pour la dernière)
            if ( $attempts < $max_attempts ) {
                sleep( $this->cache_settings['retry_delay'] * $attempts ); // Backoff exponentiel
            }
        }

        // Toutes les tentatives ont échoué, essayer le fallback
        if ( $fallback_callback && 'yes' === $this->cache_settings['enable_fallback'] ) {
            try {
                $result = call_user_func( $fallback_callback );
                
                if ( ! is_wp_error( $result ) ) {
                    // Succès avec fallback
                    $context['used_fallback'] = true;
                    $this->record_stats( $context, true, microtime( true ) - $start_time, $attempts );
                    
                    // Log du succès avec fallback
                    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                        error_log( "SmartSEO AI Performance: Succès avec fallback après {$attempts} tentatives" );
                    }
                    
                    return $result;
                }
            } catch ( Exception $e ) {
                // Log de l'échec du fallback
                if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                    error_log( "SmartSEO AI Performance: Échec du fallback - " . $e->getMessage() );
                }
            }
        }

        // Échec total - enregistrer les stats
        $this->record_stats( $context, false, microtime( true ) - $start_time, $attempts, $last_error );
        
        return $last_error ?: new WP_Error( 'total_failure', __( 'Toutes les tentatives ont échoué.', 'smartseo-ai' ) );
    }

    /**
     * Optimise le contenu avec cache et fallback
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu de l'article.
     * @param string $title Titre de l'article.
     * @param array  $options Options d'optimisation.
     * @return array|WP_Error Résultats de l'optimisation.
     */
    public function optimize_content_with_cache( $post_id, $content, $title, $options = array() ) {
        $provider = get_option( 'smartseo_ai_provider', 'openai' );
        $fallback_provider = ( 'openai' === $provider ) ? 'gemini' : 'openai';
        
        // Générer la clé de cache
        $cache_key = $this->cache_manager->generate_cache_key( $content, $title, $provider, $options );
        
        // Vérifier le cache
        $cached_result = $this->cache_manager->get( $cache_key );
        if ( false !== $cached_result ) {
            return $cached_result;
        }

        // Préparer les callbacks
        $primary_callback = function() use ( $post_id, $content, $title, $provider ) {
            if ( 'openai' === $provider ) {
                $api = new SmartSEO_AI_API();
            } else {
                $api = new SmartSEO_AI_Gemini();
            }
            return $api->optimize_content( $post_id, $content, $title );
        };

        $fallback_callback = null;
        if ( 'yes' === $this->cache_settings['enable_fallback'] ) {
            $fallback_callback = function() use ( $post_id, $content, $title, $fallback_provider ) {
                if ( 'openai' === $fallback_provider ) {
                    $api = new SmartSEO_AI_API();
                } else {
                    $api = new SmartSEO_AI_Gemini();
                }
                return $api->optimize_content( $post_id, $content, $title );
            };
        }

        // Contexte pour les stats
        $context = array(
            'post_id' => $post_id,
            'optimization_type' => 'content_optimization',
            'provider' => $provider,
            'fallback_provider' => $fallback_provider,
        );

        // Exécuter avec fallback
        $result = $this->execute_with_fallback( $primary_callback, $fallback_callback, $context );

        // Mettre en cache si succès
        if ( ! is_wp_error( $result ) ) {
            $this->cache_manager->set( $cache_key, $result, $provider );
        }

        return $result;
    }

    /**
     * Enregistre les statistiques d'une opération
     *
     * @param array $context Contexte de l'opération.
     * @param bool  $success Succès de l'opération.
     * @param float $processing_time Temps de traitement.
     * @param int   $attempts Nombre de tentatives.
     * @param WP_Error $error Erreur éventuelle.
     */
    private function record_stats( $context, $success, $processing_time, $attempts, $error = null ) {
        global $wpdb;

        $provider = $context['provider'];
        if ( isset( $context['used_fallback'] ) && $context['used_fallback'] ) {
            $provider = $context['fallback_provider'];
        }

        $data = array(
            'post_id' => $context['post_id'] ?? 0,
            'optimization_type' => $context['optimization_type'] ?? 'unknown',
            'provider' => $provider,
            'tokens_used' => $context['tokens_used'] ?? 0,
            'processing_time' => $processing_time,
            'success' => $success ? 1 : 0,
            'error_message' => $error ? $error->get_error_message() : null,
        );

        $wpdb->insert( $this->stats_table, $data );
    }

    /**
     * Récupère les statistiques de performance
     *
     * @param array $filters Filtres optionnels.
     * @return array Statistiques de performance.
     */
    public function get_performance_stats( $filters = array() ) {
        global $wpdb;

        $where_clauses = array( '1=1' );
        $where_values = array();

        // Filtres
        if ( ! empty( $filters['provider'] ) ) {
            $where_clauses[] = 'provider = %s';
            $where_values[] = $filters['provider'];
        }

        if ( ! empty( $filters['date_from'] ) ) {
            $where_clauses[] = 'created_at >= %s';
            $where_values[] = $filters['date_from'];
        }

        if ( ! empty( $filters['date_to'] ) ) {
            $where_clauses[] = 'created_at <= %s';
            $where_values[] = $filters['date_to'];
        }

        $where_sql = implode( ' AND ', $where_clauses );

        // Statistiques générales
        $general_stats = $wpdb->get_row( $wpdb->prepare(
            "SELECT 
                COUNT(*) as total_requests,
                SUM(success) as successful_requests,
                AVG(processing_time) as avg_processing_time,
                MAX(processing_time) as max_processing_time,
                MIN(processing_time) as min_processing_time,
                SUM(tokens_used) as total_tokens
             FROM {$this->stats_table} 
             WHERE {$where_sql}",
            $where_values
        ), ARRAY_A );

        // Statistiques par fournisseur
        $provider_stats = $wpdb->get_results( $wpdb->prepare(
            "SELECT 
                provider,
                COUNT(*) as requests,
                SUM(success) as successes,
                AVG(processing_time) as avg_time,
                SUM(tokens_used) as tokens
             FROM {$this->stats_table} 
             WHERE {$where_sql}
             GROUP BY provider",
            $where_values
        ), ARRAY_A );

        // Statistiques par type d'optimisation
        $type_stats = $wpdb->get_results( $wpdb->prepare(
            "SELECT 
                optimization_type,
                COUNT(*) as requests,
                SUM(success) as successes,
                AVG(processing_time) as avg_time
             FROM {$this->stats_table} 
             WHERE {$where_sql}
             GROUP BY optimization_type",
            $where_values
        ), ARRAY_A );

        // Erreurs récentes
        $recent_errors = $wpdb->get_results( $wpdb->prepare(
            "SELECT provider, optimization_type, error_message, created_at
             FROM {$this->stats_table} 
             WHERE success = 0 AND {$where_sql}
             ORDER BY created_at DESC 
             LIMIT 10",
            $where_values
        ), ARRAY_A );

        return array(
            'general' => $general_stats ?: array(),
            'by_provider' => $provider_stats ?: array(),
            'by_type' => $type_stats ?: array(),
            'recent_errors' => $recent_errors ?: array(),
        );
    }

    /**
     * Vérifie la santé des API
     *
     * @return array État de santé des API.
     */
    public function check_api_health() {
        $health = array(
            'openai' => array( 'status' => 'unknown', 'response_time' => 0, 'error' => null ),
            'gemini' => array( 'status' => 'unknown', 'response_time' => 0, 'error' => null ),
        );

        // Test OpenAI
        try {
            $start_time = microtime( true );
            $openai_api = new SmartSEO_AI_API();
            $test_result = $openai_api->test_api_connection();
            $health['openai']['response_time'] = microtime( true ) - $start_time;
            
            if ( is_wp_error( $test_result ) ) {
                $health['openai']['status'] = 'error';
                $health['openai']['error'] = $test_result->get_error_message();
            } else {
                $health['openai']['status'] = 'healthy';
            }
        } catch ( Exception $e ) {
            $health['openai']['status'] = 'error';
            $health['openai']['error'] = $e->getMessage();
        }

        // Test Gemini
        try {
            $start_time = microtime( true );
            $gemini_api = new SmartSEO_AI_Gemini();
            $test_result = $gemini_api->test_api_connection();
            $health['gemini']['response_time'] = microtime( true ) - $start_time;
            
            if ( is_wp_error( $test_result ) ) {
                $health['gemini']['status'] = 'error';
                $health['gemini']['error'] = $test_result->get_error_message();
            } else {
                $health['gemini']['status'] = 'healthy';
            }
        } catch ( Exception $e ) {
            $health['gemini']['status'] = 'error';
            $health['gemini']['error'] = $e->getMessage();
        }

        return $health;
    }
}
