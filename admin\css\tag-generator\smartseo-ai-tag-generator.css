/**
 * Styles CSS pour le Générateur de Tags IA (éditeur classique)
 *
 * @package SmartSEO_AI
 */

/* Conteneur principal */
.smartseo-ai-tag-generator-metabox {
    margin-bottom: 15px;
}

/* Options */
.smartseo-ai-tag-generator-options {
    margin-bottom: 15px;
}

.smartseo-ai-tag-generator-option {
    margin-bottom: 8px;
}

.smartseo-ai-tag-generator-option select {
    width: 100%;
    margin-top: 5px;
}

/* Actions */
.smartseo-ai-tag-generator-actions {
    margin-bottom: 15px;
}

/* Loader */
.smartseo-ai-tag-generator-loading {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.smartseo-ai-tag-generator-loading .spinner {
    float: none;
    margin-top: 0;
    margin-right: 5px;
}

.smartseo-ai-tag-generator-loading-text {
    color: #646970;
}

/* Résultats */
.smartseo-ai-tag-generator-results {
    margin-bottom: 15px;
}

.smartseo-ai-tag-generator-existing-tags,
.smartseo-ai-tag-generator-optimal-tags,
.smartseo-ai-tag-generator-generated-tags {
    margin-bottom: 15px;
}

.smartseo-ai-tag-generator-existing-tags h4,
.smartseo-ai-tag-generator-optimal-tags h4,
.smartseo-ai-tag-generator-generated-tags h4 {
    margin: 0 0 8px;
    font-size: 14px;
    font-weight: 600;
}

.smartseo-ai-tag-generator-existing-tags-list,
.smartseo-ai-tag-generator-optimal-tags-list,
.smartseo-ai-tag-generator-generated-tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.smartseo-ai-tag-generator-tag,
.smartseo-ai-tag-generator-optimal-tag,
.smartseo-ai-tag-generator-generated-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1.4;
    background-color: #f0f0f1;
    color: #3c434a;
    cursor: pointer;
    transition: all 0.2s ease;
}

.smartseo-ai-tag-generator-tag {
    background-color: #e9e9e9;
    cursor: default;
}

.smartseo-ai-tag-generator-optimal-tag {
    background-color: #d5e5f6;
    color: #135e96;
}

.smartseo-ai-tag-generator-generated-tag {
    background-color: #f0f0f1;
}

.smartseo-ai-tag-generator-generated-tag.selected {
    background-color: #d5e5f6;
    color: #135e96;
}

.smartseo-ai-tag-generator-tag-score {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 10px;
    line-height: 1;
    background-color: rgba(0, 0, 0, 0.1);
}

.smartseo-ai-tag-generator-optimal-tag .smartseo-ai-tag-generator-tag-score,
.smartseo-ai-tag-generator-generated-tag.selected .smartseo-ai-tag-generator-tag-score {
    background-color: rgba(19, 94, 150, 0.2);
}

.smartseo-ai-tag-generator-no-tags {
    margin: 0;
    font-style: italic;
    color: #646970;
}

/* Erreur */
.smartseo-ai-tag-generator-error {
    margin-bottom: 15px;
    padding: 8px 12px;
    border-left: 4px solid #d63638;
    background-color: #fcf0f1;
}

.smartseo-ai-tag-generator-error-message {
    margin: 0;
    color: #d63638;
}
