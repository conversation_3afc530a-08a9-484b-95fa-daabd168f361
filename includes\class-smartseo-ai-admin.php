<?php
/**
 * Classe pour gérer l'interface d'administration
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'interface d'administration
 */
class SmartSEO_AI_Admin {

    /**
     * Constructeur
     */
    public function __construct() {
        // Ajouter le menu d'administration
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        
        // Ajouter une colonne dans la liste des articles
        add_filter( 'manage_posts_columns', array( $this, 'add_seo_score_column' ) );
        add_filter( 'manage_pages_columns', array( $this, 'add_seo_score_column' ) );
        
        // Remplir la colonne
        add_action( 'manage_posts_custom_column', array( $this, 'display_seo_score_column' ), 10, 2 );
        add_action( 'manage_pages_custom_column', array( $this, 'display_seo_score_column' ), 10, 2 );
    }

    /**
     * Ajoute le menu d'administration
     */
    public function add_admin_menu() {
        // Menu principal
        add_menu_page(
            __( 'SmartSEO AI', 'smartseo-ai' ),
            __( 'SmartSEO AI', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai',
            array( $this, 'render_dashboard_page' ),
            'dashicons-superhero',
            30
        );
        
        // Sous-menu pour le tableau de bord
        add_submenu_page(
            'smartseo-ai',
            __( 'Tableau de bord', 'smartseo-ai' ),
            __( 'Tableau de bord', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai',
            array( $this, 'render_dashboard_page' )
        );
        
        // Sous-menu pour les paramètres
        add_submenu_page(
            'smartseo-ai',
            __( 'Paramètres', 'smartseo-ai' ),
            __( 'Paramètres', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-settings',
            array( $this, 'render_settings_page' )
        );
    }

    /**
     * Affiche la page du tableau de bord
     */
    public function render_dashboard_page() {
        // Récupérer les articles avec leur score SEO
        $posts = $this->get_posts_with_seo_score();
        
        // Inclure le template
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/dashboard.php';
    }

    /**
     * Affiche la page des paramètres
     */
    public function render_settings_page() {
        // Inclure le template
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/settings.php';
    }

    /**
     * Récupère les articles avec leur score SEO
     *
     * @return array Articles avec leur score SEO.
     */
    private function get_posts_with_seo_score() {
        $args = array(
            'post_type'      => array( 'post', 'page' ),
            'post_status'    => 'publish',
            'posts_per_page' => 50,
            'meta_query'     => array(
                'relation' => 'OR',
                array(
                    'key'     => 'smartseo_ai_seo_score',
                    'compare' => 'EXISTS',
                ),
                array(
                    'key'     => 'smartseo_ai_seo_score',
                    'compare' => 'NOT EXISTS',
                ),
            ),
        );
        
        $query = new WP_Query( $args );
        $posts = array();
        
        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                
                $post_id = get_the_ID();
                $score = get_post_meta( $post_id, 'smartseo_ai_seo_score', true );
                
                $posts[] = array(
                    'id'         => $post_id,
                    'title'      => get_the_title(),
                    'permalink'  => get_permalink(),
                    'edit_link'  => get_edit_post_link(),
                    'post_type'  => get_post_type_object( get_post_type() )->labels->singular_name,
                    'date'       => get_the_date(),
                    'seo_score'  => ! empty( $score ) ? intval( $score ) : 0,
                    'has_score'  => ! empty( $score ),
                );
            }
            
            wp_reset_postdata();
        }
        
        return $posts;
    }

    /**
     * Ajoute une colonne pour le score SEO dans la liste des articles
     *
     * @param array $columns Colonnes existantes.
     * @return array Colonnes modifiées.
     */
    public function add_seo_score_column( $columns ) {
        $new_columns = array();
        
        foreach ( $columns as $key => $value ) {
            $new_columns[$key] = $value;
            
            // Ajouter la colonne après le titre
            if ( $key === 'title' ) {
                $new_columns['smartseo_ai_score'] = __( 'Score SEO', 'smartseo-ai' );
            }
        }
        
        return $new_columns;
    }

    /**
     * Affiche le contenu de la colonne du score SEO
     *
     * @param string $column_name Nom de la colonne.
     * @param int    $post_id     ID de l'article.
     */
    public function display_seo_score_column( $column_name, $post_id ) {
        if ( $column_name === 'smartseo_ai_score' ) {
            $score = get_post_meta( $post_id, 'smartseo_ai_seo_score', true );
            
            if ( ! empty( $score ) ) {
                $score_class = $this->get_score_class( $score );
                echo '<div class="smartseo-ai-score-indicator smartseo-ai-score-' . esc_attr( $score_class ) . '">';
                echo esc_html( $score ) . '/100';
                echo '</div>';
            } else {
                echo '<div class="smartseo-ai-score-indicator smartseo-ai-score-none">';
                echo '<a href="' . esc_url( get_edit_post_link( $post_id ) ) . '" class="button button-small">';
                echo '<span class="dashicons dashicons-superhero" style="font-size: 16px; vertical-align: text-bottom;"></span> ';
                echo esc_html__( 'Optimiser', 'smartseo-ai' );
                echo '</a>';
                echo '</div>';
            }
        }
    }

    /**
     * Retourne la classe CSS en fonction du score SEO
     *
     * @param int $score Score SEO.
     * @return string Classe CSS.
     */
    private function get_score_class( $score ) {
        if ( $score >= 80 ) {
            return 'good';
        } elseif ( $score >= 50 ) {
            return 'average';
        } else {
            return 'poor';
        }
    }
}
