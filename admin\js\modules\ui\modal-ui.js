/**
 * Module de gestion des modales
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire des modales
     */
    const ModalUI = {
        /**
         * Initialise le gestionnaire des modales
         */
        init: function() {
            // Créer le conteneur de modales s'il n'existe pas
            if ($('#smartseo-ai-modal-container').length === 0) {
                $('body').append('<div id="smartseo-ai-modal-container"></div>');
            }
            
            // Ajouter les écouteurs d'événements
            this.bindEvents();
        },

        /**
         * Ajoute les écouteurs d'événements
         */
        bindEvents: function() {
            // Fermer la modale en cliquant sur le fond
            $(document).on('click', '.smartseo-ai-modal-overlay', function(e) {
                if ($(e.target).hasClass('smartseo-ai-modal-overlay')) {
                    ModalUI.hideModal();
                }
            });
            
            // Fermer la modale en cliquant sur le bouton de fermeture
            $(document).on('click', '.smartseo-ai-modal-close', function() {
                ModalUI.hideModal();
            });
            
            // Fermer la modale en appuyant sur la touche Echap
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('.smartseo-ai-modal-overlay').is(':visible')) {
                    ModalUI.hideModal();
                }
            });
        },

        /**
         * Affiche une modale
         * @param {string} title   Titre de la modale
         * @param {string} content Contenu de la modale
         * @param {Object} options Options de la modale
         */
        showModal: function(title, content, options) {
            // Options par défaut
            const defaults = {
                width: '600px',
                height: 'auto',
                buttons: [],
                closeButton: true,
                overlay: true,
                overlayClose: true,
                escClose: true,
                onOpen: null,
                onClose: null
            };
            
            // Fusionner les options
            const settings = $.extend({}, defaults, options);
            
            // Créer la modale
            const $overlay = $('<div class="smartseo-ai-modal-overlay"></div>');
            const $modal = $('<div class="smartseo-ai-modal"></div>');
            
            // Appliquer les dimensions
            $modal.css({
                width: settings.width,
                height: settings.height
            });
            
            // Ajouter l'en-tête
            const $header = $('<div class="smartseo-ai-modal-header"></div>');
            $header.append('<h3 class="smartseo-ai-modal-title">' + title + '</h3>');
            
            if (settings.closeButton) {
                $header.append('<button type="button" class="smartseo-ai-modal-close">×</button>');
            }
            
            $modal.append($header);
            
            // Ajouter le contenu
            const $body = $('<div class="smartseo-ai-modal-body"></div>');
            $body.append(content);
            $modal.append($body);
            
            // Ajouter les boutons
            if (settings.buttons.length > 0) {
                const $footer = $('<div class="smartseo-ai-modal-footer"></div>');
                
                settings.buttons.forEach(button => {
                    const $button = $('<button type="button" class="button"></button>');
                    
                    if (button.primary) {
                        $button.addClass('button-primary');
                    }
                    
                    if (button.class) {
                        $button.addClass(button.class);
                    }
                    
                    $button.text(button.text);
                    
                    if (button.click) {
                        $button.on('click', function() {
                            button.click.call(this);
                        });
                    }
                    
                    $footer.append($button);
                });
                
                $modal.append($footer);
            }
            
            // Ajouter la modale au conteneur
            $overlay.append($modal);
            $('#smartseo-ai-modal-container').empty().append($overlay);
            
            // Afficher la modale
            $overlay.fadeIn(300);
            
            // Appeler le callback d'ouverture
            if (settings.onOpen && typeof settings.onOpen === 'function') {
                settings.onOpen.call(this);
            }
            
            // Stocker les options
            $overlay.data('settings', settings);
        },

        /**
         * Masque la modale
         */
        hideModal: function() {
            const $overlay = $('.smartseo-ai-modal-overlay');
            
            // Récupérer les options
            const settings = $overlay.data('settings');
            
            // Masquer la modale
            $overlay.fadeOut(300, function() {
                $(this).remove();
                
                // Appeler le callback de fermeture
                if (settings && settings.onClose && typeof settings.onClose === 'function') {
                    settings.onClose.call(this);
                }
            });
        },

        /**
         * Affiche une modale de confirmation
         * @param {string}   message  Message à afficher
         * @param {Function} callback Fonction à appeler en cas de confirmation
         * @param {Object}   options  Options de la modale
         */
        confirm: function(message, callback, options) {
            // Options par défaut
            const defaults = {
                title: 'Confirmation',
                confirmText: 'OK',
                cancelText: 'Annuler',
                confirmClass: 'button-primary',
                cancelClass: '',
                width: '400px'
            };
            
            // Fusionner les options
            const settings = $.extend({}, defaults, options);
            
            // Créer le contenu
            const content = '<p>' + message + '</p>';
            
            // Créer les boutons
            const buttons = [
                {
                    text: settings.confirmText,
                    primary: true,
                    class: settings.confirmClass,
                    click: function() {
                        ModalUI.hideModal();
                        if (callback && typeof callback === 'function') {
                            callback.call(this, true);
                        }
                    }
                },
                {
                    text: settings.cancelText,
                    class: settings.cancelClass,
                    click: function() {
                        ModalUI.hideModal();
                        if (callback && typeof callback === 'function') {
                            callback.call(this, false);
                        }
                    }
                }
            ];
            
            // Afficher la modale
            this.showModal(settings.title, content, {
                width: settings.width,
                buttons: buttons
            });
        },

        /**
         * Affiche une modale d'alerte
         * @param {string} message Message à afficher
         * @param {Object} options Options de la modale
         */
        alert: function(message, options) {
            // Options par défaut
            const defaults = {
                title: 'Alerte',
                buttonText: 'OK',
                buttonClass: 'button-primary',
                width: '400px'
            };
            
            // Fusionner les options
            const settings = $.extend({}, defaults, options);
            
            // Créer le contenu
            const content = '<p>' + message + '</p>';
            
            // Créer les boutons
            const buttons = [
                {
                    text: settings.buttonText,
                    primary: true,
                    class: settings.buttonClass,
                    click: function() {
                        ModalUI.hideModal();
                    }
                }
            ];
            
            // Afficher la modale
            this.showModal(settings.title, content, {
                width: settings.width,
                buttons: buttons
            });
        },

        /**
         * Affiche une modale de formulaire
         * @param {string}   title    Titre de la modale
         * @param {string}   content  Contenu du formulaire
         * @param {Function} callback Fonction à appeler en cas de soumission
         * @param {Object}   options  Options de la modale
         */
        form: function(title, content, callback, options) {
            // Options par défaut
            const defaults = {
                submitText: 'Enregistrer',
                cancelText: 'Annuler',
                submitClass: 'button-primary',
                cancelClass: '',
                width: '600px'
            };
            
            // Fusionner les options
            const settings = $.extend({}, defaults, options);
            
            // Créer le contenu
            const formContent = '<form class="smartseo-ai-modal-form">' + content + '</form>';
            
            // Créer les boutons
            const buttons = [
                {
                    text: settings.submitText,
                    primary: true,
                    class: settings.submitClass,
                    click: function() {
                        // Récupérer les données du formulaire
                        const $form = $('.smartseo-ai-modal-form');
                        const formData = {};
                        
                        $form.find('input, select, textarea').each(function() {
                            const $input = $(this);
                            const name = $input.attr('name');
                            
                            if (name) {
                                if ($input.attr('type') === 'checkbox') {
                                    formData[name] = $input.is(':checked');
                                } else if ($input.attr('type') === 'radio') {
                                    if ($input.is(':checked')) {
                                        formData[name] = $input.val();
                                    }
                                } else {
                                    formData[name] = $input.val();
                                }
                            }
                        });
                        
                        // Appeler le callback
                        if (callback && typeof callback === 'function') {
                            callback.call(this, formData);
                        }
                        
                        // Fermer la modale
                        ModalUI.hideModal();
                    }
                },
                {
                    text: settings.cancelText,
                    class: settings.cancelClass,
                    click: function() {
                        ModalUI.hideModal();
                    }
                }
            ];
            
            // Afficher la modale
            this.showModal(title, formContent, {
                width: settings.width,
                buttons: buttons
            });
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.ModalUI = ModalUI;

})(jQuery);
