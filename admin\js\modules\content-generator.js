/**
 * Module de génération de contenu
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire de génération de contenu
     */
    const ContentGenerator = {
        /**
         * Initialise le gestionnaire de génération de contenu
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Génère du contenu
         * @param {string} type    Type de contenu à générer
         * @param {number} postId  ID de l'article
         * @param {string} keyword Mot-clé principal
         * @param {string} topic   Sujet de l'article
         * @param {string} content Contenu existant (optionnel)
         */
        generateContent: function(type, postId, keyword, topic, content) {
            // Afficher le chargement
            window.SmartSEOAI.UIManager.showLoading(smartseoAiWritingAssistant.i18n.generating, 'content');
            
            // Récupérer le contenu existant si non fourni
            if (!content) {
                if (window.SmartSEOAI.UIManager.isGutenbergEditor()) {
                    content = wp.data.select('core/editor').getEditedPostContent();
                } else {
                    if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                        content = tinyMCE.get('content').getContent();
                    } else {
                        content = $('#content').val();
                    }
                }
            }
            
            // Préparer les données
            const data = {
                action: 'smartseo_ai_generate_content',
                nonce: smartseoAiWritingAssistant.nonce,
                post_id: postId,
                content_type: type,
                keyword: keyword,
                topic: topic,
                content: content
            };
            
            // Envoyer la requête AJAX
            $.ajax({
                url: smartseoAiWritingAssistant.ajaxUrl,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading('content');
                    
                    if (response.success) {
                        // Afficher les résultats
                        window.SmartSEOAI.UIManager.showContentResults(response.data, type);
                        
                        // Afficher un message de succès
                        window.SmartSEOAI.UIManager.showSuccess(smartseoAiWritingAssistant.i18n.success);
                    } else {
                        // Afficher un message d'erreur
                        window.SmartSEOAI.UIManager.showError(response.data.message || smartseoAiWritingAssistant.i18n.error);
                    }
                },
                error: function(xhr, status, error) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading('content');
                    
                    // Afficher un message d'erreur
                    window.SmartSEOAI.UIManager.showError(smartseoAiWritingAssistant.i18n.error + ': ' + error);
                }
            });
        },

        /**
         * Optimise automatiquement le contenu
         * @param {number} postId  ID de l'article
         * @param {string} keyword Mot-clé principal
         */
        optimizeAll: function(postId, keyword) {
            // Afficher une confirmation
            window.SmartSEOAI.ModalUI.confirm(
                'Cette action va optimiser automatiquement votre contenu en fonction du mot-clé "' + keyword + '". Voulez-vous continuer ?',
                function(confirmed) {
                    if (confirmed) {
                        // Afficher le chargement
                        window.SmartSEOAI.UIManager.showLoading(smartseoAiWritingAssistant.i18n.optimizing);
                        
                        // Préparer les données
                        const data = {
                            action: 'smartseo_ai_optimize_all',
                            nonce: smartseoAiWritingAssistant.nonce,
                            post_id: postId,
                            keyword: keyword
                        };
                        
                        // Envoyer la requête AJAX
                        $.ajax({
                            url: smartseoAiWritingAssistant.ajaxUrl,
                            type: 'POST',
                            data: data,
                            success: function(response) {
                                // Masquer le chargement
                                window.SmartSEOAI.UIManager.hideLoading();
                                
                                if (response.success) {
                                    // Appliquer les optimisations
                                    if (response.data.title) {
                                        if (window.SmartSEOAI.UIManager.isGutenbergEditor()) {
                                            wp.data.dispatch('core/editor').editPost({ title: response.data.title });
                                        } else {
                                            $('#title').val(response.data.title);
                                        }
                                    }
                                    
                                    if (response.data.meta_description) {
                                        if (window.SmartSEOAI.UIManager.isGutenbergEditor()) {
                                            wp.data.dispatch('core/editor').editPost({ meta: { smartseo_ai_meta_description: response.data.meta_description } });
                                        } else {
                                            $('#smartseo_ai_meta_description').val(response.data.meta_description);
                                        }
                                    }
                                    
                                    if (response.data.content) {
                                        if (window.SmartSEOAI.UIManager.isGutenbergEditor()) {
                                            wp.data.dispatch('core/editor').resetBlocks(wp.blocks.parse(response.data.content));
                                        } else {
                                            if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                                                tinyMCE.get('content').setContent(response.data.content);
                                            } else {
                                                $('#content').val(response.data.content);
                                            }
                                        }
                                    }
                                    
                                    // Afficher un message de succès
                                    window.SmartSEOAI.UIManager.showSuccess(response.data.message || 'Contenu optimisé avec succès !');
                                    
                                    // Mettre à jour l'analyse en temps réel
                                    window.SmartSEOAI.LiveAnalyzer.analyzeNow();
                                } else {
                                    // Afficher un message d'erreur
                                    window.SmartSEOAI.UIManager.showError(response.data.message || smartseoAiWritingAssistant.i18n.error);
                                }
                            },
                            error: function(xhr, status, error) {
                                // Masquer le chargement
                                window.SmartSEOAI.UIManager.hideLoading();
                                
                                // Afficher un message d'erreur
                                window.SmartSEOAI.UIManager.showError(smartseoAiWritingAssistant.i18n.error + ': ' + error);
                            }
                        });
                    }
                },
                {
                    title: 'Optimisation automatique',
                    confirmText: 'Optimiser',
                    cancelText: 'Annuler'
                }
            );
        },

        /**
         * Vérifie la non-duplication du contenu
         * @param {string} content Contenu à vérifier
         */
        checkPlagiarism: function(content) {
            // Afficher le chargement
            window.SmartSEOAI.UIManager.showLoading(smartseoAiWritingAssistant.i18n.checking);
            
            // Préparer les données
            const data = {
                action: 'smartseo_ai_check_plagiarism',
                nonce: smartseoAiWritingAssistant.nonce,
                content: content
            };
            
            // Envoyer la requête AJAX
            $.ajax({
                url: smartseoAiWritingAssistant.ajaxUrl,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading();
                    
                    if (response.success) {
                        // Créer le contenu de la modale
                        const result = response.data;
                        let content = '<div class="smartseo-ai-plagiarism-result">';
                        
                        // Afficher le pourcentage d'unicité
                        content += '<div class="smartseo-ai-plagiarism-score">';
                        content += '<div class="smartseo-ai-plagiarism-score-value">' + result.unique_percentage + '%</div>';
                        content += '<div class="smartseo-ai-plagiarism-score-label">Contenu unique</div>';
                        content += '</div>';
                        
                        // Afficher les sources de duplication
                        if (result.duplicate_sources && result.duplicate_sources.length > 0) {
                            content += '<div class="smartseo-ai-plagiarism-sources">';
                            content += '<h4>Sources de duplication (' + result.duplicate_percentage + '%)</h4>';
                            content += '<ul>';
                            
                            result.duplicate_sources.forEach(source => {
                                content += '<li>';
                                content += '<div class="smartseo-ai-plagiarism-source-url"><a href="' + source.url + '" target="_blank">' + source.url + '</a></div>';
                                content += '<div class="smartseo-ai-plagiarism-source-similarity">Similarité : ' + source.similarity + '%</div>';
                                content += '<div class="smartseo-ai-plagiarism-source-text">Texte correspondant : "' + source.matched_text + '"</div>';
                                content += '</li>';
                            });
                            
                            content += '</ul>';
                            content += '</div>';
                        } else {
                            content += '<div class="smartseo-ai-plagiarism-no-sources">';
                            content += '<p>Aucune source de duplication détectée.</p>';
                            content += '</div>';
                        }
                        
                        content += '</div>';
                        
                        // Afficher la modale
                        window.SmartSEOAI.ModalUI.showModal(
                            'Résultat de la vérification de non-duplication',
                            content,
                            {
                                width: '700px',
                                buttons: [
                                    {
                                        text: 'Fermer',
                                        primary: true,
                                        click: function() {
                                            window.SmartSEOAI.ModalUI.hideModal();
                                        }
                                    }
                                ]
                            }
                        );
                    } else {
                        // Afficher un message d'erreur
                        window.SmartSEOAI.UIManager.showError(response.data.message || smartseoAiWritingAssistant.i18n.error);
                    }
                },
                error: function(xhr, status, error) {
                    // Masquer le chargement
                    window.SmartSEOAI.UIManager.hideLoading();
                    
                    // Afficher un message d'erreur
                    window.SmartSEOAI.UIManager.showError(smartseoAiWritingAssistant.i18n.error + ': ' + error);
                }
            });
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.ContentGenerator = ContentGenerator;

})(jQuery);
