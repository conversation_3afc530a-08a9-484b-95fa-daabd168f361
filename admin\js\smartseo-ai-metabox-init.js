/**
 * Script d'initialisation pour la métabox de l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Initialisation de la métabox de l'Assistant de Rédaction SEO
     */
    const SmartSEOAIMetaboxInit = {
        /**
         * Initialise la métabox
         */
        init: function() {
            console.log('SmartSEO AI Metabox Init: Initialisation');
            
            // Ajouter les écouteurs d'événements pour les onglets
            this.initTabs();
            
            // Synchroniser les champs
            this.syncFields();
            
            console.log('SmartSEO AI Metabox Init: Initialisation terminée');
        },
        
        /**
         * Initialise les onglets
         */
        initTabs: function() {
            console.log('SmartSEO AI Metabox Init: Initialisation des onglets');
            
            // Ajouter les écouteurs d'événements pour les onglets
            $('.smartseo-ai-tab-nav').on('click', function() {
                const tab = $(this).data('tab');
                
                // Activer l'onglet
                $('.smartseo-ai-tab-nav').removeClass('active');
                $(this).addClass('active');
                
                // Afficher le panneau correspondant
                $('.smartseo-ai-tab-panel').removeClass('active');
                $('.smartseo-ai-tab-panel[data-tab="' + tab + '"]').addClass('active');
            });
            
            console.log('SmartSEO AI Metabox Init: Onglets initialisés avec succès');
        },
        
        /**
         * Synchronise les champs
         */
        syncFields: function() {
            console.log('SmartSEO AI Metabox Init: Synchronisation des champs');
            
            // Synchroniser le champ de mot-clé principal
            $('#smartseo_ai_focus_keyword').on('input', function() {
                const value = $(this).val();
                $('#smartseo-ai-focus-keyword, #smartseo-ai-focus-keyword-analysis, #smartseo-ai-trends-keyword').val(value);
            });
            
            $('#smartseo-ai-focus-keyword').on('input', function() {
                const value = $(this).val();
                $('#smartseo_ai_focus_keyword, #smartseo-ai-focus-keyword-analysis, #smartseo-ai-trends-keyword').val(value);
            });
            
            $('#smartseo-ai-focus-keyword-analysis').on('input', function() {
                const value = $(this).val();
                $('#smartseo_ai_focus_keyword, #smartseo-ai-focus-keyword, #smartseo-ai-trends-keyword').val(value);
            });
            
            $('#smartseo-ai-trends-keyword').on('input', function() {
                const value = $(this).val();
                $('#smartseo_ai_focus_keyword, #smartseo-ai-focus-keyword, #smartseo-ai-focus-keyword-analysis').val(value);
            });
            
            // Initialiser les valeurs
            const focusKeyword = $('#smartseo_ai_focus_keyword').val();
            if (focusKeyword) {
                $('#smartseo-ai-focus-keyword, #smartseo-ai-focus-keyword-analysis, #smartseo-ai-trends-keyword').val(focusKeyword);
            }
            
            console.log('SmartSEO AI Metabox Init: Champs synchronisés avec succès');
        }
    };
    
    // Initialiser la métabox au chargement du document
    $(document).ready(function() {
        SmartSEOAIMetaboxInit.init();
    });

})(jQuery);
