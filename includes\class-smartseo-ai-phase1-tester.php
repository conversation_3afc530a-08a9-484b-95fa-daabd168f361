<?php
/**
 * Testeur pour les améliorations Phase 1 de SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour tester les nouvelles fonctionnalités Phase 1
 */
class SmartSEO_AI_Phase1_Tester {

    /**
     * Instance singleton
     *
     * @var SmartSEO_AI_Phase1_Tester
     */
    private static $instance = null;

    /**
     * Résultats des tests
     *
     * @var array
     */
    private $test_results = array();

    /**
     * Constructeur
     */
    private function __construct() {
        // Hook pour ajouter une page de test dans l'admin
        add_action( 'admin_menu', array( $this, 'add_test_menu' ) );
        add_action( 'wp_ajax_smartseo_ai_run_phase1_tests', array( $this, 'ajax_run_tests' ) );
    }

    /**
     * Récupère l'instance singleton
     *
     * @return SmartSEO_AI_Phase1_Tester
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Ajoute le menu de test
     */
    public function add_test_menu() {
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            add_submenu_page(
                'smartseo-ai',
                __( 'Tests Phase 1', 'smartseo-ai' ),
                __( 'Tests Phase 1', 'smartseo-ai' ),
                'manage_options',
                'smartseo-ai-phase1-tests',
                array( $this, 'render_test_page' )
            );
        }
    }

    /**
     * Affiche la page de test
     */
    public function render_test_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'Tests Phase 1 - SmartSEO AI', 'smartseo-ai' ); ?></h1>
            
            <div class="notice notice-info">
                <p><?php _e( 'Cette page permet de tester les nouvelles fonctionnalités de la Phase 1.', 'smartseo-ai' ); ?></p>
            </div>

            <button type="button" class="button button-primary" id="run-phase1-tests">
                <?php _e( 'Lancer tous les tests', 'smartseo-ai' ); ?>
            </button>

            <div id="test-results" style="margin-top: 20px;"></div>
        </div>

        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('#run-phase1-tests').on('click', function() {
                var $button = $(this);
                var $results = $('#test-results');
                
                $button.prop('disabled', true).text('<?php _e( 'Tests en cours...', 'smartseo-ai' ); ?>');
                $results.html('<div class="notice notice-info"><p><?php _e( 'Exécution des tests...', 'smartseo-ai' ); ?></p></div>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'smartseo_ai_run_phase1_tests',
                        nonce: '<?php echo wp_create_nonce( 'smartseo_ai_phase1_tests' ); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $results.html(response.data.html);
                        } else {
                            $results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        $results.html('<div class="notice notice-error"><p><?php _e( 'Erreur lors de l\'exécution des tests.', 'smartseo-ai' ); ?></p></div>');
                    },
                    complete: function() {
                        $button.prop('disabled', false).text('<?php _e( 'Lancer tous les tests', 'smartseo-ai' ); ?>');
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * AJAX : Lance tous les tests
     */
    public function ajax_run_tests() {
        check_ajax_referer( 'smartseo_ai_phase1_tests', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'smartseo-ai' ) );
        }

        $this->test_results = array();

        // Lancer tous les tests
        $this->test_cache_manager();
        $this->test_performance_manager();
        $this->test_queue_manager();
        $this->test_ui_manager();
        $this->test_database_tables();

        // Générer le HTML des résultats
        $html = $this->generate_results_html();

        wp_send_json_success( array( 'html' => $html ) );
    }

    /**
     * Teste le gestionnaire de cache
     */
    private function test_cache_manager() {
        $this->add_test_result( 'Cache Manager', 'Tests du gestionnaire de cache' );

        try {
            $cache_manager = SmartSEO_AI_Cache_Manager::get_instance();
            
            // Test 1: Génération de clé de cache
            $cache_key = $cache_manager->generate_cache_key( 'test content', 'test title', 'openai' );
            $this->add_test_result( 'Cache Key Generation', 
                ! empty( $cache_key ) && strlen( $cache_key ) === 64 ? 'PASS' : 'FAIL',
                "Clé générée: " . substr( $cache_key, 0, 16 ) . '...'
            );

            // Test 2: Stockage et récupération
            $test_data = array( 'test' => 'data', 'timestamp' => time() );
            $set_result = $cache_manager->set( $cache_key, $test_data, 'openai', 3600 );
            $get_result = $cache_manager->get( $cache_key );
            
            $this->add_test_result( 'Cache Set/Get', 
                $set_result && $get_result && $get_result['test'] === 'data' ? 'PASS' : 'FAIL',
                'Données stockées et récupérées avec succès'
            );

            // Test 3: Statistiques
            $stats = $cache_manager->get_stats();
            $this->add_test_result( 'Cache Stats', 
                is_array( $stats ) && isset( $stats['general'] ) ? 'PASS' : 'FAIL',
                'Statistiques récupérées'
            );

            // Test 4: Nettoyage
            $cache_manager->delete( $cache_key );
            $deleted_result = $cache_manager->get( $cache_key );
            $this->add_test_result( 'Cache Delete', 
                false === $deleted_result ? 'PASS' : 'FAIL',
                'Entrée supprimée avec succès'
            );

        } catch ( Exception $e ) {
            $this->add_test_result( 'Cache Manager Exception', 'FAIL', $e->getMessage() );
        }
    }

    /**
     * Teste le gestionnaire de performance
     */
    private function test_performance_manager() {
        $this->add_test_result( 'Performance Manager', 'Tests du gestionnaire de performance' );

        try {
            $performance_manager = SmartSEO_AI_Performance_Manager::get_instance();
            
            // Test 1: Vérification de santé des API
            $health = $performance_manager->check_api_health();
            $this->add_test_result( 'API Health Check', 
                is_array( $health ) && ( isset( $health['openai'] ) || isset( $health['gemini'] ) ) ? 'PASS' : 'FAIL',
                'Vérification de santé effectuée'
            );

            // Test 2: Statistiques de performance
            $stats = $performance_manager->get_performance_stats();
            $this->add_test_result( 'Performance Stats', 
                is_array( $stats ) && isset( $stats['general'] ) ? 'PASS' : 'FAIL',
                'Statistiques de performance récupérées'
            );

        } catch ( Exception $e ) {
            $this->add_test_result( 'Performance Manager Exception', 'FAIL', $e->getMessage() );
        }
    }

    /**
     * Teste le gestionnaire de queue
     */
    private function test_queue_manager() {
        $this->add_test_result( 'Queue Manager', 'Tests du gestionnaire de queue' );

        try {
            $queue_manager = SmartSEO_AI_Queue_Manager::get_instance();
            
            // Test 1: Ajout à la queue
            $test_post_ids = array( 1, 2, 3 ); // IDs de test
            $added = $queue_manager->add_to_queue( $test_post_ids, 'test_optimization', 5 );
            $this->add_test_result( 'Queue Add Items', 
                $added >= 0 ? 'PASS' : 'FAIL',
                "Éléments ajoutés: $added"
            );

            // Test 2: Statut de la queue
            $status = $queue_manager->get_queue_status();
            $this->add_test_result( 'Queue Status', 
                is_array( $status ) && isset( $status['total'] ) ? 'PASS' : 'FAIL',
                'Statut de la queue récupéré'
            );

            // Test 3: Nettoyage de la queue de test
            $cleared = $queue_manager->clear_queue( 'pending' );
            $this->add_test_result( 'Queue Clear', 
                $cleared >= 0 ? 'PASS' : 'FAIL',
                "Éléments supprimés: $cleared"
            );

        } catch ( Exception $e ) {
            $this->add_test_result( 'Queue Manager Exception', 'FAIL', $e->getMessage() );
        }
    }

    /**
     * Teste le gestionnaire d'interface utilisateur
     */
    private function test_ui_manager() {
        $this->add_test_result( 'UI Manager', 'Tests du gestionnaire d\'interface utilisateur' );

        try {
            $ui_manager = SmartSEO_AI_UI_Manager::get_instance();
            
            // Test 1: Vérification de l'instance
            $this->add_test_result( 'UI Manager Instance', 
                $ui_manager instanceof SmartSEO_AI_UI_Manager ? 'PASS' : 'FAIL',
                'Instance créée avec succès'
            );

            // Test 2: Génération du tableau de bord
            $dashboard_html = $ui_manager->render_enhanced_dashboard();
            $this->add_test_result( 'Dashboard Rendering', 
                ! empty( $dashboard_html ) && strpos( $dashboard_html, 'smartseo-ai-enhanced-dashboard' ) !== false ? 'PASS' : 'FAIL',
                'Tableau de bord généré'
            );

        } catch ( Exception $e ) {
            $this->add_test_result( 'UI Manager Exception', 'FAIL', $e->getMessage() );
        }
    }

    /**
     * Teste les tables de base de données
     */
    private function test_database_tables() {
        $this->add_test_result( 'Database Tables', 'Tests des tables de base de données' );

        global $wpdb;

        $tables = array(
            'cache' => $wpdb->prefix . 'smartseo_ai_cache',
            'stats' => $wpdb->prefix . 'smartseo_ai_stats',
            'queue' => $wpdb->prefix . 'smartseo_ai_queue',
        );

        foreach ( $tables as $name => $table ) {
            $exists = $wpdb->get_var( "SHOW TABLES LIKE '$table'" ) === $table;
            $this->add_test_result( "Table $name", 
                $exists ? 'PASS' : 'FAIL',
                $exists ? "Table $table existe" : "Table $table manquante"
            );
        }
    }

    /**
     * Ajoute un résultat de test
     *
     * @param string $test_name Nom du test.
     * @param string $status Statut (PASS/FAIL/INFO).
     * @param string $message Message descriptif.
     */
    private function add_test_result( $test_name, $status, $message = '' ) {
        $this->test_results[] = array(
            'name' => $test_name,
            'status' => $status,
            'message' => $message,
            'timestamp' => current_time( 'mysql' ),
        );
    }

    /**
     * Génère le HTML des résultats de test
     *
     * @return string HTML des résultats.
     */
    private function generate_results_html() {
        $html = '<div class="test-results">';
        $html .= '<h2>' . __( 'Résultats des tests Phase 1', 'smartseo-ai' ) . '</h2>';

        $pass_count = 0;
        $fail_count = 0;
        $info_count = 0;

        foreach ( $this->test_results as $result ) {
            $status_class = strtolower( $result['status'] );
            $status_color = '';
            
            switch ( $result['status'] ) {
                case 'PASS':
                    $status_color = 'color: #46b450; font-weight: bold;';
                    $pass_count++;
                    break;
                case 'FAIL':
                    $status_color = 'color: #dc3232; font-weight: bold;';
                    $fail_count++;
                    break;
                default:
                    $status_color = 'color: #0073aa; font-weight: bold;';
                    $info_count++;
                    break;
            }

            $html .= '<div style="padding: 10px; border-left: 4px solid ' . 
                     ( $result['status'] === 'PASS' ? '#46b450' : ( $result['status'] === 'FAIL' ? '#dc3232' : '#0073aa' ) ) . 
                     '; margin-bottom: 10px; background: #f9f9f9;">';
            $html .= '<strong>' . esc_html( $result['name'] ) . '</strong> ';
            $html .= '<span style="' . $status_color . '">[' . esc_html( $result['status'] ) . ']</span>';
            
            if ( ! empty( $result['message'] ) ) {
                $html .= '<br><small>' . esc_html( $result['message'] ) . '</small>';
            }
            
            $html .= '</div>';
        }

        // Résumé
        $html .= '<div style="margin-top: 20px; padding: 15px; background: #f0f0f1; border-radius: 4px;">';
        $html .= '<h3>' . __( 'Résumé', 'smartseo-ai' ) . '</h3>';
        $html .= '<p>';
        $html .= '<span style="color: #46b450; font-weight: bold;">' . $pass_count . ' tests réussis</span>, ';
        $html .= '<span style="color: #dc3232; font-weight: bold;">' . $fail_count . ' tests échoués</span>, ';
        $html .= '<span style="color: #0073aa; font-weight: bold;">' . $info_count . ' informations</span>';
        $html .= '</p>';
        
        if ( $fail_count === 0 ) {
            $html .= '<div class="notice notice-success inline"><p><strong>' . 
                     __( 'Tous les tests sont passés avec succès ! La Phase 1 est correctement implémentée.', 'smartseo-ai' ) . 
                     '</strong></p></div>';
        } else {
            $html .= '<div class="notice notice-warning inline"><p><strong>' . 
                     __( 'Certains tests ont échoué. Vérifiez la configuration et les logs.', 'smartseo-ai' ) . 
                     '</strong></p></div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }
}
