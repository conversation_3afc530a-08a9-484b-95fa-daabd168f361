/**
 * Module de gestion du panneau d'analyse pour l'éditeur classique
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire du panneau d'analyse pour l'éditeur classique
     */
    const ClassicAnalysisPanel = {
        /**
         * Initialise le gestionnaire du panneau d'analyse
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Rend le panneau d'analyse
         * @return {string} HTML du panneau
         */
        render: function() {
            // Créer la structure du panneau
            const $panel = $('<div class="smartseo-ai-panel"></div>');
            
            // Ajouter l'en-tête
            $panel.append('<div class="smartseo-ai-panel-header">' +
                '<h3>Analyse SEO en temps réel</h3>' +
                '<p>Analysez votre contenu en temps réel pour optimiser le SEO.</p>' +
                '</div>');
            
            // Ajouter le contenu
            const $content = $('<div class="smartseo-ai-panel-content"></div>');
            
            // Ajouter les boutons d'analyse
            $content.append('<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-analyze-now">Analyser maintenant</button>' +
                '<button type="button" class="button button-primary smartseo-ai-optimize-all">Optimiser automatiquement</button>' +
                '</div>');
            
            $content.append('<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button smartseo-ai-check-plagiarism">Vérifier la non-duplication</button>' +
                '</div>');
            
            // Ajouter le conteneur de résultats
            $content.append('<div class="smartseo-ai-results-container" id="smartseo-ai-analysis-results">' +
                '<div class="smartseo-ai-loading" style="display: none;">' +
                '<span class="spinner is-active"></span>' +
                '<p>Analyse en cours...</p>' +
                '</div>' +
                '<div class="smartseo-ai-results" style="display: none;">' +
                '<h4>Résultats de l\'analyse</h4>' +
                '<div class="smartseo-ai-results-content"></div>' +
                '</div>' +
                '</div>');
            
            // Ajouter le contenu au panneau
            $panel.append($content);
            
            return $panel;
        },

        /**
         * Affiche les résultats d'analyse en temps réel
         * @param {Object} results Résultats d'analyse
         */
        showResults: function(results) {
            const $container = $('#smartseo-ai-analysis-results');
            const $loading = $container.find('.smartseo-ai-loading');
            const $results = $container.find('.smartseo-ai-results');
            const $content = $results.find('.smartseo-ai-results-content');
            
            // Masquer le chargement
            $loading.hide();
            
            // Vider le contenu précédent
            $content.empty();
            
            // Afficher les résultats
            $results.show();
            
            // Vérifier si les résultats sont valides
            if (!results) {
                $content.append('<p>Aucun résultat disponible.</p>');
                return;
            }
            
            // Afficher le score SEO global
            const score = results.score;
            let scoreClass = 'smartseo-ai-score-poor';
            let scoreLabel = 'Faible';
            
            if (score >= 80) {
                scoreClass = 'smartseo-ai-score-good';
                scoreLabel = 'Bon';
            } else if (score >= 50) {
                scoreClass = 'smartseo-ai-score-average';
                scoreLabel = 'Moyen';
            }
            
            $content.append('<div class="smartseo-ai-score-container">' +
                '<div class="smartseo-ai-score-label">Score SEO</div>' +
                '<div class="smartseo-ai-score-value ' + scoreClass + '">' + score + '</div>' +
                '<div class="smartseo-ai-score-text">' + scoreLabel + '</div>' +
                '</div>');
            
            // Afficher la checklist d'optimisation
            if (results.optimization_checklist && results.optimization_checklist.length > 0) {
                $content.append('<h5>Checklist d\'optimisation</h5>');
                
                const $checklist = $('<div class="smartseo-ai-checklist"></div>');
                
                results.optimization_checklist.forEach(item => {
                    let statusClass = 'smartseo-ai-status-poor';
                    let statusIcon = '❌';
                    
                    if (item.status === 'good') {
                        statusClass = 'smartseo-ai-status-good';
                        statusIcon = '✅';
                    } else if (item.status === 'average') {
                        statusClass = 'smartseo-ai-status-average';
                        statusIcon = '⚠️';
                    }
                    
                    const $item = $('<div class="smartseo-ai-checklist-item ' + statusClass + '"></div>');
                    $item.append('<div class="smartseo-ai-checklist-icon">' + statusIcon + '</div>');
                    $item.append('<div class="smartseo-ai-checklist-label">' + item.label + '</div>');
                    
                    if (item.status !== 'good') {
                        $item.append('<div class="smartseo-ai-checklist-recommendation">' + item.recommendation + '</div>');
                    }
                    
                    $checklist.append($item);
                });
                
                $content.append($checklist);
            }
            
            // Afficher les détails de l'analyse
            $content.append('<h5>Détails de l\'analyse</h5>');
            
            // Analyse du contenu
            if (results.content_analysis) {
                const contentAnalysis = results.content_analysis;
                
                const $contentSection = $('<div class="smartseo-ai-analysis-section"></div>');
                $contentSection.append('<h6>Analyse du contenu</h6>');
                
                const $contentDetails = $('<div class="smartseo-ai-analysis-details"></div>');
                
                // Longueur du contenu
                let contentLengthClass = 'smartseo-ai-status-poor';
                if (contentAnalysis.content_length.status === 'good') {
                    contentLengthClass = 'smartseo-ai-status-good';
                } else if (contentAnalysis.content_length.status === 'average') {
                    contentLengthClass = 'smartseo-ai-status-average';
                }
                
                $contentDetails.append('<div class="smartseo-ai-analysis-item ' + contentLengthClass + '">' +
                    '<div class="smartseo-ai-analysis-label">Longueur du contenu</div>' +
                    '<div class="smartseo-ai-analysis-value">' + contentAnalysis.content_length.word_count + ' mots</div>' +
                    '</div>');
                
                // Lisibilité
                let readabilityClass = 'smartseo-ai-status-poor';
                if (contentAnalysis.readability.status === 'good') {
                    readabilityClass = 'smartseo-ai-status-good';
                } else if (contentAnalysis.readability.status === 'average') {
                    readabilityClass = 'smartseo-ai-status-average';
                }
                
                $contentDetails.append('<div class="smartseo-ai-analysis-item ' + readabilityClass + '">' +
                    '<div class="smartseo-ai-analysis-label">Lisibilité</div>' +
                    '<div class="smartseo-ai-analysis-value">' + contentAnalysis.readability.flesch_score + ' (' + contentAnalysis.readability.level + ')</div>' +
                    '</div>');
                
                $contentSection.append($contentDetails);
                $content.append($contentSection);
            }
            
            // Analyse des mots-clés
            if (results.keyword_analysis) {
                const keywordAnalysis = results.keyword_analysis;
                
                const $keywordSection = $('<div class="smartseo-ai-analysis-section"></div>');
                $keywordSection.append('<h6>Analyse des mots-clés</h6>');
                
                const $keywordDetails = $('<div class="smartseo-ai-analysis-details"></div>');
                
                // Densité du mot-clé
                let densityClass = 'smartseo-ai-status-poor';
                if (keywordAnalysis.density.status === 'good') {
                    densityClass = 'smartseo-ai-status-good';
                } else if (keywordAnalysis.density.status === 'average') {
                    densityClass = 'smartseo-ai-status-average';
                }
                
                $keywordDetails.append('<div class="smartseo-ai-analysis-item ' + densityClass + '">' +
                    '<div class="smartseo-ai-analysis-label">Densité du mot-clé</div>' +
                    '<div class="smartseo-ai-analysis-value">' + keywordAnalysis.density.density + '%</div>' +
                    '</div>');
                
                // Placement du mot-clé
                let placementClass = 'smartseo-ai-status-poor';
                if (keywordAnalysis.placement.status === 'good') {
                    placementClass = 'smartseo-ai-status-good';
                } else if (keywordAnalysis.placement.status === 'average') {
                    placementClass = 'smartseo-ai-status-average';
                }
                
                $keywordDetails.append('<div class="smartseo-ai-analysis-item ' + placementClass + '">' +
                    '<div class="smartseo-ai-analysis-label">Placement du mot-clé</div>' +
                    '<div class="smartseo-ai-analysis-value">' +
                        (keywordAnalysis.placement.in_title ? '✅ Titre<br>' : '❌ Titre<br>') +
                        (keywordAnalysis.placement.in_meta_description ? '✅ Meta description<br>' : '❌ Meta description<br>') +
                        (keywordAnalysis.placement.in_headings ? '✅ Titres<br>' : '❌ Titres<br>') +
                        (keywordAnalysis.placement.in_first_paragraph ? '✅ Premier paragraphe' : '❌ Premier paragraphe') +
                    '</div>' +
                    '</div>');
                
                $keywordSection.append($keywordDetails);
                $content.append($keywordSection);
            }
            
            // Analyse de la structure
            if (results.structure_analysis) {
                const structureAnalysis = results.structure_analysis;
                
                const $structureSection = $('<div class="smartseo-ai-analysis-section"></div>');
                $structureSection.append('<h6>Analyse de la structure</h6>');
                
                const $structureDetails = $('<div class="smartseo-ai-analysis-details"></div>');
                
                // Titres
                let headingsClass = 'smartseo-ai-status-poor';
                if (structureAnalysis.headings.status === 'good') {
                    headingsClass = 'smartseo-ai-status-good';
                } else if (structureAnalysis.headings.status === 'average') {
                    headingsClass = 'smartseo-ai-status-average';
                }
                
                $structureDetails.append('<div class="smartseo-ai-analysis-item ' + headingsClass + '">' +
                    '<div class="smartseo-ai-analysis-label">Titres</div>' +
                    '<div class="smartseo-ai-analysis-value">' + structureAnalysis.headings.headings.length + ' titres</div>' +
                    '</div>');
                
                // Listes
                let listsClass = 'smartseo-ai-status-poor';
                if (structureAnalysis.lists.status === 'good') {
                    listsClass = 'smartseo-ai-status-good';
                } else if (structureAnalysis.lists.status === 'average') {
                    listsClass = 'smartseo-ai-status-average';
                }
                
                $structureDetails.append('<div class="smartseo-ai-analysis-item ' + listsClass + '">' +
                    '<div class="smartseo-ai-analysis-label">Listes</div>' +
                    '<div class="smartseo-ai-analysis-value">' + structureAnalysis.lists.total_lists + ' listes</div>' +
                    '</div>');
                
                $structureSection.append($structureDetails);
                $content.append($structureSection);
            }
            
            // Analyse des liens
            if (results.link_analysis) {
                const linkAnalysis = results.link_analysis;
                
                const $linkSection = $('<div class="smartseo-ai-analysis-section"></div>');
                $linkSection.append('<h6>Analyse des liens</h6>');
                
                const $linkDetails = $('<div class="smartseo-ai-analysis-details"></div>');
                
                // Liens internes
                let internalLinksClass = 'smartseo-ai-status-poor';
                if (linkAnalysis.internal_links_analysis.status === 'good') {
                    internalLinksClass = 'smartseo-ai-status-good';
                } else if (linkAnalysis.internal_links_analysis.status === 'average') {
                    internalLinksClass = 'smartseo-ai-status-average';
                }
                
                $linkDetails.append('<div class="smartseo-ai-analysis-item ' + internalLinksClass + '">' +
                    '<div class="smartseo-ai-analysis-label">Liens internes</div>' +
                    '<div class="smartseo-ai-analysis-value">' + linkAnalysis.internal_links + ' liens</div>' +
                    '</div>');
                
                // Liens externes
                let externalLinksClass = 'smartseo-ai-status-poor';
                if (linkAnalysis.external_links_analysis.status === 'good') {
                    externalLinksClass = 'smartseo-ai-status-good';
                } else if (linkAnalysis.external_links_analysis.status === 'average') {
                    externalLinksClass = 'smartseo-ai-status-average';
                }
                
                $linkDetails.append('<div class="smartseo-ai-analysis-item ' + externalLinksClass + '">' +
                    '<div class="smartseo-ai-analysis-label">Liens externes</div>' +
                    '<div class="smartseo-ai-analysis-value">' + linkAnalysis.external_links + ' liens</div>' +
                    '</div>');
                
                $linkSection.append($linkDetails);
                $content.append($linkSection);
            }
            
            // Analyse des images
            if (results.image_analysis) {
                const imageAnalysis = results.image_analysis;
                
                const $imageSection = $('<div class="smartseo-ai-analysis-section"></div>');
                $imageSection.append('<h6>Analyse des images</h6>');
                
                const $imageDetails = $('<div class="smartseo-ai-analysis-details"></div>');
                
                // Attributs alt
                let altClass = 'smartseo-ai-status-poor';
                if (imageAnalysis.alt_analysis.status === 'good') {
                    altClass = 'smartseo-ai-status-good';
                } else if (imageAnalysis.alt_analysis.status === 'average') {
                    altClass = 'smartseo-ai-status-average';
                }
                
                $imageDetails.append('<div class="smartseo-ai-analysis-item ' + altClass + '">' +
                    '<div class="smartseo-ai-analysis-label">Attributs alt</div>' +
                    '<div class="smartseo-ai-analysis-value">' +
                        'Images : ' + imageAnalysis.total_images + '<br>' +
                        'Sans alt : ' + imageAnalysis.missing_alt +
                    '</div>' +
                    '</div>');
                
                $imageSection.append($imageDetails);
                $content.append($imageSection);
            }
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.ClassicAnalysisPanel = ClassicAnalysisPanel;

})(jQuery);
