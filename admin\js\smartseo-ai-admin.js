/**
 * Script JavaScript pour l'administration du plugin SmartSEO AI
 */
(function($) {
    'use strict';

    // Initialisation
    $(document).ready(function() {
        // Initialiser les fonctionnalités
        initCharacterCounters();
        initOptimizeButton();
        initApplySlugButton();
        initMediaSelector();
    });

    /**
     * Initialise les compteurs de caractères
     */
    function initCharacterCounters() {
        $('.smartseo-ai-char-count').each(function() {
            const counter = $(this);
            const input = counter.closest('label').next('input, textarea');
            const maxLength = counter.data('max');
            const currentLength = input.val().length;
            
            // Mettre à jour le compteur initial
            counter.find('.current').text(currentLength);
            
            // Ajouter la classe si la limite est dépassée
            if (currentLength > maxLength) {
                counter.addClass('over-limit');
            }
            
            // Écouter les changements
            input.on('input', function() {
                const length = $(this).val().length;
                counter.find('.current').text(length);
                
                if (length > maxLength) {
                    counter.addClass('over-limit');
                } else {
                    counter.removeClass('over-limit');
                }
            });
        });
    }

    /**
     * Initialise le bouton d'optimisation
     */
    function initOptimizeButton() {
        const optimizeButton = $('#smartseo-ai-optimize-button');
        
        if (optimizeButton.length === 0) {
            return;
        }
        
        optimizeButton.on('click', function() {
            const button = $(this);
            const statusElement = $('#smartseo-ai-optimize-status');
            const postId = $('#post_ID').val();
            
            // Désactiver le bouton et afficher le statut
            button.prop('disabled', true);
            button.html('<span class="smartseo-ai-loading"></span>' + smartseoAiData.i18n.optimizing);
            statusElement.text(smartseoAiData.i18n.optimizing);
            
            // Appeler l'API REST
            $.ajax({
                url: smartseoAiData.restUrl + '/optimize',
                method: 'POST',
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-WP-Nonce', smartseoAiData.nonce);
                },
                data: {
                    post_id: postId
                },
                success: function(response) {
                    // Mettre à jour les champs avec les données générées
                    if (response.meta_description) {
                        $('#smartseo_ai_meta_description').val(response.meta_description);
                    }
                    
                    if (response.keywords) {
                        $('#smartseo_ai_keywords').val(response.keywords);
                    }
                    
                    if (response.seo_title) {
                        $('#smartseo_ai_seo_title').val(response.seo_title);
                    }
                    
                    if (response.seo_slug) {
                        $('#smartseo_ai_seo_slug').val(response.seo_slug);
                    }
                    
                    if (response.og_title) {
                        $('#smartseo_ai_og_title').val(response.og_title);
                    }
                    
                    if (response.og_description) {
                        $('#smartseo_ai_og_description').val(response.og_description);
                    }
                    
                    if (response.seo_advice) {
                        // Créer ou mettre à jour la section de conseils
                        let adviceSection = $('.smartseo-ai-advice');
                        
                        if (adviceSection.length === 0) {
                            const adviceField = $('<div class="smartseo-ai-field"></div>');
                            const adviceLabel = $('<label></label>').text(smartseoAiData.i18n.seoAdvice);
                            adviceSection = $('<div class="smartseo-ai-advice"></div>');
                            
                            adviceField.append(adviceLabel, adviceSection);
                            $('.smartseo-ai-fields-container').append(adviceField);
                        }
                        
                        adviceSection.html(response.seo_advice);
                    }
                    
                    if (response.seo_score) {
                        // Créer ou mettre à jour le score SEO
                        let scoreContainer = $('.smartseo-ai-score-container');
                        
                        if (scoreContainer.length === 0) {
                            scoreContainer = $('<div class="smartseo-ai-score-container"></div>');
                            $('.smartseo-ai-optimize-button-container').after(scoreContainer);
                        }
                        
                        const scoreClass = getScoreClass(response.seo_score);
                        
                        scoreContainer.html(
                            '<div class="smartseo-ai-score smartseo-ai-score-' + scoreClass + '">' +
                            response.seo_score + '/100' +
                            '</div>' +
                            '<div class="smartseo-ai-score-label">' +
                            'Score SEO' +
                            '</div>'
                        );
                    }
                    
                    // Mettre à jour les compteurs de caractères
                    initCharacterCounters();
                    
                    // Réactiver le bouton et mettre à jour le statut
                    button.prop('disabled', false);
                    button.html('<span class="dashicons dashicons-superhero"></span> ' + 'Optimiser avec l\'IA');
                    statusElement.text(smartseoAiData.i18n.success);
                    
                    // Afficher un message de succès temporaire
                    setTimeout(function() {
                        statusElement.text('');
                    }, 3000);
                },
                error: function(xhr) {
                    // Réactiver le bouton et afficher l'erreur
                    button.prop('disabled', false);
                    button.html('<span class="dashicons dashicons-superhero"></span> ' + 'Optimiser avec l\'IA');
                    
                    let errorMessage = smartseoAiData.i18n.error;
                    
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage += ' ' + xhr.responseJSON.message;
                    }
                    
                    statusElement.text(errorMessage);
                }
            });
        });
    }

    /**
     * Initialise le bouton d'application du slug
     */
    function initApplySlugButton() {
        const applySlugButton = $('#smartseo-ai-apply-slug');
        
        if (applySlugButton.length === 0) {
            return;
        }
        
        applySlugButton.on('click', function() {
            const slug = $('#smartseo_ai_seo_slug').val();
            
            if (slug) {
                // Mettre à jour le slug dans l'éditeur WordPress
                $('#post_name').val(slug);
                $('#editable-post-name-full').text(slug);
                $('#editable-post-name').text(slug);
                $('#edit-slug-box').removeClass('hidden');
            }
        });
    }

    /**
     * Initialise le sélecteur de média pour l'image OG
     */
    function initMediaSelector() {
        const selectImageButton = $('#smartseo-ai-select-image');
        
        if (selectImageButton.length === 0 || typeof wp.media === 'undefined') {
            return;
        }
        
        let mediaUploader;
        
        selectImageButton.on('click', function(e) {
            e.preventDefault();
            
            // Si l'uploader existe déjà, l'ouvrir
            if (mediaUploader) {
                mediaUploader.open();
                return;
            }
            
            // Créer l'uploader
            mediaUploader = wp.media({
                title: 'Sélectionner une image Open Graph',
                button: {
                    text: 'Utiliser cette image'
                },
                multiple: false
            });
            
            // Quand une image est sélectionnée
            mediaUploader.on('select', function() {
                const attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#smartseo_ai_og_image').val(attachment.url);
            });
            
            // Ouvrir l'uploader
            mediaUploader.open();
        });
    }

    /**
     * Retourne la classe CSS en fonction du score SEO
     *
     * @param {number} score Score SEO
     * @return {string} Classe CSS
     */
    function getScoreClass(score) {
        if (score >= 80) {
            return 'good';
        } else if (score >= 50) {
            return 'average';
        } else {
            return 'poor';
        }
    }

})(jQuery);
