<?php
/**
 * Plugin Name: SmartSEO AI
 * Plugin URI: https://example.com/smartseo-ai
 * Description: Un plugin WordPress SEO basé sur l'intelligence artificielle qui optimise automatiquement vos articles.
 * Version: 1.0.0
 * Author: SmartSEO Team
 * Author URI: https://example.com
 * Text Domain: smartseo-ai
 * Domain Path: /languages
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Définition des constantes
define( 'SMARTSEO_AI_VERSION', '1.0.0' );
define( 'SMARTSEO_AI_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'SMARTSEO_AI_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'SMARTSEO_AI_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

/**
 * Fonction d'activation du plugin
 */
function smartseo_ai_activate() {
    // Créer les tables personnalisées si nécessaire
    // Ajouter les options par défaut
    add_option( 'smartseo_ai_provider', 'openai' );
    add_option( 'smartseo_ai_openai_api_key', '' );
    add_option( 'smartseo_ai_openai_model', 'gpt-4' );
    add_option( 'smartseo_ai_gemini_api_key', '' );
    add_option( 'smartseo_ai_gemini_model', 'gemini-1.5-pro' );
    add_option( 'smartseo_ai_settings', array(
        'enable_meta_description' => 'yes',
        'enable_keywords' => 'yes',
        'enable_seo_title' => 'yes',
        'enable_slug_optimization' => 'yes',
        'enable_open_graph' => 'yes',
        'enable_seo_advice' => 'yes',
    ) );

    // Vider le cache de réécriture
    flush_rewrite_rules();
}
register_activation_hook( __FILE__, 'smartseo_ai_activate' );

/**
 * Fonction de désactivation du plugin
 */
function smartseo_ai_deactivate() {
    // Nettoyer si nécessaire
    flush_rewrite_rules();
}
register_deactivation_hook( __FILE__, 'smartseo_ai_deactivate' );

/**
 * Chargement des fichiers requis
 */
require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai.php';

/**
 * Démarrer le plugin
 */
function smartseo_ai_init() {
    $smartseo_ai = new SmartSEO_AI();
    $smartseo_ai->run();
}
add_action( 'plugins_loaded', 'smartseo_ai_init' );
