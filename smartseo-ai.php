<?php
/**
 * Plugin Name: SmartSEO AI
 * Plugin URI: https://example.com/smartseo-ai
 * Description: Un plugin WordPress SEO basé sur l'intelligence artificielle qui optimise automatiquement vos articles.
 * Version: 1.0.0
 * Author: SmartSEO Team
 * Author URI: https://example.com
 * Text Domain: smartseo-ai
 * Domain Path: /languages
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Définition des constantes
define( 'SMARTSEO_AI_VERSION', '1.0.0' );
define( 'SMARTSEO_AI_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'SMARTSEO_AI_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'SMARTSEO_AI_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

/**
 * Fonction d'activation du plugin
 */
function smartseo_ai_activate() {
    // Créer les tables personnalisées si nécessaire
    smartseo_ai_create_tables();

    // Ajouter les options par défaut
    add_option( 'smartseo_ai_provider', 'openai' );
    add_option( 'smartseo_ai_openai_api_key', '' );
    add_option( 'smartseo_ai_openai_model', 'gpt-4' );
    add_option( 'smartseo_ai_gemini_api_key', '' );
    add_option( 'smartseo_ai_gemini_model', 'gemini-1.5-pro' );
    add_option( 'smartseo_ai_settings', array(
        'enable_meta_description' => 'yes',
        'enable_keywords' => 'yes',
        'enable_seo_title' => 'yes',
        'enable_slug_optimization' => 'yes',
        'enable_open_graph' => 'yes',
        'enable_seo_advice' => 'yes',
    ) );

    // Options de cache et performance
    add_option( 'smartseo_ai_cache_settings', array(
        'enable_cache' => 'yes',
        'cache_ttl' => 86400, // 24 heures
        'max_cache_size' => 1000, // Nombre max d'entrées
        'enable_fallback' => 'yes',
        'retry_attempts' => 3,
        'retry_delay' => 2, // secondes
    ) );

    // Options d'optimisation en masse
    add_option( 'smartseo_ai_bulk_settings', array(
        'batch_size' => 5,
        'delay_between_batches' => 3, // secondes
        'enable_resume' => 'yes',
        'max_concurrent_requests' => 3,
        'timeout_per_request' => 60, // secondes
    ) );

    // Vider le cache de réécriture
    flush_rewrite_rules();
}
register_activation_hook( __FILE__, 'smartseo_ai_activate' );

/**
 * Fonction de désactivation du plugin
 */
function smartseo_ai_deactivate() {
    // Nettoyer si nécessaire
    flush_rewrite_rules();
}
register_deactivation_hook( __FILE__, 'smartseo_ai_deactivate' );

/**
 * Chargement des fichiers requis
 */
require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/class-smartseo-ai.php';

/**
 * Démarrer le plugin
 */
function smartseo_ai_init() {
    $smartseo_ai = new SmartSEO_AI();
    $smartseo_ai->run();
}
add_action( 'plugins_loaded', 'smartseo_ai_init' );

/**
 * Crée les tables personnalisées pour le cache et les statistiques
 */
function smartseo_ai_create_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // Table pour le cache des réponses IA
    $cache_table = $wpdb->prefix . 'smartseo_ai_cache';
    $cache_sql = "CREATE TABLE $cache_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        cache_key varchar(64) NOT NULL,
        cache_data longtext NOT NULL,
        provider varchar(20) NOT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        expires_at datetime NOT NULL,
        hit_count int(11) DEFAULT 0,
        PRIMARY KEY (id),
        UNIQUE KEY cache_key (cache_key),
        KEY provider (provider),
        KEY expires_at (expires_at)
    ) $charset_collate;";

    // Table pour les statistiques d'optimisation
    $stats_table = $wpdb->prefix . 'smartseo_ai_stats';
    $stats_sql = "CREATE TABLE $stats_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        post_id bigint(20) NOT NULL,
        optimization_type varchar(50) NOT NULL,
        provider varchar(20) NOT NULL,
        tokens_used int(11) DEFAULT 0,
        processing_time float DEFAULT 0,
        success tinyint(1) DEFAULT 1,
        error_message text,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY post_id (post_id),
        KEY optimization_type (optimization_type),
        KEY provider (provider),
        KEY created_at (created_at)
    ) $charset_collate;";

    // Table pour la queue d'optimisation en masse
    $queue_table = $wpdb->prefix . 'smartseo_ai_queue';
    $queue_sql = "CREATE TABLE $queue_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        post_id bigint(20) NOT NULL,
        optimization_type varchar(50) NOT NULL,
        priority int(11) DEFAULT 10,
        status varchar(20) DEFAULT 'pending',
        attempts int(11) DEFAULT 0,
        max_attempts int(11) DEFAULT 3,
        scheduled_at datetime DEFAULT CURRENT_TIMESTAMP,
        started_at datetime NULL,
        completed_at datetime NULL,
        error_message text,
        metadata longtext,
        PRIMARY KEY (id),
        KEY post_id (post_id),
        KEY status (status),
        KEY priority (priority),
        KEY scheduled_at (scheduled_at)
    ) $charset_collate;";

    require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
    dbDelta( $cache_sql );
    dbDelta( $stats_sql );
    dbDelta( $queue_sql );

    // Créer les index pour optimiser les performances
    $wpdb->query( "CREATE INDEX IF NOT EXISTS idx_cache_provider_expires ON $cache_table (provider, expires_at)" );
    $wpdb->query( "CREATE INDEX IF NOT EXISTS idx_stats_post_type ON $stats_table (post_id, optimization_type)" );
    $wpdb->query( "CREATE INDEX IF NOT EXISTS idx_queue_status_priority ON $queue_table (status, priority)" );
}
