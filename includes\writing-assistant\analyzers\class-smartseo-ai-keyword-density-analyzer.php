<?php
/**
 * Classe pour l'analyse de la densité des mots-clés
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'analyse de la densité des mots-clés
 */
class SmartSEO_AI_Keyword_Density_Analyzer {

    /**
     * Analyse la densité des mots-clés
     *
     * @param string $content          Contenu à analyser.
     * @param string $title            Titre de l'article.
     * @param string $meta_description Meta description de l'article.
     * @param string $keyword          Mot-clé principal.
     * @return array Résultats de l'analyse.
     */
    public function analyze( $content, $title, $meta_description, $keyword ) {
        // Si aucun mot-clé n'est fourni, on ne peut pas analyser la densité
        if ( empty( $keyword ) ) {
            return array(
                'score' => 0,
                'density' => array(
                    'keyword' => '',
                    'count' => 0,
                    'total_words' => 0,
                    'density' => 0,
                    'status' => 'poor',
                ),
                'placement' => array(
                    'in_title' => false,
                    'in_first_paragraph' => false,
                    'in_headings' => false,
                    'in_meta_description' => false,
                    'status' => 'poor',
                ),
                'checks' => array(
                    array(
                        'label' => __( 'Mot-clé principal', 'smartseo-ai' ),
                        'status' => 'poor',
                        'recommendation' => __( 'Aucun mot-clé principal défini. Définissez un mot-clé principal pour l\'analyse.', 'smartseo-ai' ),
                    ),
                ),
            );
        }

        // Nettoyer le contenu
        $clean_content = strip_tags( $content );
        
        // Analyser la densité du mot-clé
        $density = $this->analyze_keyword_density( $clean_content, $keyword );
        
        // Analyser le placement du mot-clé
        $placement = $this->analyze_keyword_placement( $content, $title, $meta_description, $keyword );
        
        // Analyser les variations du mot-clé
        $variations = $this->analyze_keyword_variations( $clean_content, $keyword );
        
        // Créer la liste des vérifications
        $checks = array(
            array(
                'label' => __( 'Densité du mot-clé', 'smartseo-ai' ),
                'status' => $density['status'],
                'recommendation' => $density['recommendation'],
            ),
            array(
                'label' => __( 'Placement du mot-clé', 'smartseo-ai' ),
                'status' => $placement['status'],
                'recommendation' => $placement['recommendation'],
            ),
            array(
                'label' => __( 'Variations du mot-clé', 'smartseo-ai' ),
                'status' => $variations['status'],
                'recommendation' => $variations['recommendation'],
            ),
        );
        
        // Calculer le score
        $score = $this->calculate_score( $density, $placement, $variations );
        
        // Retourner les résultats
        return array(
            'score' => $score,
            'keyword' => $keyword,
            'density' => $density,
            'placement' => $placement,
            'variations' => $variations,
            'checks' => $checks,
        );
    }

    /**
     * Analyse la densité du mot-clé dans le contenu
     *
     * @param string $content Contenu à analyser.
     * @param string $keyword Mot-clé principal.
     * @return array Résultats de l'analyse.
     */
    private function analyze_keyword_density( $content, $keyword ) {
        // Nettoyer le contenu et le mot-clé
        $clean_content = strtolower( $content );
        $clean_keyword = strtolower( $keyword );
        
        // Compter le nombre total de mots
        $total_words = str_word_count( $clean_content );
        
        // Compter les occurrences du mot-clé
        $keyword_count = substr_count( $clean_content, $clean_keyword );
        
        // Calculer la densité
        $density = $total_words > 0 ? ( $keyword_count / $total_words ) * 100 : 0;
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( $density >= 0.5 && $density <= 2.5 ) {
            $status = 'good';
            $recommendation = sprintf(
                __( 'Bonne densité de mot-clé (%.1f%%). La densité optimale est généralement entre 0.5%% et 2.5%%.', 'smartseo-ai' ),
                $density
            );
        } elseif ( $density > 0 && $density < 0.5 ) {
            $status = 'average';
            $recommendation = sprintf(
                __( 'Densité de mot-clé un peu faible (%.1f%%). Essayez d\'augmenter légèrement l\'utilisation du mot-clé.', 'smartseo-ai' ),
                $density
            );
        } elseif ( $density > 2.5 && $density <= 3.5 ) {
            $status = 'average';
            $recommendation = sprintf(
                __( 'Densité de mot-clé un peu élevée (%.1f%%). Réduisez légèrement l\'utilisation du mot-clé pour éviter le sur-optimisation.', 'smartseo-ai' ),
                $density
            );
        } elseif ( $density > 3.5 ) {
            $recommendation = sprintf(
                __( 'Densité de mot-clé trop élevée (%.1f%%). Réduisez l\'utilisation du mot-clé pour éviter le sur-optimisation.', 'smartseo-ai' ),
                $density
            );
        } else {
            $recommendation = sprintf(
                __( 'Densité de mot-clé trop faible (%.1f%%). Augmentez l\'utilisation du mot-clé dans le contenu.', 'smartseo-ai' ),
                $density
            );
        }
        
        return array(
            'keyword' => $keyword,
            'count' => $keyword_count,
            'total_words' => $total_words,
            'density' => round( $density, 2 ),
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Analyse le placement du mot-clé dans le contenu
     *
     * @param string $content          Contenu à analyser.
     * @param string $title            Titre de l'article.
     * @param string $meta_description Meta description de l'article.
     * @param string $keyword          Mot-clé principal.
     * @return array Résultats de l'analyse.
     */
    private function analyze_keyword_placement( $content, $title, $meta_description, $keyword ) {
        // Nettoyer le mot-clé
        $clean_keyword = strtolower( $keyword );
        
        // Vérifier si le mot-clé est dans le titre
        $in_title = strpos( strtolower( $title ), $clean_keyword ) !== false;
        
        // Vérifier si le mot-clé est dans la meta description
        $in_meta_description = ! empty( $meta_description ) && strpos( strtolower( $meta_description ), $clean_keyword ) !== false;
        
        // Vérifier si le mot-clé est dans les titres (h1, h2, h3)
        $in_headings = false;
        if ( preg_match_all( '/<h[1-3][^>]*>(.*?)<\/h[1-3]>/i', $content, $matches ) ) {
            foreach ( $matches[1] as $heading ) {
                if ( strpos( strtolower( $heading ), $clean_keyword ) !== false ) {
                    $in_headings = true;
                    break;
                }
            }
        }
        
        // Vérifier si le mot-clé est dans le premier paragraphe
        $in_first_paragraph = false;
        if ( preg_match( '/<p[^>]*>(.*?)<\/p>/i', $content, $matches ) ) {
            $first_paragraph = $matches[1];
            $in_first_paragraph = strpos( strtolower( $first_paragraph ), $clean_keyword ) !== false;
        }
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        $placement_count = ( $in_title ? 1 : 0 ) + ( $in_meta_description ? 1 : 0 ) + ( $in_headings ? 1 : 0 ) + ( $in_first_paragraph ? 1 : 0 );
        
        if ( $placement_count >= 3 ) {
            $status = 'good';
            $recommendation = __( 'Excellent placement du mot-clé. Le mot-clé est présent dans les emplacements stratégiques.', 'smartseo-ai' );
        } elseif ( $placement_count >= 2 ) {
            $status = 'average';
            $recommendation = __( 'Placement du mot-clé acceptable. Essayez d\'inclure le mot-clé dans plus d\'emplacements stratégiques.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'Placement du mot-clé à améliorer. Incluez le mot-clé dans le titre, la meta description, les titres et le premier paragraphe.', 'smartseo-ai' );
        }
        
        // Ajouter des recommandations spécifiques
        if ( ! $in_title ) {
            $recommendation .= ' ' . __( 'Incluez le mot-clé dans le titre.', 'smartseo-ai' );
        }
        
        if ( ! $in_meta_description ) {
            $recommendation .= ' ' . __( 'Incluez le mot-clé dans la meta description.', 'smartseo-ai' );
        }
        
        if ( ! $in_headings ) {
            $recommendation .= ' ' . __( 'Incluez le mot-clé dans au moins un titre (H1, H2, H3).', 'smartseo-ai' );
        }
        
        if ( ! $in_first_paragraph ) {
            $recommendation .= ' ' . __( 'Incluez le mot-clé dans le premier paragraphe.', 'smartseo-ai' );
        }
        
        return array(
            'in_title' => $in_title,
            'in_meta_description' => $in_meta_description,
            'in_headings' => $in_headings,
            'in_first_paragraph' => $in_first_paragraph,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Analyse les variations du mot-clé dans le contenu
     *
     * @param string $content Contenu à analyser.
     * @param string $keyword Mot-clé principal.
     * @return array Résultats de l'analyse.
     */
    private function analyze_keyword_variations( $content, $keyword ) {
        // Nettoyer le contenu et le mot-clé
        $clean_content = strtolower( $content );
        $clean_keyword = strtolower( $keyword );
        
        // Générer des variations simples du mot-clé
        $variations = $this->generate_keyword_variations( $clean_keyword );
        
        // Compter les occurrences des variations
        $variation_counts = array();
        $total_variations = 0;
        
        foreach ( $variations as $variation ) {
            $count = substr_count( $clean_content, $variation );
            if ( $count > 0 ) {
                $variation_counts[$variation] = $count;
                $total_variations += $count;
            }
        }
        
        // Déterminer le statut
        $status = 'poor';
        $recommendation = '';
        
        if ( count( $variation_counts ) >= 3 ) {
            $status = 'good';
            $recommendation = __( 'Bonne utilisation des variations du mot-clé. Cela aide à éviter le sur-optimisation tout en renforçant la pertinence thématique.', 'smartseo-ai' );
        } elseif ( count( $variation_counts ) >= 1 ) {
            $status = 'average';
            $recommendation = __( 'Utilisation acceptable des variations du mot-clé. Essayez d\'inclure plus de variations pour renforcer la pertinence thématique.', 'smartseo-ai' );
        } else {
            $recommendation = __( 'Aucune variation du mot-clé détectée. Utilisez des synonymes et des variations du mot-clé pour renforcer la pertinence thématique sans sur-optimiser.', 'smartseo-ai' );
        }
        
        return array(
            'variations' => $variation_counts,
            'total_variations' => $total_variations,
            'status' => $status,
            'recommendation' => $recommendation,
        );
    }

    /**
     * Génère des variations simples du mot-clé
     *
     * @param string $keyword Mot-clé principal.
     * @return array Variations du mot-clé.
     */
    private function generate_keyword_variations( $keyword ) {
        $variations = array();
        
        // Ajouter le mot-clé original
        $variations[] = $keyword;
        
        // Ajouter des variations plurielles/singulières simples
        if ( substr( $keyword, -1 ) === 's' ) {
            $variations[] = substr( $keyword, 0, -1 );
        } else {
            $variations[] = $keyword . 's';
        }
        
        // Ajouter des variations avec des préfixes/suffixes courants
        $prefixes = array( 'le ', 'la ', 'les ', 'un ', 'une ', 'des ', 'ce ', 'cette ', 'ces ' );
        $suffixes = array( ' en ligne', ' gratuit', ' professionnel', ' facile', ' rapide' );
        
        foreach ( $prefixes as $prefix ) {
            $variations[] = $prefix . $keyword;
        }
        
        foreach ( $suffixes as $suffix ) {
            $variations[] = $keyword . $suffix;
        }
        
        // Supprimer les doublons et le mot-clé original
        $variations = array_unique( $variations );
        if ( ( $key = array_search( $keyword, $variations ) ) !== false ) {
            unset( $variations[$key] );
        }
        
        return $variations;
    }

    /**
     * Calcule le score global de l'analyse de mots-clés
     *
     * @param array $density    Résultats de l'analyse de densité.
     * @param array $placement  Résultats de l'analyse de placement.
     * @param array $variations Résultats de l'analyse de variations.
     * @return int Score (0-100).
     */
    private function calculate_score( $density, $placement, $variations ) {
        // Pondération des différents facteurs
        $weights = array(
            'density' => 0.4,
            'placement' => 0.4,
            'variations' => 0.2,
        );
        
        // Convertir les statuts en scores numériques
        $scores = array(
            'density' => $this->status_to_score( $density['status'] ),
            'placement' => $this->status_to_score( $placement['status'] ),
            'variations' => $this->status_to_score( $variations['status'] ),
        );
        
        // Calculer le score pondéré
        $weighted_score = 
            $scores['density'] * $weights['density'] +
            $scores['placement'] * $weights['placement'] +
            $scores['variations'] * $weights['variations'];
        
        // Arrondir le score
        return round( $weighted_score );
    }

    /**
     * Convertit un statut en score numérique
     *
     * @param string $status Statut (good, average, poor).
     * @return int Score (0-100).
     */
    private function status_to_score( $status ) {
        switch ( $status ) {
            case 'good':
                return 100;
            case 'average':
                return 50;
            case 'poor':
                return 0;
            default:
                return 0;
        }
    }
}
