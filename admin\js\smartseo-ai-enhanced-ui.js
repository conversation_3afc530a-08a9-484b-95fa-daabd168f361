/**
 * Interface utilisateur améliorée pour SmartSEO AI
 */
(function($) {
    'use strict';

    // Variables globales
    let dashboardData = {};
    let refreshInterval = null;
    let charts = {};

    // Initialisation
    $(document).ready(function() {
        initEnhancedUI();
        loadDashboardData();
        startAutoRefresh();
        bindEvents();
    });

    /**
     * Initialise l'interface utilisateur améliorée
     */
    function initEnhancedUI() {
        // Ajouter les classes CSS pour l'animation
        $('.smartseo-ai-enhanced-dashboard').addClass('loaded');
        
        // Initialiser les tooltips
        initTooltips();
        
        // Initialiser les notifications toast
        initToastNotifications();
    }

    /**
     * Charge les données du tableau de bord
     */
    function loadDashboardData() {
        showLoadingSpinners();
        
        $.ajax({
            url: smartseoAiEnhanced.ajaxUrl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_get_dashboard_data',
                nonce: smartseoAiEnhanced.nonce
            },
            success: function(response) {
                if (response.success) {
                    dashboardData = response.data;
                    updateDashboard();
                    hideLoadingSpinners();
                } else {
                    showToast('error', smartseoAiEnhanced.i18n.error + ': ' + response.data.message);
                }
            },
            error: function() {
                showToast('error', smartseoAiEnhanced.i18n.error);
                hideLoadingSpinners();
            }
        });
    }

    /**
     * Met à jour le tableau de bord avec les nouvelles données
     */
    function updateDashboard() {
        updateHealthIndicators();
        updateCharts();
        updateQuickStats();
    }

    /**
     * Met à jour les indicateurs de santé
     */
    function updateHealthIndicators() {
        // Santé des API
        const apiHealth = dashboardData.api_health;
        let apiStatus = 'healthy';
        let apiMessage = smartseoAiEnhanced.i18n.apiHealthy;
        
        for (const [provider, health] of Object.entries(apiHealth)) {
            if (health.status === 'error') {
                apiStatus = 'error';
                apiMessage = smartseoAiEnhanced.i18n.apiError + ': ' + provider;
                break;
            }
        }
        
        updateHealthCard('api-health', apiStatus, apiMessage);

        // Cache
        const cacheStats = dashboardData.cache_stats;
        const cacheEntries = cacheStats.general.total_entries || 0;
        const cacheHits = cacheStats.general.total_hits || 0;
        const hitRate = cacheEntries > 0 ? Math.round((cacheHits / cacheEntries) * 100) : 0;
        
        updateHealthCard('cache-health', 'healthy', `${cacheEntries} entrées, ${hitRate}% de hits`);

        // Queue
        const queueStatus = dashboardData.queue_status;
        const pending = queueStatus.pending || 0;
        const processing = queueStatus.processing || 0;
        
        let queueStatusText = 'healthy';
        let queueMessage = `${pending} en attente, ${processing} en cours`;
        
        if (processing > 0) {
            queueStatusText = 'processing';
        }
        
        updateHealthCard('queue-health', queueStatusText, queueMessage);
    }

    /**
     * Met à jour une carte de santé
     */
    function updateHealthCard(cardId, status, message) {
        const $card = $('#' + cardId + '-card');
        const $status = $('#' + cardId + '-status');
        
        $card.removeClass('healthy error processing warning').addClass(status);
        $status.html(`<span class="status-indicator ${status}"></span>${message}`);
    }

    /**
     * Met à jour les statistiques rapides
     */
    function updateQuickStats() {
        if (!dashboardData || !dashboardData.quick_stats) {
            return;
        }

        const stats = dashboardData.quick_stats;

        // Mettre à jour les compteurs
        $('#total-posts-optimized').text(stats.total_optimized || 0);
        $('#optimization-success-rate').text((stats.success_rate || 0) + '%');
        $('#avg-optimization-time').text((stats.avg_time || 0) + 's');
        $('#pending-optimizations').text(stats.pending || 0);

        // Mettre à jour les barres de progression si elles existent
        if (stats.success_rate) {
            $('.success-rate-bar').css('width', stats.success_rate + '%');
        }
    }

    /**
     * Met à jour les graphiques
     */
    function updateCharts() {
        updatePerformanceChart();
        updateCacheChart();
    }

    /**
     * Met à jour le graphique de performance
     */
    function updatePerformanceChart() {
        if (!dashboardData || !dashboardData.performance_stats) {
            return;
        }

        // Simulation d'un graphique simple
        console.log('Mise à jour du graphique de performance');
    }

    /**
     * Met à jour le graphique de cache
     */
    function updateCacheChart() {
        if (!dashboardData || !dashboardData.cache_stats) {
            return;
        }

        // Simulation d'un graphique simple
        console.log('Mise à jour du graphique de cache');
    }

    /**
     * Met à jour le graphique de performance
     */
    function updatePerformanceChart() {
        const ctx = document.getElementById('performance-chart');
        if (!ctx) return;

        const performanceStats = dashboardData.performance_stats;
        const providerStats = performanceStats.by_provider || [];

        // Détruire le graphique existant
        if (charts.performance) {
            charts.performance.destroy();
        }

        const labels = providerStats.map(stat => stat.provider);
        const successRates = providerStats.map(stat => {
            return stat.requests > 0 ? Math.round((stat.successes / stat.requests) * 100) : 0;
        });
        const avgTimes = providerStats.map(stat => parseFloat(stat.avg_time || 0).toFixed(2));

        charts.performance = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Taux de succès (%)',
                    data: successRates,
                    backgroundColor: smartseoAiEnhanced.settings.chartColors.success,
                    yAxisID: 'y'
                }, {
                    label: 'Temps moyen (s)',
                    data: avgTimes,
                    backgroundColor: smartseoAiEnhanced.settings.chartColors.primary,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        max: 100
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }

    /**
     * Met à jour le graphique du cache
     */
    function updateCacheChart() {
        const ctx = document.getElementById('cache-chart');
        if (!ctx) return;

        const cacheStats = dashboardData.cache_stats;
        const general = cacheStats.general || {};

        // Détruire le graphique existant
        if (charts.cache) {
            charts.cache.destroy();
        }

        const activeEntries = general.active_entries || 0;
        const expiredEntries = general.expired_entries || 0;

        charts.cache = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Actives', 'Expirées'],
                datasets: [{
                    data: [activeEntries, expiredEntries],
                    backgroundColor: [
                        smartseoAiEnhanced.settings.chartColors.success,
                        smartseoAiEnhanced.settings.chartColors.warning
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    /**
     * Démarre l'actualisation automatique
     */
    function startAutoRefresh() {
        refreshInterval = setInterval(function() {
            loadDashboardData();
        }, smartseoAiEnhanced.settings.refreshInterval);
    }

    /**
     * Arrête l'actualisation automatique
     */
    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }

    /**
     * Lie les événements
     */
    function bindEvents() {
        // Bouton vider le cache
        $('#clear-cache-btn').on('click', function() {
            if (confirm(smartseoAiEnhanced.i18n.confirm)) {
                clearCache();
            }
        });

        // Bouton vérifier les API
        $('#check-api-health-btn').on('click', function() {
            checkApiHealth();
        });

        // Bouton actualiser
        $('#refresh-dashboard-btn').on('click', function() {
            loadDashboardData();
        });

        // Optimisation en masse
        $('.start-bulk-optimization').on('click', function() {
            const postIds = getSelectedPostIds();
            if (postIds.length > 0) {
                startBulkOptimization(postIds);
            } else {
                showToast('warning', 'Veuillez sélectionner au moins un article.');
            }
        });

        // Arrêter l'optimisation en masse
        $('.stop-bulk-optimization').on('click', function() {
            if (confirm(smartseoAiEnhanced.i18n.confirm)) {
                stopBulkOptimization();
            }
        });
    }

    /**
     * Vide le cache
     */
    function clearCache() {
        const $btn = $('#clear-cache-btn');
        $btn.prop('disabled', true).text(smartseoAiEnhanced.i18n.loading);

        $.ajax({
            url: smartseoAiEnhanced.ajaxUrl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_clear_cache',
                nonce: smartseoAiEnhanced.nonce
            },
            success: function(response) {
                if (response.success) {
                    showToast('success', smartseoAiEnhanced.i18n.cacheCleared);
                    loadDashboardData();
                } else {
                    showToast('error', response.data.message);
                }
            },
            error: function() {
                showToast('error', smartseoAiEnhanced.i18n.error);
            },
            complete: function() {
                $btn.prop('disabled', false).text('Vider le cache');
            }
        });
    }

    /**
     * Vérifie la santé des API
     */
    function checkApiHealth() {
        const $btn = $('#check-api-health-btn');
        $btn.prop('disabled', true).text(smartseoAiEnhanced.i18n.loading);

        $.ajax({
            url: smartseoAiEnhanced.ajaxUrl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_check_api_health',
                nonce: smartseoAiEnhanced.nonce
            },
            success: function(response) {
                if (response.success) {
                    dashboardData.api_health = response.data;
                    updateHealthIndicators();
                    showToast('success', 'Vérification terminée');
                } else {
                    showToast('error', response.data.message);
                }
            },
            error: function() {
                showToast('error', smartseoAiEnhanced.i18n.error);
            },
            complete: function() {
                $btn.prop('disabled', false).text('Vérifier les API');
            }
        });
    }

    /**
     * Démarre l'optimisation en masse
     */
    function startBulkOptimization(postIds) {
        $.ajax({
            url: smartseoAiEnhanced.ajaxUrl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_start_bulk_optimization',
                nonce: smartseoAiEnhanced.nonce,
                post_ids: postIds,
                optimization_type: 'full_optimization',
                priority: 5
            },
            success: function(response) {
                if (response.success) {
                    showToast('success', smartseoAiEnhanced.i18n.optimizationStarted);
                    loadDashboardData();
                } else {
                    showToast('error', response.data.message);
                }
            },
            error: function() {
                showToast('error', smartseoAiEnhanced.i18n.error);
            }
        });
    }

    /**
     * Arrête l'optimisation en masse
     */
    function stopBulkOptimization() {
        $.ajax({
            url: smartseoAiEnhanced.ajaxUrl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_stop_bulk_optimization',
                nonce: smartseoAiEnhanced.nonce
            },
            success: function(response) {
                if (response.success) {
                    showToast('success', smartseoAiEnhanced.i18n.optimizationStopped);
                    loadDashboardData();
                } else {
                    showToast('error', response.data.message);
                }
            },
            error: function() {
                showToast('error', smartseoAiEnhanced.i18n.error);
            }
        });
    }

    /**
     * Récupère les IDs des articles sélectionnés
     */
    function getSelectedPostIds() {
        const postIds = [];
        $('.post-checkbox:checked').each(function() {
            postIds.push($(this).val());
        });
        return postIds;
    }

    /**
     * Affiche/masque les spinners de chargement
     */
    function showLoadingSpinners() {
        $('.health-status .spinner').addClass('is-active');
    }

    function hideLoadingSpinners() {
        $('.health-status .spinner').removeClass('is-active');
    }

    /**
     * Initialise les tooltips
     */
    function initTooltips() {
        $('[data-tooltip]').each(function() {
            $(this).attr('title', $(this).data('tooltip'));
        });
    }

    /**
     * Initialise le système de notifications toast
     */
    function initToastNotifications() {
        if (!$('#smartseo-toast-container').length) {
            $('body').append('<div id="smartseo-toast-container"></div>');
        }
    }

    /**
     * Affiche une notification toast
     */
    function showToast(type, message, duration = 5000) {
        const $container = $('#smartseo-toast-container');
        const $toast = $(`
            <div class="smartseo-toast toast-${type}">
                <span class="toast-icon"></span>
                <span class="toast-message">${message}</span>
                <button class="toast-close">&times;</button>
            </div>
        `);

        $container.append($toast);
        
        // Animation d'entrée
        setTimeout(() => $toast.addClass('show'), 100);

        // Fermeture automatique
        setTimeout(() => {
            $toast.removeClass('show');
            setTimeout(() => $toast.remove(), 300);
        }, duration);

        // Fermeture manuelle
        $toast.find('.toast-close').on('click', function() {
            $toast.removeClass('show');
            setTimeout(() => $toast.remove(), 300);
        });
    }

    // Nettoyage lors du déchargement de la page
    $(window).on('beforeunload', function() {
        stopAutoRefresh();
        
        // Détruire les graphiques
        Object.values(charts).forEach(chart => {
            if (chart) chart.destroy();
        });
    });

})(jQuery);
