/**
 * Script JavaScript pour le Générateur de Tags IA (éditeur Gutenberg)
 *
 * @package SmartSEO_AI
 */

(function(wp, $) {
    'use strict';

    // Récupérer les dépendances de WordPress
    const { __ } = wp.i18n;
    const { registerPlugin } = wp.plugins;
    const { PluginDocumentSettingPanel } = wp.editPost;
    const { Button, CheckboxControl, SelectControl, Spinner, PanelRow } = wp.components;
    const { useState, useEffect } = wp.element;
    const { useSelect, useDispatch } = wp.data;

    /**
     * Composant pour le Générateur de Tags IA
     */
    const TagGeneratorPanel = () => {
        // États
        const [isLoading, setIsLoading] = useState(false);
        const [error, setError] = useState(null);
        const [tags, setTags] = useState([]);
        const [optimalTags, setOptimalTags] = useState([]);
        const [includeBrands, setIncludeBrands] = useState(true);
        const [includeRegions, setIncludeRegions] = useState(true);
        const [useExternalApi, setUseExternalApi] = useState(false);
        const [externalApi, setExternalApi] = useState('');
        const [selectedTags, setSelectedTags] = useState([]);

        // Récupérer les données de l'article
        const postId = useSelect(select => select('core/editor').getCurrentPostId());
        const postContent = useSelect(select => select('core/editor').getEditedPostContent());
        const postTitle = useSelect(select => select('core/editor').getEditedPostAttribute('title'));
        const postCategories = useSelect(select => select('core/editor').getEditedPostAttribute('categories'));
        
        // Récupérer les tags existants
        const existingTags = useSelect(select => {
            const postTags = select('core/editor').getEditedPostAttribute('tags');
            return postTags || [];
        });
        
        // Récupérer les noms des catégories
        const categoryNames = useSelect(select => {
            const categories = select('core').getEntityRecords('taxonomy', 'category', { include: postCategories });
            return categories ? categories.map(category => category.name) : [];
        });
        
        // Dispatcher pour mettre à jour les tags
        const { editPost } = useDispatch('core/editor');

        /**
         * Génère les tags
         */
        const generateTags = () => {
            // Réinitialiser les états
            setIsLoading(true);
            setError(null);
            setTags([]);
            setOptimalTags([]);
            setSelectedTags([]);
            
            // Récupérer la catégorie
            const category = categoryNames.length > 0 ? categoryNames[0] : '';
            
            // Appeler l'API
            $.ajax({
                url: smartseoAiTagGenerator.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartseo_ai_generate_tags',
                    nonce: smartseoAiTagGenerator.nonce,
                    post_id: postId,
                    content: postContent,
                    title: postTitle,
                    category: category,
                    include_brands: includeBrands,
                    include_regions: includeRegions,
                    use_external_api: useExternalApi,
                    external_api: externalApi
                },
                success: (response) => {
                    setIsLoading(false);
                    
                    if (response.success) {
                        setTags(response.data.tags);
                        setOptimalTags(response.data.optimal_tags);
                    } else {
                        setError(response.data.message);
                    }
                },
                error: () => {
                    setIsLoading(false);
                    setError(smartseoAiTagGenerator.i18n.error);
                }
            });
        };

        /**
         * Applique les tags
         */
        const applyTags = () => {
            // Vérifier que les tags sont disponibles
            if (tags.length === 0) {
                return;
            }
            
            // Récupérer les tags à appliquer
            const tagsToApply = selectedTags.length > 0 ? selectedTags : tags;
            
            // Appeler l'API
            $.ajax({
                url: smartseoAiTagGenerator.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartseo_ai_apply_tags',
                    nonce: smartseoAiTagGenerator.nonce,
                    post_id: postId,
                    tags: tagsToApply
                },
                success: (response) => {
                    if (response.success) {
                        // Afficher un message de succès
                        wp.data.dispatch('core/notices').createSuccessNotice(
                            smartseoAiTagGenerator.i18n.apply_success,
                            { type: 'snackbar' }
                        );
                        
                        // Mettre à jour les tags dans l'éditeur
                        const tagIds = existingTags.slice();
                        
                        // Récupérer les IDs des tags
                        $.ajax({
                            url: smartseoAiTagGenerator.ajax_url,
                            type: 'POST',
                            data: {
                                action: 'wp_ajax_get_tagcloud',
                                tax: 'post_tag'
                            },
                            success: (response) => {
                                if (response.success) {
                                    // Mettre à jour les tags
                                    editPost({ tags: tagIds });
                                }
                            }
                        });
                    } else {
                        // Afficher un message d'erreur
                        wp.data.dispatch('core/notices').createErrorNotice(
                            response.data.message,
                            { type: 'snackbar' }
                        );
                    }
                },
                error: () => {
                    // Afficher un message d'erreur
                    wp.data.dispatch('core/notices').createErrorNotice(
                        smartseoAiTagGenerator.i18n.apply_error,
                        { type: 'snackbar' }
                    );
                }
            });
        };

        /**
         * Gère la sélection d'un tag
         *
         * @param {Object} tag Tag à sélectionner/désélectionner.
         */
        const handleTagSelection = (tag) => {
            // Vérifier si le tag est déjà sélectionné
            const isSelected = selectedTags.some(selectedTag => selectedTag.name === tag.name);
            
            if (isSelected) {
                // Désélectionner le tag
                setSelectedTags(selectedTags.filter(selectedTag => selectedTag.name !== tag.name));
            } else {
                // Sélectionner le tag
                setSelectedTags([...selectedTags, tag]);
            }
        };

        /**
         * Rendu du composant
         */
        return (
            <div className="smartseo-ai-tag-generator-panel">
                <div className="smartseo-ai-tag-generator-options">
                    <PanelRow>
                        <CheckboxControl
                            label={smartseoAiTagGenerator.i18n.include_brands}
                            checked={includeBrands}
                            onChange={setIncludeBrands}
                        />
                    </PanelRow>
                    
                    <PanelRow>
                        <CheckboxControl
                            label={smartseoAiTagGenerator.i18n.include_regions}
                            checked={includeRegions}
                            onChange={setIncludeRegions}
                        />
                    </PanelRow>
                    
                    <PanelRow>
                        <CheckboxControl
                            label={__('Utiliser une API externe')}
                            checked={useExternalApi}
                            onChange={setUseExternalApi}
                        />
                    </PanelRow>
                    
                    {useExternalApi && (
                        <PanelRow>
                            <SelectControl
                                label={__('API externe')}
                                value={externalApi}
                                options={[
                                    { label: __('Aucune'), value: '' },
                                    { label: __('Google Trends'), value: 'google_trends' },
                                    { label: __('Semrush'), value: 'semrush' }
                                ]}
                                onChange={setExternalApi}
                            />
                        </PanelRow>
                    )}
                </div>
                
                <div className="smartseo-ai-tag-generator-actions">
                    <PanelRow>
                        <Button
                            isPrimary
                            onClick={generateTags}
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <>
                                    <Spinner />
                                    {smartseoAiTagGenerator.i18n.loading}
                                </>
                            ) : (
                                tags.length > 0 ? smartseoAiTagGenerator.i18n.regenerate : smartseoAiTagGenerator.i18n.generate
                            )}
                        </Button>
                    </PanelRow>
                </div>
                
                {error && (
                    <div className="smartseo-ai-tag-generator-error">
                        <p className="smartseo-ai-tag-generator-error-message">{error}</p>
                    </div>
                )}
                
                {tags.length > 0 && (
                    <div className="smartseo-ai-tag-generator-results">
                        {existingTags.length > 0 && (
                            <div className="smartseo-ai-tag-generator-existing-tags">
                                <h4>{smartseoAiTagGenerator.i18n.existing_tags}</h4>
                                
                                <div className="smartseo-ai-tag-generator-existing-tags-list">
                                    {existingTags.map(tagId => (
                                        <span key={tagId} className="smartseo-ai-tag-generator-tag">
                                            {tagId}
                                        </span>
                                    ))}
                                </div>
                            </div>
                        )}
                        
                        {optimalTags.length > 0 && (
                            <div className="smartseo-ai-tag-generator-optimal-tags">
                                <h4>{smartseoAiTagGenerator.i18n.optimal_combination}</h4>
                                
                                <div className="smartseo-ai-tag-generator-optimal-tags-list">
                                    {optimalTags.map(tag => (
                                        <span
                                            key={tag.name}
                                            className="smartseo-ai-tag-generator-optimal-tag"
                                            onClick={() => handleTagSelection(tag)}
                                        >
                                            {tag.name}
                                            <span className="smartseo-ai-tag-generator-tag-score">
                                                {tag.score}
                                            </span>
                                        </span>
                                    ))}
                                </div>
                            </div>
                        )}
                        
                        <div className="smartseo-ai-tag-generator-generated-tags">
                            <h4>{smartseoAiTagGenerator.i18n.generated_tags}</h4>
                            
                            <div className="smartseo-ai-tag-generator-generated-tags-list">
                                {tags.map(tag => (
                                    <span
                                        key={tag.name}
                                        className={`smartseo-ai-tag-generator-generated-tag ${selectedTags.some(selectedTag => selectedTag.name === tag.name) ? 'selected' : ''}`}
                                        onClick={() => handleTagSelection(tag)}
                                    >
                                        {tag.name}
                                        <span className="smartseo-ai-tag-generator-tag-score">
                                            {tag.score}
                                        </span>
                                    </span>
                                ))}
                            </div>
                        </div>
                        
                        <div className="smartseo-ai-tag-generator-actions">
                            <PanelRow>
                                <Button
                                    isPrimary
                                    onClick={applyTags}
                                >
                                    {smartseoAiTagGenerator.i18n.apply}
                                </Button>
                            </PanelRow>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    // Enregistrer le plugin
    registerPlugin('smartseo-ai-tag-generator', {
        render: () => (
            <PluginDocumentSettingPanel
                name="smartseo-ai-tag-generator"
                title={smartseoAiTagGenerator.i18n.title}
                className="smartseo-ai-tag-generator-panel"
            >
                <TagGeneratorPanel />
            </PluginDocumentSettingPanel>
        ),
        icon: 'tag'
    });
})(window.wp, jQuery);
