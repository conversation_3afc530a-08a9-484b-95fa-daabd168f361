<?php
/**
 * Classe d'interface utilisateur des paramètres du Sitemap XML
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'interface utilisateur des paramètres du Sitemap XML
 */
class SmartSEO_AI_Sitemap_Settings_UI {

    /**
     * Options du sitemap
     *
     * @var array
     */
    private $options;

    /**
     * Constructeur
     *
     * @param array $options Options du sitemap.
     */
    public function __construct( $options ) {
        $this->options = $options;
    }

    /**
     * Affiche le formulaire des paramètres
     */
    public function render_settings_form() {
        ?>
        <div class="smartseo-ai-settings-section">
            <h2><?php _e( 'Paramètres du Sitemap XML', 'smartseo-ai' ); ?></h2>

            <p class="description">
                <?php _e( 'Configurez les options de votre sitemap XML pour améliorer l\'indexation de votre site par les moteurs de recherche.', 'smartseo-ai' ); ?>
            </p>

            <form method="post" action="options.php">
                <?php settings_fields( 'smartseo-ai-settings' ); ?>

                <div class="smartseo-ai-settings-card">
                    <h3><?php _e( 'Activation du Sitemap', 'smartseo-ai' ); ?></h3>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_enabled">
                            <input type="checkbox" id="smartseo_ai_sitemap_enabled" name="smartseo_ai_sitemap_options[enabled]" value="yes" <?php checked( $this->options['enabled'], 'yes' ); ?>>
                            <?php _e( 'Activer le sitemap XML', 'smartseo-ai' ); ?>
                        </label>
                        <p class="description">
                            <?php _e( 'Activez cette option pour générer automatiquement un sitemap XML pour votre site.', 'smartseo-ai' ); ?>
                        </p>
                    </div>

                    <?php if ( 'yes' === $this->options['enabled'] ) : ?>
                        <div class="smartseo-ai-settings-field">
                            <label><?php _e( 'URL du Sitemap', 'smartseo-ai' ); ?></label>
                            <div class="smartseo-ai-url-display">
                                <input type="text" readonly value="<?php echo esc_url( home_url( 'sitemap.xml' ) ); ?>" class="regular-text">
                                <button type="button" class="button smartseo-ai-copy-url" data-url="<?php echo esc_url( home_url( 'sitemap.xml' ) ); ?>">
                                    <span class="dashicons dashicons-clipboard"></span>
                                    <?php _e( 'Copier l\'URL', 'smartseo-ai' ); ?>
                                </button>
                            </div>
                            <p class="description">
                                <?php _e( 'Utilisez cette URL pour soumettre votre sitemap aux moteurs de recherche.', 'smartseo-ai' ); ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="smartseo-ai-settings-card">
                    <h3><?php _e( 'Contenus à inclure', 'smartseo-ai' ); ?></h3>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_include_posts">
                            <input type="checkbox" id="smartseo_ai_sitemap_include_posts" name="smartseo_ai_sitemap_options[include_posts]" value="yes" <?php checked( $this->options['include_posts'], 'yes' ); ?>>
                            <?php _e( 'Articles', 'smartseo-ai' ); ?>
                        </label>
                    </div>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_include_pages">
                            <input type="checkbox" id="smartseo_ai_sitemap_include_pages" name="smartseo_ai_sitemap_options[include_pages]" value="yes" <?php checked( $this->options['include_pages'], 'yes' ); ?>>
                            <?php _e( 'Pages', 'smartseo-ai' ); ?>
                        </label>
                    </div>

                    <?php if ( class_exists( 'WooCommerce' ) ) : ?>
                        <div class="smartseo-ai-settings-field">
                            <label for="smartseo_ai_sitemap_include_products">
                                <input type="checkbox" id="smartseo_ai_sitemap_include_products" name="smartseo_ai_sitemap_options[include_products]" value="yes" <?php checked( $this->options['include_products'], 'yes' ); ?>>
                                <?php _e( 'Produits WooCommerce', 'smartseo-ai' ); ?>
                            </label>
                        </div>
                    <?php endif; ?>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_include_categories">
                            <input type="checkbox" id="smartseo_ai_sitemap_include_categories" name="smartseo_ai_sitemap_options[include_categories]" value="yes" <?php checked( $this->options['include_categories'], 'yes' ); ?>>
                            <?php _e( 'Catégories', 'smartseo-ai' ); ?>
                        </label>
                    </div>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_include_tags">
                            <input type="checkbox" id="smartseo_ai_sitemap_include_tags" name="smartseo_ai_sitemap_options[include_tags]" value="yes" <?php checked( $this->options['include_tags'], 'yes' ); ?>>
                            <?php _e( 'Étiquettes', 'smartseo-ai' ); ?>
                        </label>
                    </div>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_include_custom_taxonomies">
                            <input type="checkbox" id="smartseo_ai_sitemap_include_custom_taxonomies" name="smartseo_ai_sitemap_options[include_custom_taxonomies]" value="yes" <?php checked( $this->options['include_custom_taxonomies'], 'yes' ); ?>>
                            <?php _e( 'Taxonomies personnalisées', 'smartseo-ai' ); ?>
                        </label>
                    </div>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_include_custom_post_types">
                            <input type="checkbox" id="smartseo_ai_sitemap_include_custom_post_types" name="smartseo_ai_sitemap_options[include_custom_post_types]" value="yes" <?php checked( $this->options['include_custom_post_types'], 'yes' ); ?>>
                            <?php _e( 'Types de contenu personnalisés', 'smartseo-ai' ); ?>
                        </label>
                    </div>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_include_media">
                            <input type="checkbox" id="smartseo_ai_sitemap_include_media" name="smartseo_ai_sitemap_options[include_media]" value="yes" <?php checked( $this->options['include_media'], 'yes' ); ?>>
                            <?php _e( 'Fichiers médias', 'smartseo-ai' ); ?>
                        </label>
                    </div>
                </div>

                <div class="smartseo-ai-settings-card">
                    <h3><?php _e( 'Fréquence de mise à jour', 'smartseo-ai' ); ?></h3>
                    <p class="description">
                        <?php _e( 'Définissez la fréquence à laquelle les moteurs de recherche doivent explorer chaque type de contenu.', 'smartseo-ai' ); ?>
                    </p>

                    <?php
                    $content_types = array(
                        'post'       => __( 'Articles', 'smartseo-ai' ),
                        'page'       => __( 'Pages', 'smartseo-ai' ),
                        'product'    => __( 'Produits', 'smartseo-ai' ),
                        'category'   => __( 'Catégories', 'smartseo-ai' ),
                        'post_tag'   => __( 'Étiquettes', 'smartseo-ai' ),
                        'attachment' => __( 'Médias', 'smartseo-ai' ),
                    );

                    $frequencies = array(
                        'always'  => __( 'Toujours', 'smartseo-ai' ),
                        'hourly'  => __( 'Toutes les heures', 'smartseo-ai' ),
                        'daily'   => __( 'Quotidien', 'smartseo-ai' ),
                        'weekly'  => __( 'Hebdomadaire', 'smartseo-ai' ),
                        'monthly' => __( 'Mensuel', 'smartseo-ai' ),
                        'yearly'  => __( 'Annuel', 'smartseo-ai' ),
                        'never'   => __( 'Jamais', 'smartseo-ai' ),
                    );

                    foreach ( $content_types as $type => $label ) :
                        // Ignorer les produits si WooCommerce n'est pas activé
                        if ( 'product' === $type && ! class_exists( 'WooCommerce' ) ) {
                            continue;
                        }

                        $current_frequency = isset( $this->options['frequencies'][ $type ] ) ? $this->options['frequencies'][ $type ] : 'monthly';
                        ?>
                        <div class="smartseo-ai-settings-field smartseo-ai-settings-field-row">
                            <label for="smartseo_ai_sitemap_frequency_<?php echo esc_attr( $type ); ?>"><?php echo esc_html( $label ); ?></label>
                            <select id="smartseo_ai_sitemap_frequency_<?php echo esc_attr( $type ); ?>" name="smartseo_ai_sitemap_options[frequencies][<?php echo esc_attr( $type ); ?>]">
                                <?php foreach ( $frequencies as $value => $name ) : ?>
                                    <option value="<?php echo esc_attr( $value ); ?>" <?php selected( $current_frequency, $value ); ?>><?php echo esc_html( $name ); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="smartseo-ai-settings-card">
                    <h3><?php _e( 'Priorité d\'indexation', 'smartseo-ai' ); ?></h3>
                    <p class="description">
                        <?php _e( 'Définissez la priorité d\'indexation pour chaque type de contenu (0.0 à 1.0).', 'smartseo-ai' ); ?>
                    </p>

                    <?php
                    foreach ( $content_types as $type => $label ) :
                        // Ignorer les produits si WooCommerce n'est pas activé
                        if ( 'product' === $type && ! class_exists( 'WooCommerce' ) ) {
                            continue;
                        }

                        $current_priority = isset( $this->options['priorities'][ $type ] ) ? $this->options['priorities'][ $type ] : 0.5;
                        ?>
                        <div class="smartseo-ai-settings-field smartseo-ai-settings-field-row">
                            <label for="smartseo_ai_sitemap_priority_<?php echo esc_attr( $type ); ?>"><?php echo esc_html( $label ); ?></label>
                            <div class="smartseo-ai-range-slider">
                                <input type="range" id="smartseo_ai_sitemap_priority_<?php echo esc_attr( $type ); ?>" name="smartseo_ai_sitemap_options[priorities][<?php echo esc_attr( $type ); ?>]" min="0" max="1" step="0.1" value="<?php echo esc_attr( $current_priority ); ?>">
                                <span class="smartseo-ai-range-value"><?php echo esc_html( $current_priority ); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="smartseo-ai-settings-card">
                    <h3><?php _e( 'Options avancées', 'smartseo-ai' ); ?></h3>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_use_index">
                            <input type="checkbox" id="smartseo_ai_sitemap_use_index" name="smartseo_ai_sitemap_options[use_index]" value="yes" <?php checked( $this->options['use_index'], 'yes' ); ?>>
                            <?php _e( 'Utiliser un sitemap index', 'smartseo-ai' ); ?>
                        </label>
                        <p class="description">
                            <?php _e( 'Activez cette option pour créer un fichier sitemap_index.xml qui regroupe les différents sitemaps (utile pour les sites à forte volumétrie).', 'smartseo-ai' ); ?>
                        </p>
                    </div>

                    <div class="smartseo-ai-settings-field">
                        <label for="smartseo_ai_sitemap_max_entries"><?php _e( 'Nombre maximum d\'entrées par sitemap', 'smartseo-ai' ); ?></label>
                        <input type="number" id="smartseo_ai_sitemap_max_entries" name="smartseo_ai_sitemap_options[max_entries]" value="<?php echo esc_attr( $this->options['max_entries'] ); ?>" min="100" max="50000" step="100" class="small-text">
                        <p class="description">
                            <?php _e( 'Définissez le nombre maximum d\'URLs à inclure dans chaque sitemap. La valeur recommandée est de 1000.', 'smartseo-ai' ); ?>
                        </p>
                    </div>
                </div>

                <div class="smartseo-ai-settings-card">
                    <h3><?php _e( 'Outils', 'smartseo-ai' ); ?></h3>

                    <div class="smartseo-ai-settings-field">
                        <button type="button" id="smartseo_ai_regenerate_sitemap" class="button button-secondary">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e( 'Régénérer le sitemap', 'smartseo-ai' ); ?>
                        </button>
                        <p class="description">
                            <?php _e( 'Cliquez sur ce bouton pour forcer la régénération du sitemap.', 'smartseo-ai' ); ?>
                        </p>
                    </div>

                    <div class="smartseo-ai-settings-field">
                        <a href="https://search.google.com/search-console" target="_blank" class="button button-secondary">
                            <span class="dashicons dashicons-external"></span>
                            <?php _e( 'Valider dans Google Search Console', 'smartseo-ai' ); ?>
                        </a>
                        <p class="description">
                            <?php _e( 'Ouvrez Google Search Console pour tester et valider votre sitemap.', 'smartseo-ai' ); ?>
                        </p>
                    </div>

                    <div class="smartseo-ai-settings-field">
                        <button type="button" id="smartseo_ai_validate_sitemap" class="button button-secondary">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <?php _e( 'Vérifier la validité du sitemap', 'smartseo-ai' ); ?>
                        </button>
                        <div id="smartseo_ai_sitemap_validation_result" class="smartseo-ai-validation-result"></div>
                    </div>
                </div>

                <?php if ( 'yes' === $this->options['enabled'] ) : ?>
                    <div class="smartseo-ai-settings-card">
                        <h3><?php _e( 'Analyse IA', 'smartseo-ai' ); ?></h3>

                        <div class="smartseo-ai-settings-field">
                            <button type="button" id="smartseo_ai_analyze_sitemap" class="button button-secondary">
                                <span class="dashicons dashicons-superhero"></span>
                                <?php _e( 'Analyser le sitemap avec l\'IA', 'smartseo-ai' ); ?>
                            </button>
                            <p class="description">
                                <?php _e( 'Utilisez l\'IA pour analyser votre sitemap et obtenir des suggestions d\'amélioration.', 'smartseo-ai' ); ?>
                            </p>
                        </div>

                        <div id="smartseo_ai_sitemap_analysis_result" class="smartseo-ai-analysis-result"></div>
                    </div>
                <?php endif; ?>

                <?php submit_button( __( 'Enregistrer les paramètres', 'smartseo-ai' ), 'primary', 'submit', true ); ?>
            </form>
        </div>
        <?php
    }
}
