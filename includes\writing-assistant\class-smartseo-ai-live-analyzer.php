<?php
/**
 * Classe pour l'analyse SEO en temps réel
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'analyse SEO en temps réel
 */
class SmartSEO_AI_Live_Analyzer {

    /**
     * Instance de l'analyseur de contenu
     *
     * @var SmartSEO_AI_Content_Analyzer
     */
    private $content_analyzer;

    /**
     * Instance de l'analyseur de mots-clés
     *
     * @var SmartSEO_AI_Keyword_Density_Analyzer
     */
    private $keyword_analyzer;

    /**
     * Instance de l'analyseur de structure
     *
     * @var SmartSEO_AI_Structure_Analyzer
     */
    private $structure_analyzer;

    /**
     * Instance de l'analyseur de liens
     *
     * @var SmartSEO_AI_Link_Analyzer
     */
    private $link_analyzer;

    /**
     * Instance de l'analyseur d'images
     *
     * @var SmartSEO_AI_Image_Analyzer
     */
    private $image_analyzer;

    /**
     * Constructeur
     */
    public function __construct() {
        // Initialiser les analyseurs spécialisés
        $this->content_analyzer = new SmartSEO_AI_Content_Analyzer();
        $this->keyword_analyzer = new SmartSEO_AI_Keyword_Density_Analyzer();
        $this->structure_analyzer = new SmartSEO_AI_Structure_Analyzer();
        $this->link_analyzer = new SmartSEO_AI_Link_Analyzer();
        $this->image_analyzer = new SmartSEO_AI_Image_Analyzer();
    }

    /**
     * Analyse le contenu en temps réel
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @param string $title   Titre de l'article.
     * @param string $keyword Mot-clé principal.
     * @return array Résultats de l'analyse.
     */
    public function analyze( $post_id, $content, $title, $keyword = '' ) {
        // Si le mot-clé n'est pas fourni, essayer de le récupérer des métadonnées
        if ( empty( $keyword ) && $post_id > 0 ) {
            $keyword = get_post_meta( $post_id, 'smartseo_ai_focus_keyword', true );
            if ( empty( $keyword ) ) {
                $keyword = get_post_meta( $post_id, '_yoast_wpseo_focuskw', true );
            }
        }

        // Récupérer la meta description
        $meta_description = '';
        if ( $post_id > 0 ) {
            $meta_description = get_post_meta( $post_id, 'smartseo_ai_meta_description', true );
            if ( empty( $meta_description ) ) {
                $meta_description = get_post_meta( $post_id, '_yoast_wpseo_metadesc', true );
            }
        }

        // Analyser le contenu
        $content_analysis = $this->content_analyzer->analyze( $content, $title );
        
        // Analyser les mots-clés
        $keyword_analysis = $this->keyword_analyzer->analyze( $content, $title, $meta_description, $keyword );
        
        // Analyser la structure
        $structure_analysis = $this->structure_analyzer->analyze( $content, $title );
        
        // Analyser les liens
        $link_analysis = $this->link_analyzer->analyze( $content, $post_id );
        
        // Analyser les images
        $image_analysis = $this->image_analyzer->analyze( $content, $keyword );
        
        // Calculer le score SEO global
        $seo_score = $this->calculate_seo_score(
            $content_analysis,
            $keyword_analysis,
            $structure_analysis,
            $link_analysis,
            $image_analysis
        );
        
        // Générer la checklist d'optimisation
        $optimization_checklist = $this->generate_optimization_checklist(
            $content_analysis,
            $keyword_analysis,
            $structure_analysis,
            $link_analysis,
            $image_analysis
        );
        
        // Retourner les résultats
        return array(
            'score' => $seo_score,
            'content_analysis' => $content_analysis,
            'keyword_analysis' => $keyword_analysis,
            'structure_analysis' => $structure_analysis,
            'link_analysis' => $link_analysis,
            'image_analysis' => $image_analysis,
            'optimization_checklist' => $optimization_checklist,
        );
    }

    /**
     * Calcule le score SEO global
     *
     * @param array $content_analysis   Résultats de l'analyse de contenu.
     * @param array $keyword_analysis   Résultats de l'analyse de mots-clés.
     * @param array $structure_analysis Résultats de l'analyse de structure.
     * @param array $link_analysis      Résultats de l'analyse de liens.
     * @param array $image_analysis     Résultats de l'analyse d'images.
     * @return int Score SEO (0-100).
     */
    private function calculate_seo_score( $content_analysis, $keyword_analysis, $structure_analysis, $link_analysis, $image_analysis ) {
        // Pondération des différentes analyses
        $weights = array(
            'content' => 0.25,
            'keyword' => 0.30,
            'structure' => 0.20,
            'link' => 0.15,
            'image' => 0.10,
        );
        
        // Calculer le score pondéré
        $weighted_score = 
            $content_analysis['score'] * $weights['content'] +
            $keyword_analysis['score'] * $weights['keyword'] +
            $structure_analysis['score'] * $weights['structure'] +
            $link_analysis['score'] * $weights['link'] +
            $image_analysis['score'] * $weights['image'];
        
        // Arrondir le score
        return round( $weighted_score );
    }

    /**
     * Génère la checklist d'optimisation
     *
     * @param array $content_analysis   Résultats de l'analyse de contenu.
     * @param array $keyword_analysis   Résultats de l'analyse de mots-clés.
     * @param array $structure_analysis Résultats de l'analyse de structure.
     * @param array $link_analysis      Résultats de l'analyse de liens.
     * @param array $image_analysis     Résultats de l'analyse d'images.
     * @return array Checklist d'optimisation.
     */
    private function generate_optimization_checklist( $content_analysis, $keyword_analysis, $structure_analysis, $link_analysis, $image_analysis ) {
        $checklist = array();
        
        // Ajouter les éléments de la checklist pour le contenu
        foreach ( $content_analysis['checks'] as $check ) {
            $checklist[] = array(
                'category' => 'content',
                'label' => $check['label'],
                'status' => $check['status'],
                'recommendation' => $check['recommendation'],
            );
        }
        
        // Ajouter les éléments de la checklist pour les mots-clés
        foreach ( $keyword_analysis['checks'] as $check ) {
            $checklist[] = array(
                'category' => 'keyword',
                'label' => $check['label'],
                'status' => $check['status'],
                'recommendation' => $check['recommendation'],
            );
        }
        
        // Ajouter les éléments de la checklist pour la structure
        foreach ( $structure_analysis['checks'] as $check ) {
            $checklist[] = array(
                'category' => 'structure',
                'label' => $check['label'],
                'status' => $check['status'],
                'recommendation' => $check['recommendation'],
            );
        }
        
        // Ajouter les éléments de la checklist pour les liens
        foreach ( $link_analysis['checks'] as $check ) {
            $checklist[] = array(
                'category' => 'link',
                'label' => $check['label'],
                'status' => $check['status'],
                'recommendation' => $check['recommendation'],
            );
        }
        
        // Ajouter les éléments de la checklist pour les images
        foreach ( $image_analysis['checks'] as $check ) {
            $checklist[] = array(
                'category' => 'image',
                'label' => $check['label'],
                'status' => $check['status'],
                'recommendation' => $check['recommendation'],
            );
        }
        
        return $checklist;
    }
}
