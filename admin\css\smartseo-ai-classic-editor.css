/**
 * Styles spécifiques pour l'éditeur classique
 *
 * @package SmartSEO_AI
 */

/* Styles pour la métabox */
#smartseo-ai-writing-assistant {
    margin-bottom: 20px;
}

#smartseo-ai-writing-assistant .inside {
    margin: 0;
    padding: 0;
}

/* Styles pour la barre latérale */
.smartseo-ai-classic-sidebar {
    margin-top: 20px;
    border: 1px solid #ddd;
    background-color: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.smartseo-ai-classic-sidebar-header {
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    background-color: #f5f5f5;
}

.smartseo-ai-classic-sidebar-header h2 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.smartseo-ai-classic-sidebar-content {
    padding: 12px;
}

/* Styles pour les onglets */
.smartseo-ai-tabs {
    margin-bottom: 20px;
}

.smartseo-ai-tabs-nav {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: 1px solid #ccc;
}

.smartseo-ai-tab-nav {
    padding: 8px 12px;
    margin-right: 5px;
    margin-bottom: -1px;
    cursor: pointer;
    border: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    font-weight: 500;
    font-size: 13px;
}

.smartseo-ai-tab-nav:hover {
    background-color: #f0f0f0;
}

.smartseo-ai-tab-nav.active {
    border-color: #ccc;
    border-bottom-color: #fff;
    background-color: #fff;
}

.smartseo-ai-tab-panel {
    display: none;
    padding: 15px 0;
}

.smartseo-ai-tab-panel.active {
    display: block;
}

/* Styles pour forcer l'affichage */
.smartseo-ai-classic-sidebar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.smartseo-ai-panel {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Styles pour les boutons */
.smartseo-ai-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.smartseo-ai-button-group button {
    flex: 1;
    min-width: 120px;
}

/* Styles pour les résultats */
.smartseo-ai-results-container {
    margin-top: 20px;
}

.smartseo-ai-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
}

.smartseo-ai-results h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
}

/* Styles pour les notifications */
.smartseo-ai-notification-container {
    margin-bottom: 10px;
}

.smartseo-ai-notification {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 3px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.smartseo-ai-notification-success {
    border-left: 4px solid #46b450;
}

.smartseo-ai-notification-error {
    border-left: 4px solid #dc3232;
}

.smartseo-ai-notification-info {
    border-left: 4px solid #00a0d2;
}

.smartseo-ai-notification-warning {
    border-left: 4px solid #ffb900;
}

.smartseo-ai-notification-icon {
    margin-right: 10px;
}

.smartseo-ai-notification-message {
    flex: 1;
    font-size: 13px;
}

.smartseo-ai-notification-close {
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    color: #666;
}

/* Styles pour les modales */
.smartseo-ai-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.smartseo-ai-modal {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    overflow: auto;
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.smartseo-ai-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border-bottom: 1px solid #ddd;
}

.smartseo-ai-modal-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.smartseo-ai-modal-close {
    cursor: pointer;
    font-size: 20px;
    line-height: 1;
    color: #666;
    background: none;
    border: none;
    padding: 0;
}

.smartseo-ai-modal-body {
    padding: 15px;
    max-height: 500px;
    overflow-y: auto;
}

.smartseo-ai-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px;
    border-top: 1px solid #ddd;
}
