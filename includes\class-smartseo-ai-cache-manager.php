<?php
/**
 * Gestionnaire de cache intelligent pour SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère le cache intelligent des réponses IA
 */
class SmartSEO_AI_Cache_Manager {

    /**
     * Instance singleton
     *
     * @var SmartSEO_AI_Cache_Manager
     */
    private static $instance = null;

    /**
     * Table de cache
     *
     * @var string
     */
    private $cache_table;

    /**
     * Paramètres de cache
     *
     * @var array
     */
    private $cache_settings;

    /**
     * Constructeur
     */
    private function __construct() {
        global $wpdb;
        $this->cache_table = $wpdb->prefix . 'smartseo_ai_cache';
        $this->cache_settings = get_option( 'smartseo_ai_cache_settings', array() );
        
        // Valeurs par défaut
        $this->cache_settings = wp_parse_args( $this->cache_settings, array(
            'enable_cache' => 'yes',
            'cache_ttl' => 86400,
            'max_cache_size' => 1000,
            'enable_fallback' => 'yes',
            'retry_attempts' => 3,
            'retry_delay' => 2,
        ) );

        // Planifier le nettoyage du cache
        if ( ! wp_next_scheduled( 'smartseo_ai_cleanup_cache' ) ) {
            wp_schedule_event( time(), 'hourly', 'smartseo_ai_cleanup_cache' );
        }

        add_action( 'smartseo_ai_cleanup_cache', array( $this, 'cleanup_expired_cache' ) );
    }

    /**
     * Récupère l'instance singleton
     *
     * @return SmartSEO_AI_Cache_Manager
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Génère une clé de cache basée sur le contenu et les paramètres
     *
     * @param string $content Contenu à analyser.
     * @param string $title Titre de l'article.
     * @param string $provider Fournisseur IA (openai/gemini).
     * @param array  $options Options supplémentaires.
     * @return string Clé de cache.
     */
    public function generate_cache_key( $content, $title, $provider, $options = array() ) {
        $data = array(
            'content' => $content,
            'title' => $title,
            'provider' => $provider,
            'options' => $options,
            'version' => SMARTSEO_AI_VERSION,
        );

        return hash( 'sha256', serialize( $data ) );
    }

    /**
     * Récupère une entrée du cache
     *
     * @param string $cache_key Clé de cache.
     * @return array|false Données mises en cache ou false si non trouvé.
     */
    public function get( $cache_key ) {
        if ( 'no' === $this->cache_settings['enable_cache'] ) {
            return false;
        }

        global $wpdb;

        $result = $wpdb->get_row( $wpdb->prepare(
            "SELECT cache_data, expires_at, hit_count FROM {$this->cache_table} 
             WHERE cache_key = %s AND expires_at > NOW()",
            $cache_key
        ) );

        if ( ! $result ) {
            return false;
        }

        // Incrémenter le compteur de hits
        $wpdb->query( $wpdb->prepare(
            "UPDATE {$this->cache_table} SET hit_count = hit_count + 1 WHERE cache_key = %s",
            $cache_key
        ) );

        $data = maybe_unserialize( $result->cache_data );
        
        // Log pour debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( "SmartSEO AI Cache: HIT pour la clé {$cache_key}" );
        }

        return $data;
    }

    /**
     * Stocke une entrée dans le cache
     *
     * @param string $cache_key Clé de cache.
     * @param mixed  $data Données à mettre en cache.
     * @param string $provider Fournisseur IA.
     * @param int    $ttl Durée de vie en secondes (optionnel).
     * @return bool Succès de l'opération.
     */
    public function set( $cache_key, $data, $provider, $ttl = null ) {
        if ( 'no' === $this->cache_settings['enable_cache'] ) {
            return false;
        }

        global $wpdb;

        if ( null === $ttl ) {
            $ttl = $this->cache_settings['cache_ttl'];
        }

        $expires_at = date( 'Y-m-d H:i:s', time() + $ttl );
        $serialized_data = maybe_serialize( $data );

        // Vérifier la taille du cache et nettoyer si nécessaire
        $this->maybe_cleanup_cache();

        $result = $wpdb->replace(
            $this->cache_table,
            array(
                'cache_key' => $cache_key,
                'cache_data' => $serialized_data,
                'provider' => $provider,
                'expires_at' => $expires_at,
                'hit_count' => 0,
            ),
            array( '%s', '%s', '%s', '%s', '%d' )
        );

        // Log pour debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( "SmartSEO AI Cache: SET pour la clé {$cache_key}, expire le {$expires_at}" );
        }

        return false !== $result;
    }

    /**
     * Supprime une entrée du cache
     *
     * @param string $cache_key Clé de cache.
     * @return bool Succès de l'opération.
     */
    public function delete( $cache_key ) {
        global $wpdb;

        $result = $wpdb->delete(
            $this->cache_table,
            array( 'cache_key' => $cache_key ),
            array( '%s' )
        );

        return false !== $result;
    }

    /**
     * Vide tout le cache
     *
     * @return bool Succès de l'opération.
     */
    public function flush() {
        global $wpdb;
        
        $result = $wpdb->query( "TRUNCATE TABLE {$this->cache_table}" );
        
        // Log pour debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( "SmartSEO AI Cache: FLUSH - Cache vidé complètement" );
        }

        return false !== $result;
    }

    /**
     * Nettoie le cache expiré
     *
     * @return int Nombre d'entrées supprimées.
     */
    public function cleanup_expired_cache() {
        global $wpdb;

        $deleted = $wpdb->query(
            "DELETE FROM {$this->cache_table} WHERE expires_at <= NOW()"
        );

        // Log pour debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG && $deleted > 0 ) {
            error_log( "SmartSEO AI Cache: CLEANUP - {$deleted} entrées expirées supprimées" );
        }

        return $deleted;
    }

    /**
     * Nettoie le cache si nécessaire (taille max atteinte)
     */
    private function maybe_cleanup_cache() {
        global $wpdb;

        $count = $wpdb->get_var( "SELECT COUNT(*) FROM {$this->cache_table}" );
        
        if ( $count >= $this->cache_settings['max_cache_size'] ) {
            // Supprimer les 20% d'entrées les moins utilisées
            $limit = ceil( $count * 0.2 );
            
            $wpdb->query( $wpdb->prepare(
                "DELETE FROM {$this->cache_table} 
                 ORDER BY hit_count ASC, created_at ASC 
                 LIMIT %d",
                $limit
            ) );

            // Log pour debug
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( "SmartSEO AI Cache: CLEANUP SIZE - {$limit} entrées supprimées (taille max atteinte)" );
            }
        }
    }

    /**
     * Récupère les statistiques du cache
     *
     * @return array Statistiques du cache.
     */
    public function get_stats() {
        global $wpdb;

        $stats = $wpdb->get_row(
            "SELECT 
                COUNT(*) as total_entries,
                SUM(hit_count) as total_hits,
                AVG(hit_count) as avg_hits,
                COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as active_entries,
                COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as expired_entries
             FROM {$this->cache_table}",
            ARRAY_A
        );

        $provider_stats = $wpdb->get_results(
            "SELECT provider, COUNT(*) as count, SUM(hit_count) as hits 
             FROM {$this->cache_table} 
             WHERE expires_at > NOW() 
             GROUP BY provider",
            ARRAY_A
        );

        return array(
            'general' => $stats ?: array(),
            'by_provider' => $provider_stats ?: array(),
            'settings' => $this->cache_settings,
        );
    }

    /**
     * Invalide le cache pour un article spécifique
     *
     * @param int $post_id ID de l'article.
     */
    public function invalidate_post_cache( $post_id ) {
        global $wpdb;

        // Supprimer toutes les entrées de cache qui pourraient être liées à cet article
        // Nous utilisons une approche simple : supprimer les entrées récentes qui pourraient correspondre
        $wpdb->query( $wpdb->prepare(
            "DELETE FROM {$this->cache_table} 
             WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)",
            $post_id
        ) );

        // Log pour debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( "SmartSEO AI Cache: INVALIDATE pour l'article {$post_id}" );
        }
    }
}
