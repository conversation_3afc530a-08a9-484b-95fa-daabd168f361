<?php
/**
 * Classe de gestion des métaboxes pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les métaboxes de l'Assistant de Rédaction SEO
 */
class SmartSEO_AI_Writing_Assistant_Metaboxes {

    /**
     * Initialise le gestionnaire des métaboxes
     */
    public function init() {
        // Ajouter les hooks
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
        add_action( 'save_post', array( $this, 'save_meta_boxes' ), 10, 2 );
    }

    /**
     * Ajoute les métaboxes
     */
    public function add_meta_boxes() {
        // Récupérer les types de publication
        $post_types = get_post_types( array( 'public' => true ) );

        // Ajouter la métabox pour chaque type de publication
        foreach ( $post_types as $post_type ) {
            add_meta_box(
                'smartseo-ai-writing-assistant',
                __( 'Assistant de Rédaction SEO', 'smartseo-ai' ),
                array( $this, 'render_meta_box' ),
                $post_type,
                'side',
                'high'
            );
        }
    }

    /**
     * Affiche la métabox
     *
     * @param WP_Post $post Objet post.
     */
    public function render_meta_box( $post ) {
        // Récupérer les valeurs existantes
        $focus_keyword = get_post_meta( $post->ID, 'smartseo_ai_focus_keyword', true );
        $meta_description = get_post_meta( $post->ID, 'smartseo_ai_meta_description', true );

        // Ajouter un nonce pour la sécurité
        wp_nonce_field( 'smartseo_ai_writing_assistant_meta_box', 'smartseo_ai_writing_assistant_meta_box_nonce' );

        // Inclure l'interface de l'Assistant de Rédaction SEO
        include_once SMARTSEO_AI_PLUGIN_DIR . 'admin/partials/smartseo-ai-classic-editor-sidebar.php';

        // Afficher les champs
        ?>
        <div class="smartseo-ai-meta-box">
            <div class="smartseo-ai-meta-box-field">
                <label for="smartseo_ai_focus_keyword"><?php esc_html_e( 'Mot-clé principal', 'smartseo-ai' ); ?></label>
                <input type="text" id="smartseo_ai_focus_keyword" name="smartseo_ai_focus_keyword" value="<?php echo esc_attr( $focus_keyword ); ?>" class="widefat">
            </div>

            <div class="smartseo-ai-meta-box-field">
                <label for="smartseo_ai_meta_description"><?php esc_html_e( 'Meta Description', 'smartseo-ai' ); ?></label>
                <textarea id="smartseo_ai_meta_description" name="smartseo_ai_meta_description" class="widefat" rows="3"><?php echo esc_textarea( $meta_description ); ?></textarea>
                <p class="description">
                    <span id="smartseo_ai_meta_description_counter"><?php echo strlen( $meta_description ); ?></span>/160
                </p>
            </div>

            <div class="smartseo-ai-meta-box-buttons">
                <button type="button" id="smartseo_ai_generate_meta_description" class="button button-secondary">
                    <?php esc_html_e( 'Générer une meta description', 'smartseo-ai' ); ?>
                </button>
            </div>

            <div id="smartseo_ai_meta_box_loading" class="smartseo-ai-meta-box-loading" style="display: none;">
                <span class="spinner is-active"></span>
                <p><?php esc_html_e( 'Génération en cours...', 'smartseo-ai' ); ?></p>
            </div>

            <div id="smartseo_ai_meta_box_message" class="smartseo-ai-meta-box-message" style="display: none;"></div>
        </div>

        <script>
            jQuery(document).ready(function($) {
                // Mettre à jour le compteur de caractères
                $('#smartseo_ai_meta_description').on('input', function() {
                    var length = $(this).val().length;
                    $('#smartseo_ai_meta_description_counter').text(length);

                    if (length > 160) {
                        $('#smartseo_ai_meta_description_counter').css('color', 'red');
                    } else {
                        $('#smartseo_ai_meta_description_counter').css('color', '');
                    }
                });

                // Générer une meta description
                $('#smartseo_ai_generate_meta_description').on('click', function() {
                    // Afficher le chargement
                    $('#smartseo_ai_meta_box_loading').show();
                    $('#smartseo_ai_meta_box_message').hide();

                    // Récupérer les données
                    var postId = <?php echo intval( $post->ID ); ?>;
                    var keyword = $('#smartseo_ai_focus_keyword').val();
                    var title = $('#title').val();
                    var content = '';

                    if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                        content = tinyMCE.get('content').getContent();
                    } else {
                        content = $('#content').val();
                    }

                    // Envoyer la requête AJAX
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'smartseo_ai_generate_meta_description',
                            nonce: '<?php echo wp_create_nonce( 'smartseo_ai_writing_assistant_nonce' ); ?>',
                            post_id: postId,
                            keyword: keyword,
                            title: title,
                            content: content
                        },
                        success: function(response) {
                            // Masquer le chargement
                            $('#smartseo_ai_meta_box_loading').hide();

                            if (response.success) {
                                // Mettre à jour la meta description
                                $('#smartseo_ai_meta_description').val(response.data.meta_description);
                                $('#smartseo_ai_meta_description').trigger('input');

                                // Afficher un message de succès
                                $('#smartseo_ai_meta_box_message').html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                                $('#smartseo_ai_meta_box_message').show();
                            } else {
                                // Afficher un message d'erreur
                                $('#smartseo_ai_meta_box_message').html('<div class="notice notice-error inline"><p>' + response.data.message + '</p></div>');
                                $('#smartseo_ai_meta_box_message').show();
                            }
                        },
                        error: function(xhr, status, error) {
                            // Masquer le chargement
                            $('#smartseo_ai_meta_box_loading').hide();

                            // Afficher un message d'erreur
                            $('#smartseo_ai_meta_box_message').html('<div class="notice notice-error inline"><p><?php esc_html_e( 'Une erreur est survenue', 'smartseo-ai' ); ?></p></div>');
                            $('#smartseo_ai_meta_box_message').show();
                        }
                    });
                });
            });
        </script>
        <?php
    }

    /**
     * Enregistre les données des métaboxes
     *
     * @param int     $post_id ID de l'article.
     * @param WP_Post $post    Objet post.
     */
    public function save_meta_boxes( $post_id, $post ) {
        // Vérifier le nonce
        if ( ! isset( $_POST['smartseo_ai_writing_assistant_meta_box_nonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['smartseo_ai_writing_assistant_meta_box_nonce'] ), 'smartseo_ai_writing_assistant_meta_box' ) ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Vérifier si c'est une sauvegarde automatique
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }

        // Vérifier si c'est une révision
        if ( wp_is_post_revision( $post_id ) ) {
            return;
        }

        // Enregistrer le mot-clé principal
        if ( isset( $_POST['smartseo_ai_focus_keyword'] ) ) {
            update_post_meta( $post_id, 'smartseo_ai_focus_keyword', sanitize_text_field( wp_unslash( $_POST['smartseo_ai_focus_keyword'] ) ) );
        }

        // Enregistrer la meta description
        if ( isset( $_POST['smartseo_ai_meta_description'] ) ) {
            update_post_meta( $post_id, 'smartseo_ai_meta_description', sanitize_text_field( wp_unslash( $_POST['smartseo_ai_meta_description'] ) ) );
        }
    }
}
