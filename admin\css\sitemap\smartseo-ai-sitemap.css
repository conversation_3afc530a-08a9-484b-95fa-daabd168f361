/**
 * Styles CSS pour l'interface du Sitemap XML
 *
 * @package SmartSEO_AI
 */

/* Carte des paramètres */
.smartseo-ai-settings-card {
    background-color: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    margin-bottom: 20px;
    padding: 15px;
}

.smartseo-ai-settings-card h3 {
    border-bottom: 1px solid #eee;
    font-size: 14px;
    margin: 0 0 15px;
    padding-bottom: 10px;
}

/* Champs de formulaire */
.smartseo-ai-settings-field {
    margin-bottom: 15px;
}

.smartseo-ai-settings-field label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
}

.smartseo-ai-settings-field-row {
    align-items: center;
    display: flex;
    justify-content: space-between;
}

.smartseo-ai-settings-field-row label {
    flex: 0 0 200px;
    margin-bottom: 0;
}

.smartseo-ai-settings-field p.description {
    color: #666;
    font-size: 12px;
    font-style: italic;
    margin: 5px 0 0;
}

/* Affichage d'URL */
.smartseo-ai-url-display {
    align-items: center;
    display: flex;
    gap: 10px;
}

.smartseo-ai-url-display input[readonly] {
    background-color: #f9f9f9;
    flex: 1;
}

/* Slider de priorité */
.smartseo-ai-range-slider {
    align-items: center;
    display: flex;
    flex: 1;
    gap: 10px;
}

.smartseo-ai-range-slider input[type="range"] {
    flex: 1;
}

.smartseo-ai-range-value {
    background-color: #f0f0f0;
    border-radius: 3px;
    display: inline-block;
    font-weight: 600;
    min-width: 30px;
    padding: 2px 5px;
    text-align: center;
}

/* Messages */
.smartseo-ai-validation-result,
.smartseo-ai-analysis-result {
    margin-top: 10px;
}

.smartseo-ai-validation-result .notice,
.smartseo-ai-analysis-result .notice {
    margin: 5px 0;
}

.smartseo-ai-validation-result .notice p,
.smartseo-ai-analysis-result .notice p {
    display: flex;
    align-items: center;
}

.smartseo-ai-validation-result .notice .dashicons,
.smartseo-ai-analysis-result .notice .dashicons {
    margin-right: 5px;
}

/* Suggestions */
.smartseo-ai-suggestions {
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    margin-top: 15px;
    padding: 15px;
}

.smartseo-ai-suggestions h4 {
    border-bottom: 1px solid #eee;
    font-size: 14px;
    margin: 0 0 10px;
    padding-bottom: 5px;
}

.smartseo-ai-suggestions-analysis {
    margin-bottom: 20px;
}

.smartseo-ai-suggestions-analysis-content {
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
}

.smartseo-ai-suggestions-missing-urls ul {
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
    padding-left: 20px;
}

.smartseo-ai-suggestions-missing-urls li {
    margin-bottom: 5px;
}

/* Boutons */
.smartseo-ai-settings-field .button {
    align-items: center;
    display: inline-flex;
}

.smartseo-ai-settings-field .button .dashicons {
    font-size: 16px;
    height: 16px;
    margin-right: 5px;
    width: 16px;
}
