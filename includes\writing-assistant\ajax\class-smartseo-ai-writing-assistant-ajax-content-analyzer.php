<?php
/**
 * Classe de gestion des AJAX d'analyse de contenu pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les AJAX d'analyse de contenu de l'Assistant de Rédaction SEO
 */
class SmartSEO_AI_Writing_Assistant_Ajax_Content_Analyzer {

    /**
     * Analyseur de contenu
     *
     * @var SmartSEO_AI_Content_Analyzer
     */
    private $content_analyzer;

    /**
     * Analyseur de densité de mots-clés
     *
     * @var SmartSEO_AI_Keyword_Density_Analyzer
     */
    private $keyword_density_analyzer;

    /**
     * Analyseur de structure
     *
     * @var SmartSEO_AI_Structure_Analyzer
     */
    private $structure_analyzer;

    /**
     * Analyseur de liens
     *
     * @var SmartSEO_AI_Link_Analyzer
     */
    private $link_analyzer;

    /**
     * Analyseur d'images
     *
     * @var SmartSEO_AI_Image_Analyzer
     */
    private $image_analyzer;

    /**
     * Constructeur
     */
    public function __construct() {
        // Initialiser les analyseurs
        $this->content_analyzer = new SmartSEO_AI_Content_Analyzer();
        $this->keyword_density_analyzer = new SmartSEO_AI_Keyword_Density_Analyzer();
        $this->structure_analyzer = new SmartSEO_AI_Structure_Analyzer();
        $this->link_analyzer = new SmartSEO_AI_Link_Analyzer();
        $this->image_analyzer = new SmartSEO_AI_Image_Analyzer();
    }

    /**
     * Analyse le contenu
     */
    public function analyze_content() {
        // Vérifier le nonce
        if ( ! check_ajax_referer( 'smartseo_ai_writing_assistant_nonce', 'nonce', false ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page.', 'smartseo-ai' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les données
        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $content = isset( $_POST['content'] ) ? wp_kses_post( wp_unslash( $_POST['content'] ) ) : '';
        $title = isset( $_POST['title'] ) ? sanitize_text_field( wp_unslash( $_POST['title'] ) ) : '';
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';

        // Récupérer la meta description
        $meta_description = get_post_meta( $post_id, 'smartseo_ai_meta_description', true );

        // Analyser le contenu
        $content_analysis = $this->content_analyzer->analyze( $content );
        
        // Analyser la densité de mots-clés
        $keyword_analysis = array();
        if ( ! empty( $keyword ) ) {
            $keyword_analysis = array(
                'density' => $this->keyword_density_analyzer->analyze_density( $content, $keyword ),
                'placement' => $this->keyword_density_analyzer->analyze_placement( $content, $title, $meta_description, $keyword ),
            );
        }
        
        // Analyser la structure
        $structure_analysis = $this->structure_analyzer->analyze( $content );
        
        // Analyser les liens
        $link_analysis = $this->link_analyzer->analyze( $content );
        
        // Analyser les images
        $image_analysis = $this->image_analyzer->analyze( $content );
        
        // Calculer le score SEO global
        $score = $this->calculate_score( $content_analysis, $keyword_analysis, $structure_analysis, $link_analysis, $image_analysis );
        
        // Générer la checklist d'optimisation
        $optimization_checklist = $this->generate_optimization_checklist( $content_analysis, $keyword_analysis, $structure_analysis, $link_analysis, $image_analysis );

        // Envoyer la réponse
        wp_send_json_success( array(
            'score' => $score,
            'content_analysis' => $content_analysis,
            'keyword_analysis' => $keyword_analysis,
            'structure_analysis' => $structure_analysis,
            'link_analysis' => $link_analysis,
            'image_analysis' => $image_analysis,
            'optimization_checklist' => $optimization_checklist,
        ) );
    }

    /**
     * Calcule le score SEO global
     *
     * @param array $content_analysis   Analyse du contenu.
     * @param array $keyword_analysis   Analyse des mots-clés.
     * @param array $structure_analysis Analyse de la structure.
     * @param array $link_analysis      Analyse des liens.
     * @param array $image_analysis     Analyse des images.
     * @return int Score SEO global.
     */
    private function calculate_score( $content_analysis, $keyword_analysis, $structure_analysis, $link_analysis, $image_analysis ) {
        $score = 0;
        $total_points = 0;
        
        // Points pour la longueur du contenu
        if ( isset( $content_analysis['content_length']['status'] ) ) {
            $total_points += 20;
            
            if ( $content_analysis['content_length']['status'] === 'good' ) {
                $score += 20;
            } elseif ( $content_analysis['content_length']['status'] === 'average' ) {
                $score += 10;
            }
        }
        
        // Points pour la lisibilité
        if ( isset( $content_analysis['readability']['status'] ) ) {
            $total_points += 15;
            
            if ( $content_analysis['readability']['status'] === 'good' ) {
                $score += 15;
            } elseif ( $content_analysis['readability']['status'] === 'average' ) {
                $score += 7;
            }
        }
        
        // Points pour la densité de mots-clés
        if ( isset( $keyword_analysis['density']['status'] ) ) {
            $total_points += 15;
            
            if ( $keyword_analysis['density']['status'] === 'good' ) {
                $score += 15;
            } elseif ( $keyword_analysis['density']['status'] === 'average' ) {
                $score += 7;
            }
        }
        
        // Points pour le placement des mots-clés
        if ( isset( $keyword_analysis['placement']['status'] ) ) {
            $total_points += 15;
            
            if ( $keyword_analysis['placement']['status'] === 'good' ) {
                $score += 15;
            } elseif ( $keyword_analysis['placement']['status'] === 'average' ) {
                $score += 7;
            }
        }
        
        // Points pour les titres
        if ( isset( $structure_analysis['headings']['status'] ) ) {
            $total_points += 10;
            
            if ( $structure_analysis['headings']['status'] === 'good' ) {
                $score += 10;
            } elseif ( $structure_analysis['headings']['status'] === 'average' ) {
                $score += 5;
            }
        }
        
        // Points pour les listes
        if ( isset( $structure_analysis['lists']['status'] ) ) {
            $total_points += 5;
            
            if ( $structure_analysis['lists']['status'] === 'good' ) {
                $score += 5;
            } elseif ( $structure_analysis['lists']['status'] === 'average' ) {
                $score += 2;
            }
        }
        
        // Points pour les liens internes
        if ( isset( $link_analysis['internal_links_analysis']['status'] ) ) {
            $total_points += 10;
            
            if ( $link_analysis['internal_links_analysis']['status'] === 'good' ) {
                $score += 10;
            } elseif ( $link_analysis['internal_links_analysis']['status'] === 'average' ) {
                $score += 5;
            }
        }
        
        // Points pour les liens externes
        if ( isset( $link_analysis['external_links_analysis']['status'] ) ) {
            $total_points += 5;
            
            if ( $link_analysis['external_links_analysis']['status'] === 'good' ) {
                $score += 5;
            } elseif ( $link_analysis['external_links_analysis']['status'] === 'average' ) {
                $score += 2;
            }
        }
        
        // Points pour les attributs alt des images
        if ( isset( $image_analysis['alt_analysis']['status'] ) ) {
            $total_points += 5;
            
            if ( $image_analysis['alt_analysis']['status'] === 'good' ) {
                $score += 5;
            } elseif ( $image_analysis['alt_analysis']['status'] === 'average' ) {
                $score += 2;
            }
        }
        
        // Calculer le score final
        $final_score = $total_points > 0 ? round( ( $score / $total_points ) * 100 ) : 0;
        
        return $final_score;
    }

    /**
     * Génère la checklist d'optimisation
     *
     * @param array $content_analysis   Analyse du contenu.
     * @param array $keyword_analysis   Analyse des mots-clés.
     * @param array $structure_analysis Analyse de la structure.
     * @param array $link_analysis      Analyse des liens.
     * @param array $image_analysis     Analyse des images.
     * @return array Checklist d'optimisation.
     */
    private function generate_optimization_checklist( $content_analysis, $keyword_analysis, $structure_analysis, $link_analysis, $image_analysis ) {
        $checklist = array();
        
        // Vérifier la longueur du contenu
        if ( isset( $content_analysis['content_length'] ) ) {
            $word_count = $content_analysis['content_length']['word_count'];
            $status = $content_analysis['content_length']['status'];
            
            $item = array(
                'label' => sprintf( __( 'Longueur du contenu : %d mots', 'smartseo-ai' ), $word_count ),
                'status' => $status,
            );
            
            if ( $status !== 'good' ) {
                if ( $word_count < 300 ) {
                    $item['recommendation'] = __( 'Votre contenu est trop court. Ajoutez plus de contenu pour atteindre au moins 300 mots.', 'smartseo-ai' );
                } elseif ( $word_count < 600 ) {
                    $item['recommendation'] = __( 'Votre contenu pourrait être plus détaillé. Visez au moins 600 mots pour un contenu plus complet.', 'smartseo-ai' );
                }
            }
            
            $checklist[] = $item;
        }
        
        // Vérifier la densité de mots-clés
        if ( isset( $keyword_analysis['density'] ) ) {
            $density = $keyword_analysis['density']['density'];
            $status = $keyword_analysis['density']['status'];
            
            $item = array(
                'label' => sprintf( __( 'Densité du mot-clé : %s%%', 'smartseo-ai' ), $density ),
                'status' => $status,
            );
            
            if ( $status !== 'good' ) {
                if ( $density < 0.5 ) {
                    $item['recommendation'] = __( 'La densité de votre mot-clé est trop faible. Utilisez votre mot-clé plus souvent dans le contenu.', 'smartseo-ai' );
                } elseif ( $density > 2.5 ) {
                    $item['recommendation'] = __( 'La densité de votre mot-clé est trop élevée. Réduisez l\'utilisation de votre mot-clé pour éviter le bourrage de mots-clés.', 'smartseo-ai' );
                }
            }
            
            $checklist[] = $item;
        }
        
        // Vérifier le placement des mots-clés
        if ( isset( $keyword_analysis['placement'] ) ) {
            $placement = $keyword_analysis['placement'];
            $status = $placement['status'];
            
            $item = array(
                'label' => __( 'Placement du mot-clé', 'smartseo-ai' ),
                'status' => $status,
            );
            
            if ( $status !== 'good' ) {
                $recommendations = array();
                
                if ( ! $placement['in_title'] ) {
                    $recommendations[] = __( 'Incluez votre mot-clé dans le titre.', 'smartseo-ai' );
                }
                
                if ( ! $placement['in_meta_description'] ) {
                    $recommendations[] = __( 'Incluez votre mot-clé dans la meta description.', 'smartseo-ai' );
                }
                
                if ( ! $placement['in_headings'] ) {
                    $recommendations[] = __( 'Incluez votre mot-clé dans au moins un titre (H2 ou H3).', 'smartseo-ai' );
                }
                
                if ( ! $placement['in_first_paragraph'] ) {
                    $recommendations[] = __( 'Incluez votre mot-clé dans le premier paragraphe.', 'smartseo-ai' );
                }
                
                $item['recommendation'] = implode( ' ', $recommendations );
            }
            
            $checklist[] = $item;
        }
        
        // Vérifier la structure des titres
        if ( isset( $structure_analysis['headings'] ) ) {
            $headings = $structure_analysis['headings']['headings'];
            $status = $structure_analysis['headings']['status'];
            
            $item = array(
                'label' => sprintf( __( 'Structure des titres : %d titres', 'smartseo-ai' ), count( $headings ) ),
                'status' => $status,
            );
            
            if ( $status !== 'good' ) {
                if ( count( $headings ) < 3 ) {
                    $item['recommendation'] = __( 'Ajoutez plus de titres (H2, H3) pour mieux structurer votre contenu. Visez au moins 3 titres.', 'smartseo-ai' );
                } elseif ( ! isset( $headings['h2'] ) || count( $headings['h2'] ) === 0 ) {
                    $item['recommendation'] = __( 'Ajoutez au moins un titre H2 pour structurer votre contenu.', 'smartseo-ai' );
                }
            }
            
            $checklist[] = $item;
        }
        
        // Vérifier les liens internes
        if ( isset( $link_analysis['internal_links_analysis'] ) ) {
            $internal_links = $link_analysis['internal_links'];
            $status = $link_analysis['internal_links_analysis']['status'];
            
            $item = array(
                'label' => sprintf( __( 'Liens internes : %d liens', 'smartseo-ai' ), $internal_links ),
                'status' => $status,
            );
            
            if ( $status !== 'good' ) {
                $item['recommendation'] = __( 'Ajoutez plus de liens internes pour améliorer la navigation et le référencement. Visez au moins 2-3 liens internes.', 'smartseo-ai' );
            }
            
            $checklist[] = $item;
        }
        
        // Vérifier les liens externes
        if ( isset( $link_analysis['external_links_analysis'] ) ) {
            $external_links = $link_analysis['external_links'];
            $status = $link_analysis['external_links_analysis']['status'];
            
            $item = array(
                'label' => sprintf( __( 'Liens externes : %d liens', 'smartseo-ai' ), $external_links ),
                'status' => $status,
            );
            
            if ( $status !== 'good' ) {
                $item['recommendation'] = __( 'Ajoutez quelques liens externes vers des sources fiables pour améliorer la crédibilité. Visez au moins 1-2 liens externes.', 'smartseo-ai' );
            }
            
            $checklist[] = $item;
        }
        
        // Vérifier les attributs alt des images
        if ( isset( $image_analysis['alt_analysis'] ) ) {
            $total_images = $image_analysis['total_images'];
            $missing_alt = $image_analysis['missing_alt'];
            $status = $image_analysis['alt_analysis']['status'];
            
            $item = array(
                'label' => sprintf( __( 'Attributs alt des images : %d/%d images avec alt', 'smartseo-ai' ), $total_images - $missing_alt, $total_images ),
                'status' => $status,
            );
            
            if ( $status !== 'good' && $total_images > 0 ) {
                $item['recommendation'] = __( 'Ajoutez des attributs alt descriptifs à toutes vos images pour améliorer l\'accessibilité et le référencement.', 'smartseo-ai' );
            }
            
            $checklist[] = $item;
        }
        
        // Vérifier la lisibilité
        if ( isset( $content_analysis['readability'] ) ) {
            $flesch_score = $content_analysis['readability']['flesch_score'];
            $level = $content_analysis['readability']['level'];
            $status = $content_analysis['readability']['status'];
            
            $item = array(
                'label' => sprintf( __( 'Lisibilité : %s (%s)', 'smartseo-ai' ), $flesch_score, $level ),
                'status' => $status,
            );
            
            if ( $status !== 'good' ) {
                $item['recommendation'] = __( 'Améliorez la lisibilité de votre contenu en utilisant des phrases plus courtes et un vocabulaire plus simple.', 'smartseo-ai' );
            }
            
            $checklist[] = $item;
        }
        
        return $checklist;
    }
}
