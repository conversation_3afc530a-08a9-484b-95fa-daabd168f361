<?php
/**
 * Gestionnaire de queue pour l'optimisation en masse SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère la queue d'optimisation en masse
 */
class SmartSEO_AI_Queue_Manager {

    /**
     * Instance singleton
     *
     * @var SmartSEO_AI_Queue_Manager
     */
    private static $instance = null;

    /**
     * Table de queue
     *
     * @var string
     */
    private $queue_table;

    /**
     * Paramètres de traitement en masse
     *
     * @var array
     */
    private $bulk_settings;

    /**
     * Gestionnaire de performance
     *
     * @var SmartSEO_AI_Performance_Manager
     */
    private $performance_manager;

    /**
     * Constructeur
     */
    private function __construct() {
        global $wpdb;
        $this->queue_table = $wpdb->prefix . 'smartseo_ai_queue';
        $this->bulk_settings = get_option( 'smartseo_ai_bulk_settings', array() );
        $this->performance_manager = SmartSEO_AI_Performance_Manager::get_instance();
        
        // Valeurs par défaut
        $this->bulk_settings = wp_parse_args( $this->bulk_settings, array(
            'batch_size' => 5,
            'delay_between_batches' => 3,
            'enable_resume' => 'yes',
            'max_concurrent_requests' => 3,
            'timeout_per_request' => 60,
        ) );

        // Planifier le traitement de la queue
        if ( ! wp_next_scheduled( 'smartseo_ai_process_queue' ) ) {
            wp_schedule_event( time(), 'smartseo_ai_queue_interval', 'smartseo_ai_process_queue' );
        }

        add_action( 'smartseo_ai_process_queue', array( $this, 'process_queue' ) );
        
        // Ajouter l'intervalle personnalisé
        add_filter( 'cron_schedules', array( $this, 'add_cron_interval' ) );
    }

    /**
     * Récupère l'instance singleton
     *
     * @return SmartSEO_AI_Queue_Manager
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Ajoute un intervalle personnalisé pour le cron
     *
     * @param array $schedules Intervalles existants.
     * @return array Intervalles modifiés.
     */
    public function add_cron_interval( $schedules ) {
        $schedules['smartseo_ai_queue_interval'] = array(
            'interval' => $this->bulk_settings['delay_between_batches'],
            'display' => __( 'SmartSEO AI Queue Processing', 'smartseo-ai' ),
        );
        return $schedules;
    }

    /**
     * Ajoute des articles à la queue d'optimisation
     *
     * @param array  $post_ids IDs des articles.
     * @param string $optimization_type Type d'optimisation.
     * @param int    $priority Priorité (1-10, 10 = haute priorité).
     * @param array  $metadata Métadonnées supplémentaires.
     * @return int Nombre d'articles ajoutés.
     */
    public function add_to_queue( $post_ids, $optimization_type = 'full_optimization', $priority = 5, $metadata = array() ) {
        global $wpdb;

        $added = 0;
        $current_time = current_time( 'mysql' );

        foreach ( $post_ids as $post_id ) {
            // Vérifier si l'article n'est pas déjà en queue
            $existing = $wpdb->get_var( $wpdb->prepare(
                "SELECT id FROM {$this->queue_table} 
                 WHERE post_id = %d AND optimization_type = %s AND status IN ('pending', 'processing')",
                $post_id,
                $optimization_type
            ) );

            if ( ! $existing ) {
                $result = $wpdb->insert(
                    $this->queue_table,
                    array(
                        'post_id' => $post_id,
                        'optimization_type' => $optimization_type,
                        'priority' => $priority,
                        'status' => 'pending',
                        'scheduled_at' => $current_time,
                        'metadata' => maybe_serialize( $metadata ),
                    ),
                    array( '%d', '%s', '%d', '%s', '%s', '%s' )
                );

                if ( $result ) {
                    $added++;
                }
            }
        }

        // Log pour debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( "SmartSEO AI Queue: {$added} articles ajoutés à la queue pour {$optimization_type}" );
        }

        return $added;
    }

    /**
     * Traite la queue d'optimisation
     */
    public function process_queue() {
        global $wpdb;

        // Vérifier si un traitement est déjà en cours
        $processing_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->queue_table} WHERE status = 'processing'"
        );

        if ( $processing_count >= $this->bulk_settings['max_concurrent_requests'] ) {
            return; // Trop de traitements en cours
        }

        // Récupérer le prochain lot à traiter
        $batch_size = $this->bulk_settings['batch_size'] - $processing_count;
        if ( $batch_size <= 0 ) {
            return;
        }

        $queue_items = $wpdb->get_results( $wpdb->prepare(
            "SELECT * FROM {$this->queue_table} 
             WHERE status = 'pending' AND attempts < max_attempts
             ORDER BY priority DESC, scheduled_at ASC 
             LIMIT %d",
            $batch_size
        ) );

        if ( empty( $queue_items ) ) {
            return; // Rien à traiter
        }

        // Log pour debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( "SmartSEO AI Queue: Traitement de " . count( $queue_items ) . " éléments" );
        }

        foreach ( $queue_items as $item ) {
            $this->process_queue_item( $item );
        }
    }

    /**
     * Traite un élément de la queue
     *
     * @param object $item Élément de la queue.
     */
    private function process_queue_item( $item ) {
        global $wpdb;

        // Marquer comme en cours de traitement
        $wpdb->update(
            $this->queue_table,
            array(
                'status' => 'processing',
                'started_at' => current_time( 'mysql' ),
                'attempts' => $item->attempts + 1,
            ),
            array( 'id' => $item->id ),
            array( '%s', '%s', '%d' ),
            array( '%d' )
        );

        try {
            $post = get_post( $item->post_id );
            if ( ! $post ) {
                throw new Exception( 'Article introuvable' );
            }

            $metadata = maybe_unserialize( $item->metadata );
            $result = null;

            // Traitement selon le type d'optimisation
            switch ( $item->optimization_type ) {
                case 'full_optimization':
                    $result = $this->performance_manager->optimize_content_with_cache(
                        $item->post_id,
                        $post->post_content,
                        $post->post_title,
                        $metadata
                    );
                    break;

                case 'meta_description':
                    $result = $this->optimize_meta_description( $item->post_id, $post, $metadata );
                    break;

                case 'keywords':
                    $result = $this->optimize_keywords( $item->post_id, $post, $metadata );
                    break;

                case 'title':
                    $result = $this->optimize_title( $item->post_id, $post, $metadata );
                    break;

                default:
                    throw new Exception( 'Type d\'optimisation non supporté: ' . $item->optimization_type );
            }

            if ( is_wp_error( $result ) ) {
                throw new Exception( $result->get_error_message() );
            }

            // Succès - marquer comme terminé
            $wpdb->update(
                $this->queue_table,
                array(
                    'status' => 'completed',
                    'completed_at' => current_time( 'mysql' ),
                ),
                array( 'id' => $item->id ),
                array( '%s', '%s' ),
                array( '%d' )
            );

            // Log pour debug
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( "SmartSEO AI Queue: Succès pour l'article {$item->post_id} ({$item->optimization_type})" );
            }

        } catch ( Exception $e ) {
            // Échec - vérifier si on doit réessayer
            if ( $item->attempts >= $item->max_attempts ) {
                // Trop de tentatives - marquer comme échoué
                $wpdb->update(
                    $this->queue_table,
                    array(
                        'status' => 'failed',
                        'error_message' => $e->getMessage(),
                        'completed_at' => current_time( 'mysql' ),
                    ),
                    array( 'id' => $item->id ),
                    array( '%s', '%s', '%s' ),
                    array( '%d' )
                );
            } else {
                // Remettre en attente pour une nouvelle tentative
                $wpdb->update(
                    $this->queue_table,
                    array(
                        'status' => 'pending',
                        'error_message' => $e->getMessage(),
                        'scheduled_at' => date( 'Y-m-d H:i:s', time() + ( $item->attempts * 60 ) ), // Délai croissant
                    ),
                    array( 'id' => $item->id ),
                    array( '%s', '%s', '%s' ),
                    array( '%d' )
                );
            }

            // Log pour debug
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( "SmartSEO AI Queue: Échec pour l'article {$item->post_id} - " . $e->getMessage() );
            }
        }
    }

    /**
     * Optimise la méta description d'un article
     *
     * @param int    $post_id ID de l'article.
     * @param WP_Post $post Article.
     * @param array  $metadata Métadonnées.
     * @return array|WP_Error Résultat de l'optimisation.
     */
    private function optimize_meta_description( $post_id, $post, $metadata ) {
        $provider = get_option( 'smartseo_ai_provider', 'openai' );
        
        if ( 'openai' === $provider ) {
            $api = new SmartSEO_AI_API();
        } else {
            $api = new SmartSEO_AI_Gemini();
        }

        // Générer une méta description optimisée
        $prompt = "Génère une méta description SEO optimisée pour cet article :\n\n";
        $prompt .= "Titre : " . $post->post_title . "\n";
        $prompt .= "Contenu : " . wp_strip_all_tags( $post->post_content ) . "\n\n";
        $prompt .= "La méta description doit faire entre 120 et 160 caractères et être engageante.";

        $result = $api->generate_content( $prompt );
        
        if ( ! is_wp_error( $result ) ) {
            update_post_meta( $post_id, 'smartseo_ai_meta_description', sanitize_text_field( $result ) );
        }

        return $result;
    }

    /**
     * Optimise les mots-clés d'un article
     *
     * @param int    $post_id ID de l'article.
     * @param WP_Post $post Article.
     * @param array  $metadata Métadonnées.
     * @return array|WP_Error Résultat de l'optimisation.
     */
    private function optimize_keywords( $post_id, $post, $metadata ) {
        $provider = get_option( 'smartseo_ai_provider', 'openai' );
        
        if ( 'openai' === $provider ) {
            $api = new SmartSEO_AI_API();
        } else {
            $api = new SmartSEO_AI_Gemini();
        }

        // Générer des mots-clés optimisés
        $prompt = "Génère 5-8 mots-clés SEO pertinents pour cet article :\n\n";
        $prompt .= "Titre : " . $post->post_title . "\n";
        $prompt .= "Contenu : " . wp_strip_all_tags( $post->post_content ) . "\n\n";
        $prompt .= "Retourne les mots-clés séparés par des virgules.";

        $result = $api->generate_content( $prompt );
        
        if ( ! is_wp_error( $result ) ) {
            update_post_meta( $post_id, 'smartseo_ai_keywords', sanitize_text_field( $result ) );
        }

        return $result;
    }

    /**
     * Optimise le titre d'un article
     *
     * @param int    $post_id ID de l'article.
     * @param WP_Post $post Article.
     * @param array  $metadata Métadonnées.
     * @return array|WP_Error Résultat de l'optimisation.
     */
    private function optimize_title( $post_id, $post, $metadata ) {
        $provider = get_option( 'smartseo_ai_provider', 'openai' );
        
        if ( 'openai' === $provider ) {
            $api = new SmartSEO_AI_API();
        } else {
            $api = new SmartSEO_AI_Gemini();
        }

        // Générer un titre SEO optimisé
        $prompt = "Génère un titre SEO optimisé pour cet article :\n\n";
        $prompt .= "Titre actuel : " . $post->post_title . "\n";
        $prompt .= "Contenu : " . wp_strip_all_tags( substr( $post->post_content, 0, 500 ) ) . "...\n\n";
        $prompt .= "Le titre doit faire entre 40 et 60 caractères et être accrocheur.";

        $result = $api->generate_content( $prompt );
        
        if ( ! is_wp_error( $result ) ) {
            update_post_meta( $post_id, 'smartseo_ai_seo_title', sanitize_text_field( $result ) );
        }

        return $result;
    }

    /**
     * Récupère le statut de la queue
     *
     * @return array Statut de la queue.
     */
    public function get_queue_status() {
        global $wpdb;

        $stats = $wpdb->get_row(
            "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
             FROM {$this->queue_table}",
            ARRAY_A
        );

        return $stats ?: array();
    }

    /**
     * Vide la queue
     *
     * @param string $status Statut à vider (optionnel).
     * @return int Nombre d'éléments supprimés.
     */
    public function clear_queue( $status = null ) {
        global $wpdb;

        if ( $status ) {
            $deleted = $wpdb->delete(
                $this->queue_table,
                array( 'status' => $status ),
                array( '%s' )
            );
        } else {
            $deleted = $wpdb->query( "TRUNCATE TABLE {$this->queue_table}" );
        }

        return $deleted;
    }
}
