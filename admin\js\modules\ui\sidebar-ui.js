/**
 * Module de gestion de la barre latérale
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    // Déclaration des sous-modules
    let GutenbergSidebar;
    let ClassicEditorSidebar;

    /**
     * Gestionnaire de la barre latérale
     */
    const SidebarUI = {
        /**
         * Initialise le gestionnaire de la barre latérale
         */
        init: function() {
            console.log('SidebarUI: Initialisation du gestionnaire de la barre latérale');

            // Initialiser les sous-modules
            try {
                // Récupérer les sous-modules
                GutenbergSidebar = window.SmartSEOAI.GutenbergSidebar;
                ClassicEditorSidebar = window.SmartSEOAI.ClassicEditorSidebar;

                if (GutenbergSidebar) {
                    console.log('SidebarUI: Initialisation de GutenbergSidebar');
                    GutenbergSidebar.init();
                } else {
                    console.error('SidebarUI: GutenbergSidebar n\'est pas disponible');
                }

                if (ClassicEditorSidebar) {
                    console.log('SidebarUI: Initialisation de ClassicEditorSidebar');
                    ClassicEditorSidebar.init();
                } else {
                    console.error('SidebarUI: ClassicEditorSidebar n\'est pas disponible');
                }
            } catch (error) {
                console.error('SidebarUI: Erreur lors de l\'initialisation des sous-modules', error);
            }
        },

        /**
         * Ajoute la barre latérale à l'éditeur Gutenberg
         */
        addGutenbergSidebar: function() {
            console.log('SidebarUI: Ajout de la barre latérale Gutenberg');

            try {
                // Récupérer le sous-module si nécessaire
                if (!GutenbergSidebar) {
                    GutenbergSidebar = window.SmartSEOAI.GutenbergSidebar;
                }

                if (GutenbergSidebar && GutenbergSidebar.addSidebar) {
                    GutenbergSidebar.addSidebar();
                    console.log('SidebarUI: Barre latérale Gutenberg ajoutée avec succès');
                } else {
                    console.error('SidebarUI: Impossible d\'ajouter la barre latérale Gutenberg');
                }
            } catch (error) {
                console.error('SidebarUI: Erreur lors de l\'ajout de la barre latérale Gutenberg', error);
            }
        },

        /**
         * Ajoute la barre latérale à l'éditeur classique
         */
        addClassicEditorSidebar: function() {
            console.log('SidebarUI: Ajout de la barre latérale de l\'éditeur classique');

            try {
                // Récupérer le sous-module si nécessaire
                if (!ClassicEditorSidebar) {
                    ClassicEditorSidebar = window.SmartSEOAI.ClassicEditorSidebar;
                }

                if (ClassicEditorSidebar && ClassicEditorSidebar.addSidebar) {
                    ClassicEditorSidebar.addSidebar();
                    console.log('SidebarUI: Barre latérale de l\'éditeur classique ajoutée avec succès');
                } else {
                    console.error('SidebarUI: Impossible d\'ajouter la barre latérale de l\'éditeur classique');

                    // Créer une barre latérale de secours
                    this.createFallbackSidebar();
                }
            } catch (error) {
                console.error('SidebarUI: Erreur lors de l\'ajout de la barre latérale de l\'éditeur classique', error);

                // Créer une barre latérale de secours
                this.createFallbackSidebar();
            }
        },

        /**
         * Crée une barre latérale de secours pour l'éditeur classique
         */
        createFallbackSidebar: function() {
            console.log('SidebarUI: Création d\'une barre latérale de secours');

            // Vérifier si nous sommes dans l'éditeur classique
            if ($('#post').length === 0) {
                console.error('SidebarUI: Impossible de créer une barre latérale de secours (pas dans l\'éditeur classique)');
                return;
            }

            // Créer la barre latérale
            const $sidebar = $('<div id="smartseo-ai-classic-sidebar" class="smartseo-ai-classic-sidebar"></div>');

            // Ajouter l'en-tête
            $sidebar.append('<div class="smartseo-ai-classic-sidebar-header"><h2>Assistant de Rédaction SEO</h2></div>');

            // Ajouter le contenu
            const $content = $('<div class="smartseo-ai-classic-sidebar-content"></div>');

            // Ajouter les onglets
            $content.append('<div class="smartseo-ai-tabs">' +
                '<ul class="smartseo-ai-tabs-nav">' +
                '<li class="smartseo-ai-tab-nav active" data-tab="content">Contenu</li>' +
                '<li class="smartseo-ai-tab-nav" data-tab="keywords">Mots-clés</li>' +
                '<li class="smartseo-ai-tab-nav" data-tab="analysis">Analyse</li>' +
                '<li class="smartseo-ai-tab-nav" data-tab="trends">Tendances</li>' +
                '</ul>' +
                '<div class="smartseo-ai-tab-panel active" data-tab="content">' +
                '<div class="smartseo-ai-panel-header">' +
                '<h3>Génération de contenu</h3>' +
                '<p>Générez du contenu optimisé pour le SEO.</p>' +
                '</div>' +
                '<div class="smartseo-ai-panel-content">' +
                '<div class="smartseo-ai-form-group">' +
                '<label for="smartseo-ai-focus-keyword">Mot-clé principal</label>' +
                '<input type="text" id="smartseo-ai-focus-keyword" class="widefat" placeholder="Entrez votre mot-clé principal">' +
                '</div>' +
                '<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-generate-title">Générer un titre</button>' +
                '<button type="button" class="button button-primary smartseo-ai-generate-meta">Générer une meta description</button>' +
                '</div>' +
                '<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-generate-intro">Générer une introduction</button>' +
                '<button type="button" class="button button-primary smartseo-ai-generate-conclusion">Générer une conclusion</button>' +
                '</div>' +
                '<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-generate-headings">Générer des titres H2</button>' +
                '<button type="button" class="button button-primary smartseo-ai-generate-paragraph">Générer un paragraphe</button>' +
                '</div>' +
                '<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-generate-article">Générer un article complet</button>' +
                '</div>' +
                '</div>' +
                '<div id="smartseo-ai-content-results" class="smartseo-ai-results-container">' +
                '<div class="smartseo-ai-loading" style="display: none;">' +
                '<span class="spinner is-active"></span>' +
                '<p>Génération en cours...</p>' +
                '</div>' +
                '<div class="smartseo-ai-results" style="display: none;">' +
                '<h4>Résultats de la génération</h4>' +
                '<div class="smartseo-ai-results-content"></div>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<div class="smartseo-ai-tab-panel" data-tab="keywords">' +
                '<div class="smartseo-ai-panel-header">' +
                '<h3>Analyse de mots-clés</h3>' +
                '<p>Analysez et trouvez les meilleurs mots-clés pour votre contenu.</p>' +
                '</div>' +
                '<div class="smartseo-ai-panel-content">' +
                '<div class="smartseo-ai-form-group">' +
                '<label for="smartseo-ai-focus-keyword-analysis">Mot-clé principal</label>' +
                '<input type="text" id="smartseo-ai-focus-keyword-analysis" class="widefat" placeholder="Entrez votre mot-clé principal">' +
                '</div>' +
                '<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-analyze-keywords">Analyser les mots-clés</button>' +
                '</div>' +
                '</div>' +
                '<div id="smartseo-ai-keyword-results" class="smartseo-ai-results-container">' +
                '<div class="smartseo-ai-loading" style="display: none;">' +
                '<span class="spinner is-active"></span>' +
                '<p>Analyse en cours...</p>' +
                '</div>' +
                '<div class="smartseo-ai-results" style="display: none;">' +
                '<h4>Résultats de l\'analyse</h4>' +
                '<div class="smartseo-ai-results-content"></div>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<div class="smartseo-ai-tab-panel" data-tab="analysis">' +
                '<div class="smartseo-ai-panel-header">' +
                '<h3>Analyse SEO en temps réel</h3>' +
                '<p>Analysez votre contenu en temps réel pour optimiser le SEO.</p>' +
                '</div>' +
                '<div class="smartseo-ai-panel-content">' +
                '<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-analyze-now">Analyser maintenant</button>' +
                '<button type="button" class="button button-primary smartseo-ai-optimize-all">Optimiser automatiquement</button>' +
                '</div>' +
                '<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button smartseo-ai-check-plagiarism">Vérifier la non-duplication</button>' +
                '</div>' +
                '</div>' +
                '<div id="smartseo-ai-analysis-results" class="smartseo-ai-results-container">' +
                '<div class="smartseo-ai-loading" style="display: none;">' +
                '<span class="spinner is-active"></span>' +
                '<p>Analyse en cours...</p>' +
                '</div>' +
                '<div class="smartseo-ai-results" style="display: none;">' +
                '<h4>Résultats de l\'analyse</h4>' +
                '<div class="smartseo-ai-results-content"></div>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<div class="smartseo-ai-tab-panel" data-tab="trends">' +
                '<div class="smartseo-ai-panel-header">' +
                '<h3>Tendances et sujets populaires</h3>' +
                '<p>Découvrez les sujets tendances liés à votre mot-clé.</p>' +
                '</div>' +
                '<div class="smartseo-ai-panel-content">' +
                '<div class="smartseo-ai-form-group">' +
                '<label for="smartseo-ai-trends-keyword">Mot-clé pour les tendances</label>' +
                '<input type="text" id="smartseo-ai-trends-keyword" class="widefat" placeholder="Entrez un mot-clé">' +
                '</div>' +
                '<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-get-trends">Obtenir les tendances</button>' +
                '</div>' +
                '</div>' +
                '<div id="smartseo-ai-trends-results" class="smartseo-ai-results-container">' +
                '<div class="smartseo-ai-loading" style="display: none;">' +
                '<span class="spinner is-active"></span>' +
                '<p>Recherche des tendances...</p>' +
                '</div>' +
                '<div class="smartseo-ai-results" style="display: none;">' +
                '<h4>Sujets tendances</h4>' +
                '<div class="smartseo-ai-results-content"></div>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>');

            // Ajouter le contenu à la barre latérale
            $sidebar.append($content);

            // Ajouter la barre latérale après le formulaire de publication
            $('#submitdiv').after($sidebar);

            // Ajouter les écouteurs d'événements pour les onglets
            $('.smartseo-ai-tab-nav').on('click', function() {
                const tab = $(this).data('tab');

                // Activer l'onglet
                $('.smartseo-ai-tab-nav').removeClass('active');
                $(this).addClass('active');

                // Afficher le panneau correspondant
                $('.smartseo-ai-tab-panel').removeClass('active');
                $('.smartseo-ai-tab-panel[data-tab="' + tab + '"]').addClass('active');
            });

            console.log('SidebarUI: Barre latérale de secours créée avec succès');
        },

        /**
         * Affiche les résultats de génération de contenu dans Gutenberg
         * @param {Object} results Résultats de génération
         * @param {string} type    Type de contenu
         */
        showGutenbergContentResults: function(results, type) {
            GutenbergSidebar.showContentResults(results, type);
        },

        /**
         * Affiche les résultats de génération de contenu dans l'éditeur classique
         * @param {Object} results Résultats de génération
         * @param {string} type    Type de contenu
         */
        showClassicEditorContentResults: function(results, type) {
            ClassicEditorSidebar.showContentResults(results, type);
        },

        /**
         * Affiche les résultats d'analyse des mots-clés dans Gutenberg
         * @param {Object} results Résultats d'analyse
         */
        showGutenbergKeywordResults: function(results) {
            GutenbergSidebar.showKeywordResults(results);
        },

        /**
         * Affiche les résultats d'analyse des mots-clés dans l'éditeur classique
         * @param {Object} results Résultats d'analyse
         */
        showClassicEditorKeywordResults: function(results) {
            ClassicEditorSidebar.showKeywordResults(results);
        },

        /**
         * Affiche les résultats d'analyse en temps réel dans Gutenberg
         * @param {Object} results Résultats d'analyse
         */
        showGutenbergLiveAnalysisResults: function(results) {
            GutenbergSidebar.showLiveAnalysisResults(results);
        },

        /**
         * Affiche les résultats d'analyse en temps réel dans l'éditeur classique
         * @param {Object} results Résultats d'analyse
         */
        showClassicEditorLiveAnalysisResults: function(results) {
            ClassicEditorSidebar.showLiveAnalysisResults(results);
        },

        /**
         * Affiche les résultats de tendances dans Gutenberg
         * @param {Object} results Résultats de tendances
         */
        showGutenbergTrendsResults: function(results) {
            GutenbergSidebar.showTrendsResults(results);
        },

        /**
         * Affiche les résultats de tendances dans l'éditeur classique
         * @param {Object} results Résultats de tendances
         */
        showClassicEditorTrendsResults: function(results) {
            ClassicEditorSidebar.showTrendsResults(results);
        },

        /**
         * Applique une suggestion dans Gutenberg
         * @param {string} content Contenu à appliquer
         * @param {string} target  Cible de l'application
         */
        applyGutenbergSuggestion: function(content, target) {
            GutenbergSidebar.applySuggestion(content, target);
        },

        /**
         * Applique une suggestion dans l'éditeur classique
         * @param {string} content Contenu à appliquer
         * @param {string} target  Cible de l'application
         */
        applyClassicEditorSuggestion: function(content, target) {
            ClassicEditorSidebar.applySuggestion(content, target);
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.SidebarUI = SidebarUI;

})(jQuery);
