<?php
/**
 * Vue des paramètres
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e( 'SmartSEO AI - Paramètres', 'smartseo-ai' ); ?></h1>

    <div class="smartseo-ai-container">
        <div class="smartseo-ai-header">
            <h1><?php _e( 'Paramètres', 'smartseo-ai' ); ?></h1>
            <span class="smartseo-ai-version">v<?php echo SMARTSEO_AI_VERSION; ?></span>
        </div>

        <div class="smartseo-ai-content">
            <?php
            // Afficher les messages d'erreur ou de succès
            settings_errors( 'smartseo-ai-settings' );

            // Vérifier si la clé API est définie
            $api_key = get_option( 'smartseo_ai_openai_api_key', '' );
            if ( empty( $api_key ) ) {
                ?>
                <div class="smartseo-ai-notice smartseo-ai-notice-warning">
                    <span class="dashicons dashicons-warning"></span>
                    <div>
                        <?php _e( 'Veuillez configurer votre clé API OpenAI pour utiliser toutes les fonctionnalités du plugin.', 'smartseo-ai' ); ?>
                    </div>
                </div>
                <?php
            }

            // Récupérer les onglets
            $tabs = apply_filters( 'smartseo_ai_settings_tabs', array(
                'general' => array(
                    'title' => __( 'Général', 'smartseo-ai' ),
                    'icon'  => 'dashicons-admin-generic',
                ),
            ) );

            // Déterminer l'onglet actif
            $active_tab = isset( $_GET['tab'] ) ? sanitize_key( $_GET['tab'] ) : 'general';
            if ( ! isset( $tabs[ $active_tab ] ) ) {
                $active_tab = 'general';
            }
            ?>

            <div class="smartseo-ai-tabs">
                <ul class="smartseo-ai-tabs-nav">
                    <?php foreach ( $tabs as $tab_id => $tab ) : ?>
                        <li class="smartseo-ai-tab-nav <?php echo $active_tab === $tab_id ? 'active' : ''; ?>">
                            <a href="<?php echo esc_url( add_query_arg( 'tab', $tab_id ) ); ?>">
                                <span class="dashicons <?php echo esc_attr( $tab['icon'] ); ?>"></span>
                                <?php echo esc_html( $tab['title'] ); ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>

                <div class="smartseo-ai-tab-content">
                    <?php if ( $active_tab === 'general' ) : ?>
                        <form method="post" action="options.php" class="smartseo-ai-settings-form">
                            <?php
                            // Afficher les champs cachés
                            settings_fields( 'smartseo-ai-settings' );

                            // Afficher les sections de paramètres
                            do_settings_sections( 'smartseo-ai-settings' );

                            // Afficher le bouton de soumission
                            submit_button();
                            ?>
                        </form>
                    <?php else : ?>
                        <?php do_action( 'smartseo_ai_settings_content_' . $active_tab ); ?>
                    <?php endif; ?>
                </div>
            </div>

            <div id="openai-info" class="smartseo-ai-notice smartseo-ai-notice-info">
                <span class="dashicons dashicons-info"></span>
                <div>
                    <p><strong><?php _e( 'Comment obtenir une clé API OpenAI ?', 'smartseo-ai' ); ?></strong></p>
                    <ol>
                        <li><?php _e( 'Créez un compte sur <a href="https://platform.openai.com/signup" target="_blank">OpenAI</a>.', 'smartseo-ai' ); ?></li>
                        <li><?php _e( 'Accédez à la section <a href="https://platform.openai.com/api-keys" target="_blank">API Keys</a>.', 'smartseo-ai' ); ?></li>
                        <li><?php _e( 'Cliquez sur "Create new secret key" et donnez-lui un nom.', 'smartseo-ai' ); ?></li>
                        <li><?php _e( 'Copiez la clé générée et collez-la dans le champ ci-dessus.', 'smartseo-ai' ); ?></li>
                    </ol>
                    <p><?php _e( 'Note : L\'utilisation de l\'API OpenAI est payante. Consultez leur <a href="https://openai.com/pricing" target="_blank">grille tarifaire</a> pour plus d\'informations.', 'smartseo-ai' ); ?></p>
                </div>
            </div>

            <div id="gemini-info" class="smartseo-ai-notice smartseo-ai-notice-info">
                <span class="dashicons dashicons-info"></span>
                <div>
                    <p><strong><?php _e( 'Comment obtenir une clé API Google Gemini ?', 'smartseo-ai' ); ?></strong></p>
                    <ol>
                        <li><?php _e( 'Accédez à <a href="https://aistudio.google.com/" target="_blank">Google AI Studio</a> et connectez-vous avec votre compte Google.', 'smartseo-ai' ); ?></li>
                        <li><?php _e( 'Cliquez sur votre profil en haut à droite, puis sur "Get API key".', 'smartseo-ai' ); ?></li>
                        <li><?php _e( 'Créez une nouvelle clé API en cliquant sur "Create API key".', 'smartseo-ai' ); ?></li>
                        <li><?php _e( 'Copiez la clé générée et collez-la dans le champ ci-dessus.', 'smartseo-ai' ); ?></li>
                    </ol>
                    <p><?php _e( 'Note : Google Gemini offre un quota gratuit généreux. Consultez leur <a href="https://ai.google.dev/pricing" target="_blank">grille tarifaire</a> pour plus d\'informations.', 'smartseo-ai' ); ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Script pour afficher/masquer les clés API et les informations
    (function($) {
        $(document).ready(function() {
            // Ajouter un bouton pour afficher/masquer les clés API
            $('#smartseo_ai_openai_api_key').after('<button type="button" class="smartseo-ai-toggle-api-key button"><span class="dashicons dashicons-visibility"></span></button>');
            $('#smartseo_ai_gemini_api_key').after('<button type="button" class="smartseo-ai-toggle-api-key button"><span class="dashicons dashicons-visibility"></span></button>');

            // Gérer le clic sur les boutons
            $('.smartseo-ai-toggle-api-key').on('click', function(e) {
                e.preventDefault();

                const apiKeyField = $(this).prev('input');
                const icon = $(this).find('.dashicons');

                if (apiKeyField.attr('type') === 'password') {
                    apiKeyField.attr('type', 'text');
                    icon.removeClass('dashicons-visibility').addClass('dashicons-hidden');
                } else {
                    apiKeyField.attr('type', 'password');
                    icon.removeClass('dashicons-hidden').addClass('dashicons-visibility');
                }
            });

            // Afficher/masquer les informations en fonction du fournisseur sélectionné
            function toggleProviderInfo() {
                var provider = $('#smartseo_ai_provider').val();
                if (provider === 'openai') {
                    $('#openai-info').show();
                    $('#gemini-info').hide();
                } else {
                    $('#openai-info').hide();
                    $('#gemini-info').show();
                }
            }

            toggleProviderInfo();
            $('#smartseo_ai_provider').on('change', toggleProviderInfo);
        });
    })(jQuery);
</script>
