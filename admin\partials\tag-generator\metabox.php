<?php
/**
 * Template pour la métabox du Générateur de Tags IA
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="smartseo-ai-tag-generator-metabox">
    <div class="smartseo-ai-tag-generator-options">
        <div class="smartseo-ai-tag-generator-option">
            <label for="smartseo-ai-tag-generator-include-brands">
                <input type="checkbox" id="smartseo-ai-tag-generator-include-brands" checked>
                <?php _e( 'Inclure les marques et produits', 'smartseo-ai' ); ?>
            </label>
        </div>
        
        <div class="smartseo-ai-tag-generator-option">
            <label for="smartseo-ai-tag-generator-include-regions">
                <input type="checkbox" id="smartseo-ai-tag-generator-include-regions" checked>
                <?php _e( 'Inclure les régions et lieux', 'smartseo-ai' ); ?>
            </label>
        </div>
        
        <div class="smartseo-ai-tag-generator-option">
            <label for="smartseo-ai-tag-generator-use-external-api">
                <input type="checkbox" id="smartseo-ai-tag-generator-use-external-api">
                <?php _e( 'Utiliser une API externe', 'smartseo-ai' ); ?>
            </label>
            
            <select id="smartseo-ai-tag-generator-external-api" disabled>
                <option value=""><?php _e( 'Aucune', 'smartseo-ai' ); ?></option>
                <option value="google_trends"><?php _e( 'Google Trends', 'smartseo-ai' ); ?></option>
                <option value="semrush"><?php _e( 'Semrush', 'smartseo-ai' ); ?></option>
            </select>
        </div>
    </div>
    
    <div class="smartseo-ai-tag-generator-actions">
        <button type="button" id="smartseo-ai-tag-generator-generate" class="button button-primary">
            <?php _e( 'Générer les Tags', 'smartseo-ai' ); ?>
        </button>
    </div>
    
    <div class="smartseo-ai-tag-generator-loading" style="display: none;">
        <span class="spinner is-active"></span>
        <span class="smartseo-ai-tag-generator-loading-text">
            <?php _e( 'Génération en cours...', 'smartseo-ai' ); ?>
        </span>
    </div>
    
    <div class="smartseo-ai-tag-generator-results" style="display: none;">
        <div class="smartseo-ai-tag-generator-existing-tags">
            <h4><?php _e( 'Tags existants', 'smartseo-ai' ); ?></h4>
            
            <div class="smartseo-ai-tag-generator-existing-tags-list">
                <?php if ( ! empty( $existing_tags ) ) : ?>
                    <?php foreach ( $existing_tags as $tag ) : ?>
                        <span class="smartseo-ai-tag-generator-tag"><?php echo esc_html( $tag ); ?></span>
                    <?php endforeach; ?>
                <?php else : ?>
                    <p class="smartseo-ai-tag-generator-no-tags">
                        <?php _e( 'Aucun tag existant.', 'smartseo-ai' ); ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="smartseo-ai-tag-generator-optimal-tags">
            <h4><?php _e( 'Combinaison optimale', 'smartseo-ai' ); ?></h4>
            
            <div class="smartseo-ai-tag-generator-optimal-tags-list">
                <!-- Rempli dynamiquement par JavaScript -->
            </div>
        </div>
        
        <div class="smartseo-ai-tag-generator-generated-tags">
            <h4><?php _e( 'Tags générés', 'smartseo-ai' ); ?></h4>
            
            <div class="smartseo-ai-tag-generator-generated-tags-list">
                <!-- Rempli dynamiquement par JavaScript -->
            </div>
        </div>
        
        <div class="smartseo-ai-tag-generator-actions">
            <button type="button" id="smartseo-ai-tag-generator-apply" class="button button-primary">
                <?php _e( 'Appliquer automatiquement', 'smartseo-ai' ); ?>
            </button>
            
            <button type="button" id="smartseo-ai-tag-generator-regenerate" class="button">
                <?php _e( 'Régénérer les suggestions', 'smartseo-ai' ); ?>
            </button>
        </div>
    </div>
    
    <div class="smartseo-ai-tag-generator-error" style="display: none;">
        <p class="smartseo-ai-tag-generator-error-message"></p>
    </div>
</div>
