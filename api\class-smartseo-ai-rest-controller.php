<?php
/**
 * Classe pour gérer les endpoints REST API
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les endpoints REST API
 */
class SmartSEO_AI_REST_Controller {

    /**
     * Namespace de l'API
     *
     * @var string
     */
    private $namespace = 'smartseo-ai/v1';

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à faire ici pour l'instant
    }

    /**
     * Enregistre les routes REST API
     */
    public function register_routes() {
        // Route pour optimiser un article
        register_rest_route(
            $this->namespace,
            '/optimize',
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'optimize_post' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'post_id' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        },
                        'sanitize_callback' => 'absint',
                    ),
                ),
            )
        );

        // Route pour récupérer les données SEO d'un article
        register_rest_route(
            $this->namespace,
            '/get-seo-data/(?P<post_id>\d+)',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_seo_data' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'post_id' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        },
                        'sanitize_callback' => 'absint',
                    ),
                ),
            )
        );

        // Route pour récupérer la liste des articles à optimiser
        register_rest_route(
            $this->namespace,
            '/get-posts-for-optimization',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_posts_for_optimization' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'post_type' => array(
                        'required'          => false,
                        'validate_callback' => function( $param ) {
                            return is_string( $param );
                        },
                        'sanitize_callback' => 'sanitize_text_field',
                    ),
                    'limit' => array(
                        'required'          => false,
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        },
                        'sanitize_callback' => 'absint',
                        'default'           => 50,
                    ),
                ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur
     *
     * @param WP_REST_Request $request Requête REST.
     * @return bool|WP_Error True si l'utilisateur a les permissions, WP_Error sinon.
     */
    public function check_permissions( $request ) {
        // Vérifier si l'utilisateur est connecté et peut éditer des articles
        if ( ! current_user_can( 'edit_posts' ) ) {
            return new WP_Error(
                'rest_forbidden',
                __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'smartseo-ai' ),
                array( 'status' => rest_authorization_required_code() )
            );
        }

        // Si un ID d'article est fourni, vérifier si l'utilisateur peut l'éditer
        $post_id = isset( $request['post_id'] ) ? $request['post_id'] : 0;
        if ( $post_id > 0 && ! current_user_can( 'edit_post', $post_id ) ) {
            return new WP_Error(
                'rest_forbidden',
                __( 'Vous n\'avez pas les permissions nécessaires pour éditer cet article.', 'smartseo-ai' ),
                array( 'status' => rest_authorization_required_code() )
            );
        }

        return true;
    }

    /**
     * Optimise un article avec l'IA
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response|WP_Error Réponse REST ou erreur.
     */
    public function optimize_post( $request ) {
        // Récupérer l'ID de l'article
        $post_id = $request->get_param( 'post_id' );

        // Récupérer l'article
        $post = get_post( $post_id );
        if ( ! $post ) {
            return new WP_Error(
                'post_not_found',
                __( 'Article non trouvé.', 'smartseo-ai' ),
                array( 'status' => 404 )
            );
        }

        // Récupérer le contenu et le titre de l'article
        $content = $post->post_content;
        $title = $post->post_title;

        // Nettoyer le contenu (supprimer les shortcodes, etc.)
        $content = $this->clean_content( $content );

        // Déterminer le fournisseur d'IA à utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        // Initialiser l'API appropriée
        if ( $provider === 'gemini' ) {
            $api = new SmartSEO_AI_Gemini();
        } else {
            $api = new SmartSEO_AI_API();
        }

        // Vérifier si le contenu est vide
        if ( empty( $content ) ) {
            return new WP_Error(
                'empty_content',
                __( 'Le contenu de l\'article est vide. Impossible d\'optimiser un article sans contenu.', 'smartseo-ai' ),
                array( 'status' => 400 )
            );
        }

        // Vérifier si le titre est vide
        if ( empty( $title ) ) {
            return new WP_Error(
                'empty_title',
                __( 'Le titre de l\'article est vide. Impossible d\'optimiser un article sans titre.', 'smartseo-ai' ),
                array( 'status' => 400 )
            );
        }

        try {
            // Optimiser le contenu
            $result = $api->optimize_content( $post_id, $content, $title );

            // Vérifier s'il y a une erreur
            if ( is_wp_error( $result ) ) {
                return $result;
            }
        } catch ( Exception $e ) {
            // Capturer toute exception et la convertir en WP_Error
            return new WP_Error(
                'api_exception',
                __( 'Une erreur s\'est produite lors de l\'optimisation : ', 'smartseo-ai' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }

        // Retourner les résultats
        return rest_ensure_response( $result );
    }

    /**
     * Récupère les données SEO d'un article
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response|WP_Error Réponse REST ou erreur.
     */
    public function get_seo_data( $request ) {
        // Récupérer l'ID de l'article
        $post_id = $request->get_param( 'post_id' );

        // Récupérer l'article
        $post = get_post( $post_id );
        if ( ! $post ) {
            return new WP_Error(
                'post_not_found',
                __( 'Article non trouvé.', 'smartseo-ai' ),
                array( 'status' => 404 )
            );
        }

        // Récupérer les données SEO
        $seo_data = array(
            'meta_description' => get_post_meta( $post_id, 'smartseo_ai_meta_description', true ),
            'keywords' => get_post_meta( $post_id, 'smartseo_ai_keywords', true ),
            'seo_title' => get_post_meta( $post_id, 'smartseo_ai_seo_title', true ),
            'seo_slug' => get_post_meta( $post_id, 'smartseo_ai_seo_slug', true ),
            'og_title' => get_post_meta( $post_id, 'smartseo_ai_og_title', true ),
            'og_description' => get_post_meta( $post_id, 'smartseo_ai_og_description', true ),
            'og_image' => get_post_meta( $post_id, 'smartseo_ai_og_image', true ),
            'seo_advice' => get_post_meta( $post_id, 'smartseo_ai_seo_advice', true ),
            'seo_score' => get_post_meta( $post_id, 'smartseo_ai_seo_score', true ),
        );

        // Retourner les données
        return rest_ensure_response( $seo_data );
    }

    /**
     * Récupère la liste des articles à optimiser
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response|WP_Error Réponse REST ou erreur.
     */
    public function get_posts_for_optimization( $request ) {
        // Récupérer les paramètres
        $post_type = $request->get_param( 'post_type' );
        $limit = $request->get_param( 'limit' );

        // Préparer les arguments de la requête
        $args = array(
            'post_type'      => ! empty( $post_type ) ? $post_type : array( 'post', 'page' ),
            'post_status'    => 'publish',
            'posts_per_page' => $limit,
            'orderby'        => 'date',
            'order'          => 'DESC',
        );

        // Exécuter la requête
        $query = new WP_Query( $args );
        $posts = array();

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();

                $post_id = get_the_ID();
                $score = get_post_meta( $post_id, 'smartseo_ai_seo_score', true );

                $posts[] = array(
                    'id'         => $post_id,
                    'title'      => get_the_title(),
                    'permalink'  => get_permalink(),
                    'edit_link'  => get_edit_post_link(),
                    'post_type'  => get_post_type_object( get_post_type() )->labels->singular_name,
                    'date'       => get_the_date(),
                    'seo_score'  => ! empty( $score ) ? intval( $score ) : 0,
                    'has_score'  => ! empty( $score ),
                    'status'     => ! empty( $score ) ? 'optimized' : 'not-optimized',
                );
            }

            wp_reset_postdata();
        }

        // Retourner les résultats
        return rest_ensure_response( $posts );
    }

    /**
     * Nettoie le contenu d'un article
     *
     * @param string $content Contenu de l'article.
     * @return string Contenu nettoyé.
     */
    private function clean_content( $content ) {
        // Supprimer les shortcodes
        $content = strip_shortcodes( $content );

        // Supprimer les balises HTML
        $content = wp_strip_all_tags( $content );

        // Supprimer les espaces multiples
        $content = preg_replace( '/\s+/', ' ', $content );

        // Limiter la longueur du contenu (pour éviter de dépasser les limites de l'API)
        if ( strlen( $content ) > 4000 ) {
            $content = substr( $content, 0, 4000 ) . '...';
        }

        return trim( $content );
    }
}
