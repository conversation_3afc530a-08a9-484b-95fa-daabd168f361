/**
 * Styles pour les tests de l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

/* Styles pour le bouton de test */
#smartseo-ai-test-button {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: #0073aa;
    color: white;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

#smartseo-ai-test-button:hover {
    background-color: #005a87;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

/* Styles pour le bouton de débogage */
#smartseo-ai-debug-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #dc3232;
    color: white;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

#smartseo-ai-debug-button:hover {
    background-color: #b32424;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

/* Styles pour les résultats de test */
.smartseo-ai-test-results {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: #f9f9f9;
}

.smartseo-ai-test-results h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
}

.smartseo-ai-test-results ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.smartseo-ai-test-results li {
    margin-bottom: 5px;
    padding: 5px;
    border-radius: 3px;
}

.smartseo-ai-test-results .success {
    background-color: #f0fff0;
    border-left: 3px solid #46b450;
}

.smartseo-ai-test-results .error {
    background-color: #fff0f0;
    border-left: 3px solid #dc3232;
}

.smartseo-ai-test-results .warning {
    background-color: #fffaf0;
    border-left: 3px solid #ffb900;
}

.smartseo-ai-test-results .info {
    background-color: #f0f8ff;
    border-left: 3px solid #00a0d2;
}

/* Styles pour les modales de test */
.smartseo-ai-test-modal .smartseo-ai-modal-header {
    background-color: #0073aa;
    color: white;
}

.smartseo-ai-test-modal .smartseo-ai-modal-close {
    color: white;
}

.smartseo-ai-test-modal .smartseo-ai-modal-body {
    padding: 20px;
}

/* Styles pour les notifications de test */
.smartseo-ai-test-notification {
    border-left-width: 5px !important;
}

.smartseo-ai-test-notification .smartseo-ai-notification-message {
    font-weight: 600;
}

/* Styles pour les animations de test */
.smartseo-ai-test-loading {
    background-color: rgba(0, 115, 170, 0.8) !important;
}

.smartseo-ai-test-loading #smartseo-ai-loading-container {
    border: 2px solid #0073aa !important;
}

.smartseo-ai-test-loading #smartseo-ai-loading-spinner {
    border-top-color: #0073aa !important;
}

.smartseo-ai-test-loading #smartseo-ai-loading-message {
    color: #0073aa !important;
}
