<?php
/**
 * Vue du tableau de bord amélioré SmartSEO AI
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Récupérer les gestionnaires
$ui_manager = SmartSEO_AI_UI_Manager::get_instance();
$cache_manager = SmartSEO_AI_Cache_Manager::get_instance();
$performance_manager = SmartSEO_AI_Performance_Manager::get_instance();
$queue_manager = SmartSEO_AI_Queue_Manager::get_instance();

// Récupérer les données initiales
$cache_stats = $cache_manager->get_stats();
$performance_stats = $performance_manager->get_performance_stats();
$queue_status = $queue_manager->get_queue_status();
$api_health = $performance_manager->check_api_health();
?>

<div class="wrap smartseo-ai-enhanced-dashboard">
    <h1><?php _e( 'Tableau de bord SmartSEO AI', 'smartseo-ai' ); ?></h1>
    
    <!-- Indicateurs de santé -->
    <div class="smartseo-health-indicators">
        <div class="health-card" id="api-health-card">
            <h3><?php _e( 'Santé des API', 'smartseo-ai' ); ?></h3>
            <div class="health-status" id="api-health-status">
                <?php
                $api_status = 'healthy';
                $api_message = __( 'Toutes les API fonctionnent', 'smartseo-ai' );
                
                foreach ( $api_health as $provider => $health ) {
                    if ( 'error' === $health['status'] ) {
                        $api_status = 'error';
                        $api_message = sprintf( __( 'Problème avec %s', 'smartseo-ai' ), $provider );
                        break;
                    }
                }
                ?>
                <span class="status-indicator <?php echo esc_attr( $api_status ); ?>"></span>
                <?php echo esc_html( $api_message ); ?>
            </div>
            <div class="health-details">
                <?php foreach ( $api_health as $provider => $health ) : ?>
                    <div class="api-detail">
                        <strong><?php echo esc_html( ucfirst( $provider ) ); ?>:</strong>
                        <span class="status-<?php echo esc_attr( $health['status'] ); ?>">
                            <?php echo esc_html( $health['status'] ); ?>
                        </span>
                        <?php if ( isset( $health['response_time'] ) ) : ?>
                            <small>(<?php echo esc_html( number_format( $health['response_time'], 3 ) ); ?>s)</small>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div class="health-card" id="cache-health-card">
            <h3><?php _e( 'Cache', 'smartseo-ai' ); ?></h3>
            <div class="health-status" id="cache-health-status">
                <?php
                $cache_entries = $cache_stats['general']['total_entries'] ?? 0;
                $cache_hits = $cache_stats['general']['total_hits'] ?? 0;
                $hit_rate = $cache_entries > 0 ? round( ( $cache_hits / $cache_entries ) * 100 ) : 0;
                ?>
                <span class="status-indicator healthy"></span>
                <?php printf( __( '%d entrées, %d%% de hits', 'smartseo-ai' ), $cache_entries, $hit_rate ); ?>
            </div>
            <div class="health-details">
                <div class="cache-detail">
                    <strong><?php _e( 'Actives:', 'smartseo-ai' ); ?></strong>
                    <?php echo esc_html( $cache_stats['general']['active_entries'] ?? 0 ); ?>
                </div>
                <div class="cache-detail">
                    <strong><?php _e( 'Expirées:', 'smartseo-ai' ); ?></strong>
                    <?php echo esc_html( $cache_stats['general']['expired_entries'] ?? 0 ); ?>
                </div>
            </div>
        </div>
        
        <div class="health-card" id="queue-health-card">
            <h3><?php _e( 'Queue d\'optimisation', 'smartseo-ai' ); ?></h3>
            <div class="health-status" id="queue-health-status">
                <?php
                $pending = $queue_status['pending'] ?? 0;
                $processing = $queue_status['processing'] ?? 0;
                $queue_status_class = $processing > 0 ? 'processing' : 'healthy';
                ?>
                <span class="status-indicator <?php echo esc_attr( $queue_status_class ); ?>"></span>
                <?php printf( __( '%d en attente, %d en cours', 'smartseo-ai' ), $pending, $processing ); ?>
            </div>
            <div class="health-details">
                <div class="queue-detail">
                    <strong><?php _e( 'Terminés:', 'smartseo-ai' ); ?></strong>
                    <?php echo esc_html( $queue_status['completed'] ?? 0 ); ?>
                </div>
                <div class="queue-detail">
                    <strong><?php _e( 'Échecs:', 'smartseo-ai' ); ?></strong>
                    <?php echo esc_html( $queue_status['failed'] ?? 0 ); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques de performance -->
    <div class="smartseo-charts-container">
        <div class="chart-card">
            <h3><?php _e( 'Performance des API', 'smartseo-ai' ); ?></h3>
            <canvas id="performance-chart" width="400" height="200"></canvas>
        </div>
        
        <div class="chart-card">
            <h3><?php _e( 'Utilisation du cache', 'smartseo-ai' ); ?></h3>
            <canvas id="cache-chart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Statistiques détaillées -->
    <div class="smartseo-stats-container">
        <div class="stats-card">
            <h3><?php _e( 'Statistiques générales', 'smartseo-ai' ); ?></h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label"><?php _e( 'Requêtes totales:', 'smartseo-ai' ); ?></span>
                    <span class="stat-value"><?php echo esc_html( $performance_stats['general']['total_requests'] ?? 0 ); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><?php _e( 'Taux de succès:', 'smartseo-ai' ); ?></span>
                    <span class="stat-value">
                        <?php
                        $total = $performance_stats['general']['total_requests'] ?? 0;
                        $success = $performance_stats['general']['successful_requests'] ?? 0;
                        $rate = $total > 0 ? round( ( $success / $total ) * 100 ) : 0;
                        echo esc_html( $rate . '%' );
                        ?>
                    </span>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><?php _e( 'Temps moyen:', 'smartseo-ai' ); ?></span>
                    <span class="stat-value">
                        <?php echo esc_html( number_format( $performance_stats['general']['avg_processing_time'] ?? 0, 2 ) . 's' ); ?>
                    </span>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><?php _e( 'Tokens utilisés:', 'smartseo-ai' ); ?></span>
                    <span class="stat-value"><?php echo esc_html( number_format( $performance_stats['general']['total_tokens'] ?? 0 ) ); ?></span>
                </div>
            </div>
        </div>

        <div class="stats-card">
            <h3><?php _e( 'Performance par fournisseur', 'smartseo-ai' ); ?></h3>
            <div class="provider-stats">
                <?php if ( ! empty( $performance_stats['by_provider'] ) ) : ?>
                    <?php foreach ( $performance_stats['by_provider'] as $provider_stat ) : ?>
                        <div class="provider-stat">
                            <h4><?php echo esc_html( ucfirst( $provider_stat['provider'] ) ); ?></h4>
                            <div class="provider-details">
                                <span><?php printf( __( '%d requêtes', 'smartseo-ai' ), $provider_stat['requests'] ); ?></span>
                                <span><?php printf( __( '%d succès', 'smartseo-ai' ), $provider_stat['successes'] ); ?></span>
                                <span><?php printf( __( '%.2fs moyen', 'smartseo-ai' ), $provider_stat['avg_time'] ); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else : ?>
                    <p><?php _e( 'Aucune donnée disponible.', 'smartseo-ai' ); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="smartseo-quick-actions">
        <h3><?php _e( 'Actions rapides', 'smartseo-ai' ); ?></h3>
        <div class="action-buttons">
            <button type="button" class="button button-secondary" id="clear-cache-btn">
                <span class="dashicons dashicons-trash"></span>
                <?php _e( 'Vider le cache', 'smartseo-ai' ); ?>
            </button>
            <button type="button" class="button button-secondary" id="check-api-health-btn">
                <span class="dashicons dashicons-heart"></span>
                <?php _e( 'Vérifier les API', 'smartseo-ai' ); ?>
            </button>
            <button type="button" class="button button-primary" id="refresh-dashboard-btn">
                <span class="dashicons dashicons-update"></span>
                <?php _e( 'Actualiser', 'smartseo-ai' ); ?>
            </button>
        </div>
    </div>

    <!-- Erreurs récentes -->
    <?php if ( ! empty( $performance_stats['recent_errors'] ) ) : ?>
        <div class="smartseo-recent-errors">
            <h3><?php _e( 'Erreurs récentes', 'smartseo-ai' ); ?></h3>
            <div class="errors-list">
                <?php foreach ( array_slice( $performance_stats['recent_errors'], 0, 5 ) as $error ) : ?>
                    <div class="error-item">
                        <span class="error-time"><?php echo esc_html( $error['created_at'] ); ?></span>
                        <span class="error-provider"><?php echo esc_html( $error['provider'] ); ?></span>
                        <span class="error-type"><?php echo esc_html( $error['optimization_type'] ); ?></span>
                        <span class="error-message"><?php echo esc_html( $error['error_message'] ); ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Données initiales pour JavaScript -->
<script type="text/javascript">
    window.smartseoInitialData = <?php echo wp_json_encode( array(
        'cache_stats' => $cache_stats,
        'performance_stats' => $performance_stats,
        'queue_status' => $queue_status,
        'api_health' => $api_health,
    ) ); ?>;
</script>

<style>
/* Styles spécifiques à cette page */
.health-details {
    margin-top: 10px;
    font-size: 12px;
    color: #666;
}

.api-detail,
.cache-detail,
.queue-detail {
    margin-bottom: 5px;
}

.status-healthy { color: #46b450; }
.status-error { color: #dc3232; }
.status-processing { color: #0073aa; }

.smartseo-stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stats-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.stat-label {
    font-weight: 600;
    color: #666;
}

.stat-value {
    font-weight: bold;
    color: #0073aa;
}

.provider-stats {
    display: grid;
    gap: 15px;
}

.provider-stat h4 {
    margin: 0 0 5px 0;
    color: #0073aa;
}

.provider-details {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.smartseo-recent-errors {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.errors-list {
    max-height: 300px;
    overflow-y: auto;
}

.error-item {
    display: grid;
    grid-template-columns: 150px 80px 120px 1fr;
    gap: 10px;
    padding: 8px;
    border-bottom: 1px solid #eee;
    font-size: 12px;
}

.error-time { color: #666; }
.error-provider { font-weight: bold; color: #0073aa; }
.error-type { color: #666; }
.error-message { color: #dc3232; }

.action-buttons .button .dashicons {
    margin-right: 5px;
}
</style>
