/**
 * Script d'initialisation principal pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Initialisation principale de l'Assistant de Rédaction SEO
     */
    const SmartSEOAIInit = {
        /**
         * Initialise l'Assistant de Rédaction SEO
         */
        init: function() {
            console.log('SmartSEO AI Init: Initialisation principale');
            
            // Vérifier si le namespace global est disponible
            if (!window.SmartSEOAI) {
                console.error('SmartSEO AI Init: Le namespace global n\'est pas disponible');
                
                // Créer un bouton de débogage
                this.createDebugButton();
                return;
            }
            
            // Initialiser les modules
            this.initModules();
            
            console.log('SmartSEO AI Init: Initialisation principale terminée');
        },
        
        /**
         * Initialise les modules
         */
        initModules: function() {
            console.log('SmartSEO AI Init: Initialisation des modules');
            
            try {
                // Initialiser le gestionnaire d'interface utilisateur
                if (window.SmartSEOAI.UIManager) {
                    window.SmartSEOAI.UIManager.init();
                    console.log('SmartSEO AI Init: UIManager initialisé avec succès');
                } else {
                    console.error('SmartSEO AI Init: UIManager n\'est pas disponible');
                }
                
                // Initialiser le générateur de contenu
                if (window.SmartSEOAI.ContentGenerator) {
                    window.SmartSEOAI.ContentGenerator.init();
                    console.log('SmartSEO AI Init: ContentGenerator initialisé avec succès');
                } else {
                    console.error('SmartSEO AI Init: ContentGenerator n\'est pas disponible');
                }
                
                // Initialiser l'analyseur de mots-clés
                if (window.SmartSEOAI.KeywordAnalyzer) {
                    window.SmartSEOAI.KeywordAnalyzer.init();
                    console.log('SmartSEO AI Init: KeywordAnalyzer initialisé avec succès');
                } else {
                    console.error('SmartSEO AI Init: KeywordAnalyzer n\'est pas disponible');
                }
                
                // Initialiser l'analyseur en temps réel
                if (window.SmartSEOAI.LiveAnalyzer) {
                    window.SmartSEOAI.LiveAnalyzer.init();
                    console.log('SmartSEO AI Init: LiveAnalyzer initialisé avec succès');
                } else {
                    console.error('SmartSEO AI Init: LiveAnalyzer n\'est pas disponible');
                }
                
                // Initialiser le gestionnaire de tendances
                if (window.SmartSEOAI.TrendsManager) {
                    window.SmartSEOAI.TrendsManager.init();
                    console.log('SmartSEO AI Init: TrendsManager initialisé avec succès');
                } else {
                    console.error('SmartSEO AI Init: TrendsManager n\'est pas disponible');
                }
            } catch (error) {
                console.error('SmartSEO AI Init: Erreur lors de l\'initialisation des modules', error);
            }
        },
        
        /**
         * Crée un bouton de débogage
         */
        createDebugButton: function() {
            console.log('SmartSEO AI Init: Création du bouton de débogage');
            
            // Créer le bouton
            const $button = $('<div id="smartseo-ai-debug-button" style="position: fixed; bottom: 20px; right: 20px; background-color: #dc3232; color: white; padding: 10px; border-radius: 5px; cursor: pointer; z-index: 9999;">SmartSEO AI Debug</div>');
            
            // Ajouter le bouton au corps de la page
            $('body').append($button);
            
            // Ajouter l'écouteur d'événement
            $button.on('click', function() {
                // Afficher les informations de débogage
                console.log('SmartSEO AI Debug: Informations de débogage');
                
                // Vérifier si le namespace global est disponible
                if (window.SmartSEOAI) {
                    console.log('SmartSEO AI Debug: Le namespace global est disponible');
                    
                    // Vérifier les modules
                    console.log('SmartSEO AI Debug: Modules disponibles', Object.keys(window.SmartSEOAI));
                } else {
                    console.error('SmartSEO AI Debug: Le namespace global n\'est pas disponible');
                }
                
                // Vérifier les scripts chargés
                console.log('SmartSEO AI Debug: Scripts chargés', $('script').map(function() {
                    return $(this).attr('src');
                }).get());
                
                // Vérifier les styles chargés
                console.log('SmartSEO AI Debug: Styles chargés', $('link[rel="stylesheet"]').map(function() {
                    return $(this).attr('href');
                }).get());
                
                // Vérifier si nous sommes dans l'éditeur Gutenberg
                if (typeof wp !== 'undefined' && wp.data && wp.data.select('core/editor')) {
                    console.log('SmartSEO AI Debug: Éditeur Gutenberg détecté');
                } else {
                    console.log('SmartSEO AI Debug: Éditeur classique détecté');
                }
                
                // Afficher un message
                alert('Informations de débogage affichées dans la console.');
            });
            
            console.log('SmartSEO AI Init: Bouton de débogage créé avec succès');
        }
    };
    
    // Initialiser l'Assistant de Rédaction SEO au chargement du document
    $(document).ready(function() {
        // Attendre que tous les scripts soient chargés
        setTimeout(function() {
            SmartSEOAIInit.init();
        }, 1000);
    });

})(jQuery);
