/**
 * Module de gestion du panneau de mots-clés pour l'éditeur classique
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Gestionnaire du panneau de mots-clés pour l'éditeur classique
     */
    const ClassicKeywordPanel = {
        /**
         * Initialise le gestionnaire du panneau de mots-clés
         */
        init: function() {
            // Rien à initialiser pour l'instant
        },

        /**
         * Rend le panneau de mots-clés
         * @return {string} HTML du panneau
         */
        render: function() {
            // Créer la structure du panneau
            const $panel = $('<div class="smartseo-ai-panel"></div>');
            
            // Ajouter l'en-tête
            $panel.append('<div class="smartseo-ai-panel-header">' +
                '<h3>Analyse de mots-clés</h3>' +
                '<p>Analysez et trouvez les meilleurs mots-clés pour votre contenu.</p>' +
                '</div>');
            
            // Ajouter le contenu
            const $content = $('<div class="smartseo-ai-panel-content"></div>');
            
            // Ajouter le champ de mot-clé
            $content.append('<div class="smartseo-ai-form-group">' +
                '<label for="smartseo-ai-focus-keyword-analysis">Mot-clé principal</label>' +
                '<input type="text" id="smartseo-ai-focus-keyword-analysis" class="widefat" placeholder="Entrez votre mot-clé principal">' +
                '</div>');
            
            // Ajouter les boutons d'analyse
            $content.append('<div class="smartseo-ai-button-group">' +
                '<button type="button" class="button button-primary smartseo-ai-analyze-keywords">Analyser les mots-clés</button>' +
                '<button type="button" class="button button-primary smartseo-ai-get-trends">Obtenir les tendances</button>' +
                '</div>');
            
            // Ajouter le conteneur de résultats
            $content.append('<div class="smartseo-ai-results-container" id="smartseo-ai-keyword-results">' +
                '<div class="smartseo-ai-loading" style="display: none;">' +
                '<span class="spinner is-active"></span>' +
                '<p>Analyse en cours...</p>' +
                '</div>' +
                '<div class="smartseo-ai-results" style="display: none;">' +
                '<h4>Résultats de l\'analyse</h4>' +
                '<div class="smartseo-ai-results-content"></div>' +
                '</div>' +
                '</div>');
            
            // Ajouter le contenu au panneau
            $panel.append($content);
            
            return $panel;
        },

        /**
         * Affiche les résultats d'analyse des mots-clés
         * @param {Object} results Résultats d'analyse
         */
        showResults: function(results) {
            const $container = $('#smartseo-ai-keyword-results');
            const $loading = $container.find('.smartseo-ai-loading');
            const $results = $container.find('.smartseo-ai-results');
            const $content = $results.find('.smartseo-ai-results-content');
            
            // Masquer le chargement
            $loading.hide();
            
            // Vider le contenu précédent
            $content.empty();
            
            // Afficher les résultats
            $results.show();
            
            // Vérifier si les résultats sont valides
            if (!results || (!results.primary_keyword && (!results.secondary_keywords || results.secondary_keywords.length === 0))) {
                $content.append('<p>Aucun résultat disponible.</p>');
                return;
            }
            
            // Afficher le mot-clé principal
            if (results.primary_keyword) {
                const primaryKeyword = results.primary_keyword;
                
                $content.append('<h5>Mot-clé principal</h5>');
                
                const $primaryItem = $('<div class="smartseo-ai-keyword-item smartseo-ai-primary-keyword"></div>');
                $primaryItem.append('<div class="smartseo-ai-keyword-name">' + primaryKeyword.keyword + '</div>');
                
                const $primaryDetails = $('<div class="smartseo-ai-keyword-details"></div>');
                $primaryDetails.append('<div class="smartseo-ai-keyword-volume"><span>Volume :</span> ' + primaryKeyword.volume + '</div>');
                $primaryDetails.append('<div class="smartseo-ai-keyword-difficulty"><span>Difficulté :</span> ' + primaryKeyword.difficulty + '</div>');
                $primaryDetails.append('<div class="smartseo-ai-keyword-intent"><span>Intention :</span> ' + primaryKeyword.intent + '</div>');
                
                $primaryItem.append($primaryDetails);
                
                $primaryItem.append('<div class="smartseo-ai-keyword-actions">' +
                    '<button class="button smartseo-ai-use-keyword" data-keyword="' + primaryKeyword.keyword + '">Utiliser</button>' +
                    '</div>');
                
                $content.append($primaryItem);
            }
            
            // Afficher les mots-clés secondaires
            if (results.secondary_keywords && results.secondary_keywords.length > 0) {
                $content.append('<h5>Mots-clés secondaires</h5>');
                
                const $secondaryList = $('<div class="smartseo-ai-keyword-list"></div>');
                
                results.secondary_keywords.forEach(keyword => {
                    const $item = $('<div class="smartseo-ai-keyword-item"></div>');
                    $item.append('<div class="smartseo-ai-keyword-name">' + keyword.keyword + '</div>');
                    
                    const $details = $('<div class="smartseo-ai-keyword-details"></div>');
                    $details.append('<div class="smartseo-ai-keyword-volume"><span>Volume :</span> ' + keyword.volume + '</div>');
                    $details.append('<div class="smartseo-ai-keyword-difficulty"><span>Difficulté :</span> ' + keyword.difficulty + '</div>');
                    $details.append('<div class="smartseo-ai-keyword-intent"><span>Intention :</span> ' + keyword.intent + '</div>');
                    
                    $item.append($details);
                    
                    $item.append('<div class="smartseo-ai-keyword-actions">' +
                        '<button class="button smartseo-ai-use-keyword" data-keyword="' + keyword.keyword + '">Utiliser</button>' +
                        '</div>');
                    
                    $secondaryList.append($item);
                });
                
                $content.append($secondaryList);
            }
            
            // Afficher les mots-clés longue traîne
            if (results.long_tail_keywords && results.long_tail_keywords.length > 0) {
                $content.append('<h5>Mots-clés longue traîne</h5>');
                
                const $longTailList = $('<div class="smartseo-ai-keyword-list"></div>');
                
                results.long_tail_keywords.forEach(keyword => {
                    const $item = $('<div class="smartseo-ai-keyword-item"></div>');
                    $item.append('<div class="smartseo-ai-keyword-name">' + keyword.keyword + '</div>');
                    
                    const $details = $('<div class="smartseo-ai-keyword-details"></div>');
                    $details.append('<div class="smartseo-ai-keyword-volume"><span>Volume :</span> ' + keyword.volume + '</div>');
                    $details.append('<div class="smartseo-ai-keyword-difficulty"><span>Difficulté :</span> ' + keyword.difficulty + '</div>');
                    $details.append('<div class="smartseo-ai-keyword-intent"><span>Intention :</span> ' + keyword.intent + '</div>');
                    
                    $item.append($details);
                    
                    $item.append('<div class="smartseo-ai-keyword-actions">' +
                        '<button class="button smartseo-ai-use-keyword" data-keyword="' + keyword.keyword + '">Utiliser</button>' +
                        '</div>');
                    
                    $longTailList.append($item);
                });
                
                $content.append($longTailList);
            }
            
            // Ajouter les écouteurs d'événements pour les boutons "Utiliser"
            $('.smartseo-ai-use-keyword').on('click', function() {
                const keyword = $(this).data('keyword');
                $('#smartseo-ai-focus-keyword, #smartseo-ai-focus-keyword-analysis, #smartseo-ai-trends-keyword').val(keyword);
                
                // Mettre à jour le champ de mot-clé principal (si Yoast SEO est installé)
                $('#yoast_wpseo_focuskw').val(keyword);
                
                // Mettre à jour le champ de mot-clé principal personnalisé
                $('#smartseo_ai_focus_keyword').val(keyword);
                
                // Afficher un message de succès
                window.SmartSEOAI.UIManager.showSuccess('Mot-clé principal mis à jour.');
            });
        }
    };

    // Ajouter le module au namespace global
    window.SmartSEOAI = window.SmartSEOAI || {};
    window.SmartSEOAI.ClassicKeywordPanel = ClassicKeywordPanel;

})(jQuery);
