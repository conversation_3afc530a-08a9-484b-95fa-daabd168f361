<?php
/**
 * Classe pour l'Assistant de Rédaction SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'Assistant de Rédaction SEO
 */
class SmartSEO_AI_Writing_Assistant {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_Writing_Assistant
     */
    private static $instance = null;

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Instance du générateur de contenu
     *
     * @var SmartSEO_AI_Content_Generator
     */
    private $content_generator;

    /**
     * Instance de l'analyseur de mots-clés
     *
     * @var SmartSEO_AI_Keyword_Analyzer
     */
    private $keyword_analyzer;

    /**
     * Instance de l'analyseur en temps réel
     *
     * @var SmartSEO_AI_Live_Analyzer
     */
    private $live_analyzer;

    /**
     * Instance de l'API de tendances
     *
     * @var SmartSEO_AI_Trends_API
     */
    private $trends_api;

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }

        // Initialiser les classes
        $this->content_generator = new SmartSEO_AI_Content_Generator( $this->ai_api );
        $this->keyword_analyzer = new SmartSEO_AI_Keyword_Analyzer( $this->ai_api );
        $this->live_analyzer = new SmartSEO_AI_Live_Analyzer();
        $this->trends_api = new SmartSEO_AI_Trends_API();

        // Ajouter les hooks
        $this->add_hooks();
    }

    /**
     * Ajoute les hooks WordPress
     */
    private function add_hooks() {
        // Ajouter le menu d'administration
        add_action( 'admin_menu', array( $this, 'add_writing_assistant_menu' ) );

        // Enregistrer les scripts et styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_assets' ) );

        // Ajouter les actions AJAX
        add_action( 'wp_ajax_smartseo_ai_generate_content', array( $this, 'ajax_generate_content' ) );
        add_action( 'wp_ajax_smartseo_ai_analyze_keywords', array( $this, 'ajax_analyze_keywords' ) );
        add_action( 'wp_ajax_smartseo_ai_get_trends', array( $this, 'ajax_get_trends' ) );
        add_action( 'wp_ajax_smartseo_ai_live_analyze', array( $this, 'ajax_live_analyze' ) );
        add_action( 'wp_ajax_smartseo_ai_check_plagiarism', array( $this, 'ajax_check_plagiarism' ) );

        // Ajouter les filtres pour l'intégration avec l'éditeur
        add_filter( 'mce_buttons', array( $this, 'register_mce_button' ) );
        add_filter( 'mce_external_plugins', array( $this, 'add_mce_plugin' ) );

        // Ajouter les hooks pour Gutenberg
        add_action( 'enqueue_block_editor_assets', array( $this, 'enqueue_block_editor_assets' ) );
    }

    /**
     * Ajoute le menu de l'Assistant de Rédaction SEO
     */
    public function add_writing_assistant_menu() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Assistant de Rédaction SEO', 'smartseo-ai' ),
            __( 'Assistant de Rédaction', 'smartseo-ai' ),
            'edit_posts',
            'smartseo-ai-writing-assistant',
            array( $this, 'render_writing_assistant_page' )
        );
    }

    /**
     * Affiche la page de l'Assistant de Rédaction SEO
     */
    public function render_writing_assistant_page() {
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/writing-assistant.php';
    }

    /**
     * Enregistre les scripts et styles
     *
     * @param string $hook Hook actuel.
     */
    public function enqueue_assets( $hook ) {
        // N'enregistrer les scripts que sur les pages nécessaires
        if ( 'post.php' !== $hook && 'post-new.php' !== $hook && 'smartseo-ai_page_smartseo-ai-writing-assistant' !== $hook ) {
            return;
        }

        // Styles
        wp_enqueue_style(
            'smartseo-ai-writing-assistant',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-writing-assistant.css',
            array(),
            SMARTSEO_AI_VERSION
        );

        // Scripts
        wp_enqueue_script(
            'smartseo-ai-writing-assistant',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-writing-assistant.js',
            array( 'jquery', 'wp-i18n' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Script pour l'analyse en temps réel
        wp_enqueue_script(
            'smartseo-ai-live-analyzer',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-live-analyzer.js',
            array( 'jquery', 'wp-i18n' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Localiser le script
        wp_localize_script(
            'smartseo-ai-writing-assistant',
            'smartseoAiWritingAssistant',
            array(
                'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'smartseo_ai_nonce' ),
                'i18n' => array(
                    'generateContent' => __( 'Générer du contenu', 'smartseo-ai' ),
                    'analyzing' => __( 'Analyse en cours...', 'smartseo-ai' ),
                    'generating' => __( 'Génération en cours...', 'smartseo-ai' ),
                    'success' => __( 'Génération réussie !', 'smartseo-ai' ),
                    'error' => __( 'Erreur lors de la génération.', 'smartseo-ai' ),
                    'keywordAnalysis' => __( 'Analyse des mots-clés', 'smartseo-ai' ),
                    'trendingTopics' => __( 'Sujets tendances', 'smartseo-ai' ),
                    'seoScore' => __( 'Score SEO', 'smartseo-ai' ),
                    'optimizeAll' => __( 'Optimiser automatiquement', 'smartseo-ai' ),
                    'checkPlagiarism' => __( 'Vérifier la non-duplication', 'smartseo-ai' ),
                    'contentLength' => __( 'Longueur du contenu', 'smartseo-ai' ),
                    'keywordDensity' => __( 'Densité des mots-clés', 'smartseo-ai' ),
                    'readability' => __( 'Lisibilité', 'smartseo-ai' ),
                    'titleTag' => __( 'Balise titre', 'smartseo-ai' ),
                    'metaDescription' => __( 'Meta description', 'smartseo-ai' ),
                    'headings' => __( 'Titres et sous-titres', 'smartseo-ai' ),
                    'images' => __( 'Images', 'smartseo-ai' ),
                    'links' => __( 'Liens', 'smartseo-ai' ),
                    'good' => __( 'Bon', 'smartseo-ai' ),
                    'average' => __( 'Moyen', 'smartseo-ai' ),
                    'poor' => __( 'Faible', 'smartseo-ai' ),
                    'apply' => __( 'Appliquer', 'smartseo-ai' ),
                    'copy' => __( 'Copier', 'smartseo-ai' ),
                    'copied' => __( 'Copié !', 'smartseo-ai' ),
                ),
            )
        );
    }

    /**
     * Enregistre les scripts et styles pour l'éditeur de blocs
     */
    public function enqueue_block_editor_assets() {
        // Styles pour le panneau latéral
        wp_enqueue_style(
            'smartseo-ai-writing-assistant-gutenberg',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-writing-assistant.css',
            array(),
            SMARTSEO_AI_VERSION
        );

        // Script pour le panneau latéral
        wp_enqueue_script(
            'smartseo-ai-writing-assistant-gutenberg',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-writing-assistant-gutenberg.js',
            array( 'wp-blocks', 'wp-element', 'wp-components', 'wp-editor', 'wp-data', 'wp-plugins', 'wp-edit-post', 'wp-i18n', 'wp-api-fetch' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Localiser le script
        wp_localize_script(
            'smartseo-ai-writing-assistant-gutenberg',
            'smartseoAiWritingAssistant',
            array(
                'restUrl' => rest_url( 'smartseo-ai/v1' ),
                'nonce' => wp_create_nonce( 'wp_rest' ),
                'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                'ajaxNonce' => wp_create_nonce( 'smartseo_ai_nonce' ),
                'i18n' => array(
                    'panelTitle' => __( 'Assistant de Rédaction SEO', 'smartseo-ai' ),
                    'generateContent' => __( 'Générer du contenu', 'smartseo-ai' ),
                    'analyzing' => __( 'Analyse en cours...', 'smartseo-ai' ),
                    'generating' => __( 'Génération en cours...', 'smartseo-ai' ),
                    'success' => __( 'Génération réussie !', 'smartseo-ai' ),
                    'error' => __( 'Erreur lors de la génération.', 'smartseo-ai' ),
                    'keywordAnalysis' => __( 'Analyse des mots-clés', 'smartseo-ai' ),
                    'trendingTopics' => __( 'Sujets tendances', 'smartseo-ai' ),
                    'seoScore' => __( 'Score SEO', 'smartseo-ai' ),
                    'optimizeAll' => __( 'Optimiser automatiquement', 'smartseo-ai' ),
                    'checkPlagiarism' => __( 'Vérifier la non-duplication', 'smartseo-ai' ),
                    'contentLength' => __( 'Longueur du contenu', 'smartseo-ai' ),
                    'keywordDensity' => __( 'Densité des mots-clés', 'smartseo-ai' ),
                    'readability' => __( 'Lisibilité', 'smartseo-ai' ),
                    'titleTag' => __( 'Balise titre', 'smartseo-ai' ),
                    'metaDescription' => __( 'Meta description', 'smartseo-ai' ),
                    'headings' => __( 'Titres et sous-titres', 'smartseo-ai' ),
                    'images' => __( 'Images', 'smartseo-ai' ),
                    'links' => __( 'Liens', 'smartseo-ai' ),
                    'good' => __( 'Bon', 'smartseo-ai' ),
                    'average' => __( 'Moyen', 'smartseo-ai' ),
                    'poor' => __( 'Faible', 'smartseo-ai' ),
                    'apply' => __( 'Appliquer', 'smartseo-ai' ),
                    'copy' => __( 'Copier', 'smartseo-ai' ),
                    'copied' => __( 'Copié !', 'smartseo-ai' ),
                ),
            )
        );
    }

    /**
     * Enregistre le bouton TinyMCE
     *
     * @param array $buttons Boutons existants.
     * @return array Boutons mis à jour.
     */
    public function register_mce_button( $buttons ) {
        array_push( $buttons, 'smartseo_ai_writing_assistant' );
        return $buttons;
    }

    /**
     * Ajoute le plugin TinyMCE
     *
     * @param array $plugins Plugins existants.
     * @return array Plugins mis à jour.
     */
    public function add_mce_plugin( $plugins ) {
        $plugins['smartseo_ai_writing_assistant'] = SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-writing-assistant-tinymce.js';
        return $plugins;
    }

    /**
     * Génère du contenu via AJAX
     */
    public function ajax_generate_content() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $content_type = isset( $_POST['content_type'] ) ? sanitize_text_field( wp_unslash( $_POST['content_type'] ) ) : '';
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';
        $topic = isset( $_POST['topic'] ) ? sanitize_text_field( wp_unslash( $_POST['topic'] ) ) : '';
        $content = isset( $_POST['content'] ) ? wp_kses_post( wp_unslash( $_POST['content'] ) ) : '';

        if ( empty( $content_type ) ) {
            wp_send_json_error( array( 'message' => __( 'Type de contenu non spécifié.', 'smartseo-ai' ) ) );
        }

        // Générer le contenu
        $result = $this->content_generator->generate( $post_id, $content_type, $keyword, $topic, $content );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array( 'content' => $result ) );
    }

    /**
     * Analyse les mots-clés via AJAX
     */
    public function ajax_analyze_keywords() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $content = isset( $_POST['content'] ) ? wp_kses_post( wp_unslash( $_POST['content'] ) ) : '';
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';

        // Analyser les mots-clés
        $result = $this->keyword_analyzer->analyze( $post_id, $content, $keyword );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( $result );
    }

    /**
     * Récupère les tendances via AJAX
     */
    public function ajax_get_trends() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';

        // Récupérer les tendances
        $result = $this->trends_api->get_trends( $keyword );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( $result );
    }

    /**
     * Analyse le contenu en temps réel via AJAX
     */
    public function ajax_live_analyze() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $content = isset( $_POST['content'] ) ? wp_kses_post( wp_unslash( $_POST['content'] ) ) : '';
        $title = isset( $_POST['title'] ) ? sanitize_text_field( wp_unslash( $_POST['title'] ) ) : '';
        $keyword = isset( $_POST['keyword'] ) ? sanitize_text_field( wp_unslash( $_POST['keyword'] ) ) : '';

        // Analyser le contenu
        $result = $this->live_analyzer->analyze( $post_id, $content, $title, $keyword );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( $result );
    }

    /**
     * Vérifie la non-duplication du contenu via AJAX
     */
    public function ajax_check_plagiarism() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        $content = isset( $_POST['content'] ) ? wp_kses_post( wp_unslash( $_POST['content'] ) ) : '';

        if ( empty( $content ) ) {
            wp_send_json_error( array( 'message' => __( 'Contenu vide.', 'smartseo-ai' ) ) );
        }

        // Vérifier si une API de détection de plagiat est configurée
        $plagiarism_api_key = get_option( 'smartseo_ai_plagiarism_api_key', '' );
        if ( empty( $plagiarism_api_key ) ) {
            wp_send_json_error( array( 'message' => __( 'Aucune API de détection de plagiat n\'est configurée.', 'smartseo-ai' ) ) );
        }

        // Simuler une vérification de plagiat (à remplacer par une vraie API)
        $result = array(
            'unique_percentage' => 95,
            'duplicate_percentage' => 5,
            'duplicate_sources' => array(
                array(
                    'url' => 'https://example.com/sample-page',
                    'similarity' => 5,
                    'matched_text' => substr( $content, 0, 50 ) . '...',
                ),
            ),
        );

        wp_send_json_success( $result );
    }

    /**
     * Retourne l'instance unique de la classe
     *
     * @return SmartSEO_AI_Writing_Assistant Instance de la classe.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
