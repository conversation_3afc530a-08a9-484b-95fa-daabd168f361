<?php
/**
 * Classe de sauvegarde des paramètres du Sitemap XML
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui sauvegarde les paramètres du Sitemap XML
 */
class SmartSEO_AI_Sitemap_Settings_Saver {

    /**
     * Sauvegarde les paramètres du sitemap
     *
     * @param array $input Paramètres à sauvegarder.
     * @return array Paramètres sauvegardés.
     */
    public function save( $input ) {
        // Récupérer les anciens paramètres
        $old_options = get_option( 'smartseo_ai_sitemap_options', array() );
        
        // Vérifier si l'activation a changé
        $sitemap_enabled_changed = isset( $old_options['enabled'] ) && $old_options['enabled'] !== $input['enabled'];
        
        // Sauvegarder les paramètres
        $saved = $input;
        
        // Si l'activation a changé, vider le cache de réécriture
        if ( $sitemap_enabled_changed ) {
            $this->schedule_rewrite_flush();
        }
        
        // Si le sitemap est activé, générer le sitemap
        if ( 'yes' === $saved['enabled'] ) {
            $this->schedule_sitemap_generation();
        }
        
        // Ajouter un message de succès
        add_settings_error(
            'smartseo_ai_settings',
            'smartseo_ai_sitemap_settings_saved',
            __( 'Paramètres du sitemap sauvegardés avec succès.', 'smartseo-ai' ),
            'success'
        );
        
        return $saved;
    }
    
    /**
     * Planifie le vidage du cache de réécriture
     */
    private function schedule_rewrite_flush() {
        // Planifier le vidage du cache de réécriture
        add_action( 'shutdown', 'flush_rewrite_rules' );
    }
    
    /**
     * Planifie la génération du sitemap
     */
    private function schedule_sitemap_generation() {
        // Planifier la génération du sitemap
        if ( ! wp_next_scheduled( 'smartseo_ai_generate_sitemap' ) ) {
            wp_schedule_event( time(), 'daily', 'smartseo_ai_generate_sitemap' );
        }
    }
}
